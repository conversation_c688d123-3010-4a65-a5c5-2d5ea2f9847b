{"ast": null, "code": "// Utility functions for managing sub-topics across components\n\n/**\n * Get all sub-topics from localStorage\n * @returns {Array} Array of sub-topic objects\n */\nexport const getAllSubTopics = () => {\n  try {\n    const savedSubTopics = localStorage.getItem('adminSubTopics');\n    if (savedSubTopics) {\n      return JSON.parse(savedSubTopics);\n    }\n    return [];\n  } catch (err) {\n    console.error(\"Error loading sub-topics from localStorage:\", err);\n    return [];\n  }\n};\n\n/**\n * Get only active sub-topics names for use in forms\n * @returns {Array} Array of active sub-topic names\n */\nexport const getActiveSubTopicNames = () => {\n  try {\n    const allSubTopics = getAllSubTopics();\n    return allSubTopics.filter(topic => topic.isActive).map(topic => topic.name);\n  } catch (err) {\n    console.error(\"Error getting active sub-topics:\", err);\n    return [];\n  }\n};\n\n/**\n * Save sub-topics to localStorage\n * @param {Array} subTopics - Array of sub-topic objects\n */\nexport const saveSubTopics = subTopics => {\n  try {\n    localStorage.setItem('adminSubTopics', JSON.stringify(subTopics));\n    // Trigger custom event to notify other components\n    window.dispatchEvent(new CustomEvent('subTopicsUpdated', {\n      detail: {\n        subTopics\n      }\n    }));\n  } catch (err) {\n    console.error(\"Error saving sub-topics to localStorage:\", err);\n  }\n};\n\n/**\n * Get default sub-topics list\n * @returns {Array} Array of default sub-topic names\n */\nexport const getDefaultSubTopics = () => {\n  return ['IT-software/DB/QA/WEB/GRAPHICS/GIS', 'IT-SOFTWARE/NETWORKS/SYSTEMS', 'ACCOUNTING/AUDITING/FINANCE', 'BANKING & FINANCE/INSURANCE', 'SALES/MARKETING/MERCHANDISING', 'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS', 'LOGISTICS', 'ENG-MECH/AUTO/ELE', 'MANUFACTURING', 'MEDIA/ADVERT/COMMUNICATION', 'SECURITY', 'EDUCATION', 'SUPERVISION', 'APPAREL/CLOTHING', 'TICKETING/AIRLINE', 'R&D/SCIENCE/RESEARCH', 'AGRICULTURE/ENVIRONMENT'];\n};\n\n/**\n * Initialize sub-topics with default values if none exist\n */\nexport const initializeSubTopics = () => {\n  const existingSubTopics = getAllSubTopics();\n  if (existingSubTopics.length === 0) {\n    const defaultSubTopics = getDefaultSubTopics();\n    const initializedSubTopics = defaultSubTopics.map((topic, index) => ({\n      id: index + 1,\n      name: topic,\n      description: `Description for ${topic}`,\n      isActive: true,\n      createdAt: new Date().toISOString(),\n      usageCount: 0\n    }));\n    saveSubTopics(initializedSubTopics);\n    return initializedSubTopics;\n  }\n  return existingSubTopics;\n};", "map": {"version": 3, "names": ["getAllSubTopics", "savedSubTopics", "localStorage", "getItem", "JSON", "parse", "err", "console", "error", "getActiveSubTopicNames", "allSubTopics", "filter", "topic", "isActive", "map", "name", "saveSubTopics", "subTopics", "setItem", "stringify", "window", "dispatchEvent", "CustomEvent", "detail", "getDefaultSubTopics", "initializeSubTopics", "existingSubTopics", "length", "defaultSubTopics", "initializedSubTopics", "index", "id", "description", "createdAt", "Date", "toISOString", "usageCount"], "sources": ["D:/Thirmaa Office/job_page/client/src/utils/subTopicsUtils.js"], "sourcesContent": ["// Utility functions for managing sub-topics across components\n\n/**\n * Get all sub-topics from localStorage\n * @returns {Array} Array of sub-topic objects\n */\nexport const getAllSubTopics = () => {\n  try {\n    const savedSubTopics = localStorage.getItem('adminSubTopics');\n    if (savedSubTopics) {\n      return JSON.parse(savedSubTopics);\n    }\n    return [];\n  } catch (err) {\n    console.error(\"Error loading sub-topics from localStorage:\", err);\n    return [];\n  }\n};\n\n/**\n * Get only active sub-topics names for use in forms\n * @returns {Array} Array of active sub-topic names\n */\nexport const getActiveSubTopicNames = () => {\n  try {\n    const allSubTopics = getAllSubTopics();\n    return allSubTopics\n      .filter(topic => topic.isActive)\n      .map(topic => topic.name);\n  } catch (err) {\n    console.error(\"Error getting active sub-topics:\", err);\n    return [];\n  }\n};\n\n/**\n * Save sub-topics to localStorage\n * @param {Array} subTopics - Array of sub-topic objects\n */\nexport const saveSubTopics = (subTopics) => {\n  try {\n    localStorage.setItem('adminSubTopics', JSON.stringify(subTopics));\n    // Trigger custom event to notify other components\n    window.dispatchEvent(new CustomEvent('subTopicsUpdated', { \n      detail: { subTopics } \n    }));\n  } catch (err) {\n    console.error(\"Error saving sub-topics to localStorage:\", err);\n  }\n};\n\n/**\n * Get default sub-topics list\n * @returns {Array} Array of default sub-topic names\n */\nexport const getDefaultSubTopics = () => {\n  return [\n    'IT-software/DB/QA/WEB/GRAPHICS/GIS',\n    'IT-SOFTWARE/NETWORKS/SYSTEMS',\n    'ACCOUNTING/AUDITING/FINANCE',\n    'BANKING & FINANCE/INSURANCE',\n    'SALES/MARKETING/MERCHANDISING',\n    'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS',\n    'LOGISTICS',\n    'ENG-MECH/AUTO/ELE',\n    'MANUFACTURING',\n    'MEDIA/ADVERT/COMMUNICATION',\n    'SECURITY',\n    'EDUCATION',\n    'SUPERVISION',\n    'APPAREL/CLOTHING',\n    'TICKETING/AIRLINE',\n    'R&D/SCIENCE/RESEARCH',\n    'AGRICULTURE/ENVIRONMENT'\n  ];\n};\n\n/**\n * Initialize sub-topics with default values if none exist\n */\nexport const initializeSubTopics = () => {\n  const existingSubTopics = getAllSubTopics();\n  \n  if (existingSubTopics.length === 0) {\n    const defaultSubTopics = getDefaultSubTopics();\n    const initializedSubTopics = defaultSubTopics.map((topic, index) => ({\n      id: index + 1,\n      name: topic,\n      description: `Description for ${topic}`,\n      isActive: true,\n      createdAt: new Date().toISOString(),\n      usageCount: 0\n    }));\n    \n    saveSubTopics(initializedSubTopics);\n    return initializedSubTopics;\n  }\n  \n  return existingSubTopics;\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,eAAe,GAAGA,CAAA,KAAM;EACnC,IAAI;IACF,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,IAAIF,cAAc,EAAE;MAClB,OAAOG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;IACnC;IACA,OAAO,EAAE;EACX,CAAC,CAAC,OAAOK,GAAG,EAAE;IACZC,OAAO,CAACC,KAAK,CAAC,6CAA6C,EAAEF,GAAG,CAAC;IACjE,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;EAC1C,IAAI;IACF,MAAMC,YAAY,GAAGV,eAAe,CAAC,CAAC;IACtC,OAAOU,YAAY,CAChBC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAC/BC,GAAG,CAACF,KAAK,IAAIA,KAAK,CAACG,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAOT,GAAG,EAAE;IACZC,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEF,GAAG,CAAC;IACtD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMU,aAAa,GAAIC,SAAS,IAAK;EAC1C,IAAI;IACFf,YAAY,CAACgB,OAAO,CAAC,gBAAgB,EAAEd,IAAI,CAACe,SAAS,CAACF,SAAS,CAAC,CAAC;IACjE;IACAG,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,kBAAkB,EAAE;MACvDC,MAAM,EAAE;QAAEN;MAAU;IACtB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,OAAOX,GAAG,EAAE;IACZC,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEF,GAAG,CAAC;EAChE;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;EACvC,OAAO,CACL,oCAAoC,EACpC,8BAA8B,EAC9B,6BAA6B,EAC7B,6BAA6B,EAC7B,+BAA+B,EAC/B,8CAA8C,EAC9C,WAAW,EACX,mBAAmB,EACnB,eAAe,EACf,4BAA4B,EAC5B,UAAU,EACV,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,yBAAyB,CAC1B;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EACvC,MAAMC,iBAAiB,GAAG1B,eAAe,CAAC,CAAC;EAE3C,IAAI0B,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;IAClC,MAAMC,gBAAgB,GAAGJ,mBAAmB,CAAC,CAAC;IAC9C,MAAMK,oBAAoB,GAAGD,gBAAgB,CAACd,GAAG,CAAC,CAACF,KAAK,EAAEkB,KAAK,MAAM;MACnEC,EAAE,EAAED,KAAK,GAAG,CAAC;MACbf,IAAI,EAAEH,KAAK;MACXoB,WAAW,EAAE,mBAAmBpB,KAAK,EAAE;MACvCC,QAAQ,EAAE,IAAI;MACdoB,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,UAAU,EAAE;IACd,CAAC,CAAC,CAAC;IAEHpB,aAAa,CAACa,oBAAoB,CAAC;IACnC,OAAOA,oBAAoB;EAC7B;EAEA,OAAOH,iBAAiB;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}