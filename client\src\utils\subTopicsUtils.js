// Utility functions for managing sub-topics across components

/**
 * Get all sub-topics from localStorage
 * @returns {Array} Array of sub-topic objects
 */
export const getAllSubTopics = () => {
  try {
    const savedSubTopics = localStorage.getItem('adminSubTopics');
    if (savedSubTopics) {
      return JSON.parse(savedSubTopics);
    }
    return [];
  } catch (err) {
    console.error("Error loading sub-topics from localStorage:", err);
    return [];
  }
};

/**
 * Get only active sub-topics names for use in forms
 * @returns {Array} Array of active sub-topic names
 */
export const getActiveSubTopicNames = () => {
  try {
    const allSubTopics = getAllSubTopics();
    return allSubTopics
      .filter(topic => topic.isActive)
      .map(topic => topic.name);
  } catch (err) {
    console.error("Error getting active sub-topics:", err);
    return [];
  }
};

/**
 * Save sub-topics to localStorage
 * @param {Array} subTopics - Array of sub-topic objects
 */
export const saveSubTopics = (subTopics) => {
  try {
    localStorage.setItem('adminSubTopics', JSON.stringify(subTopics));
    // Trigger custom event to notify other components
    window.dispatchEvent(new CustomEvent('subTopicsUpdated', { 
      detail: { subTopics } 
    }));
  } catch (err) {
    console.error("Error saving sub-topics to localStorage:", err);
  }
};

/**
 * Get default sub-topics list
 * @returns {Array} Array of default sub-topic names
 */
export const getDefaultSubTopics = () => {
  return [
    'IT-software/DB/QA/WEB/GRAPHICS/GIS',
    'IT-SOFTWARE/NETWORKS/SYSTEMS',
    'ACCOUNTING/AUDITING/FINANCE',
    'BANKING & FINANCE/INSURANCE',
    'SALES/MARKETING/MERCHANDISING',
    'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS',
    'LOGISTICS',
    'ENG-MECH/AUTO/ELE',
    'MANUFACTURING',
    'MEDIA/ADVERT/COMMUNICATION',
    'SECURITY',
    'EDUCATION',
    'SUPERVISION',
    'APPAREL/CLOTHING',
    'TICKETING/AIRLINE',
    'R&D/SCIENCE/RESEARCH',
    'AGRICULTURE/ENVIRONMENT'
  ];
};

/**
 * Initialize sub-topics with default values if none exist
 */
export const initializeSubTopics = () => {
  const existingSubTopics = getAllSubTopics();
  
  if (existingSubTopics.length === 0) {
    const defaultSubTopics = getDefaultSubTopics();
    const initializedSubTopics = defaultSubTopics.map((topic, index) => ({
      id: index + 1,
      name: topic,
      description: `Description for ${topic}`,
      isActive: true,
      createdAt: new Date().toISOString(),
      usageCount: 0
    }));
    
    saveSubTopics(initializedSubTopics);
    return initializedSubTopics;
  }
  
  return existingSubTopics;
};
