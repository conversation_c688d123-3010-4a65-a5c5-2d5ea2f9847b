import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import '../css/HomePage.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt, faClock, faEye, faChevronLeft, faChevronRight, faFilter, faTimes, faBriefcase, faBookmark, faShare, faBars, faSync } from '@fortawesome/free-solid-svg-icons';
import { FaSpinner } from 'react-icons/fa';
import ApiService from '../services/apiService';
import { getActiveSubTopicNames, getDefaultSubTopics } from '../utils/subTopicsUtils';
import NewsLetter from './NewsLetter';
import Banner from './Banner';
import SearchArea from './SearchArea';
import FeaturedSection from './FeaturedSection';
import SocialMediaFeeds from './SocialMediaFeeds';
import FeaturedCompanies from './FeaturedCompanies';
import UrgentJobCard from './UrgentJobCard';
import PageHelmet from './PageHelmet';

const HomePage = () => {
  const [jobs, setJobs] = useState([]);
  const [hotJobs, setHotJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSubTopics, setSelectedSubTopics] = useState([]);
  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);
  const [availableSubTopics, setAvailableSubTopics] = useState([]);
  const jobsPerPage = 3;

  // Load available sub-topics from localStorage
  const loadSubTopicsFromStorage = () => {
    try {
      const activeSubTopicNames = getActiveSubTopicNames();
      if (activeSubTopicNames.length > 0) {
        setAvailableSubTopics(activeSubTopicNames);
      } else {
        // Use default sub-topics if no saved data
        const defaultSubTopics = getDefaultSubTopics();
        setAvailableSubTopics(defaultSubTopics);
      }
    } catch (err) {
      console.error("Error loading sub-topics from localStorage:", err);
      // Fallback to default sub-topics
      const defaultSubTopics = getDefaultSubTopics();
      setAvailableSubTopics(defaultSubTopics);
    }
  };

  // Function to shorten long topic names
  const shortenTopicName = (topic, maxLength = 30) => {
    if (topic.length <= maxLength) {
      return topic;
    }
    return topic.substring(0, maxLength) + '...';
  };

  // Initialize available sub-topics
  useEffect(() => {
    loadSubTopicsFromStorage();
  }, []);

  // Listen for sub-topics updates from SubTopicsAdmin
  useEffect(() => {
    const handleSubTopicsUpdate = (event) => {
      console.log("Sub-topics updated in HomePage, refreshing available options...");
      loadSubTopicsFromStorage();
    };

    // Add event listener for sub-topics updates
    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);
    };
  }, []);

  // Fetch jobs from API
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        // Add a timestamp parameter to prevent caching
        const response = await ApiService.jobs.getAll({ _t: new Date().getTime() });
        
        // Transform backend data to match the format used in frontend
        const formattedJobs = response.data.map(job => ({
          id: job.job_id,
          position: job.job_title,
          company: job.company_name,
          location: job.main_topics,
          workTime: job.job_type,
          // Pass min and max salary for display
          min_salary: job.min_salary,
          max_salary: job.max_salary,
          salary: formatSalary(job.min_salary, job.max_salary),
          views: job.view_count || 0, // Use actual view count instead of random number
          postedTime: formatPostedDate(job.start_date),
          datePosted: job.start_date, // Store the original date string for debugging
          // Ensure we have valid image paths or fallbacks
          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',
          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',
          hot: job.hot || false, // Default if not in database
          status: job.status || 'Active',
          description: job.job_description || 'No description provided',
          subTopics: (() => {
            try {
              if (!job.sub_topics) return [];
              if (typeof job.sub_topics === 'string') {
                if (job.sub_topics.trim().startsWith('[')) {
                  return JSON.parse(job.sub_topics);
                } else {
                  return [job.sub_topics];
                }
              } else if (Array.isArray(job.sub_topics)) {
                return job.sub_topics;
              }
              return [];
            } catch (err) {
              console.error("Error parsing sub_topics:", err);
              return [];
            }
          })(),
          // Add a Date object for sorting purposes
          dateObj: new Date(job.start_date)
        }));
        
        // Sort jobs with newest first (by date posted)
        const sortedJobs = formattedJobs.sort((a, b) => b.dateObj - a.dateObj);
        
        setJobs(sortedJobs);
        setError(null);
      } catch (err) {
        console.error("Error fetching jobs:", err);
        setError("Failed to load jobs from server");
      } finally {
        setLoading(false);
      }
    };

    const fetchHotJobs = async () => {
      try {
        const response = await ApiService.jobs.getHotJobs();
        
        // Transform hot jobs data to match the format used in frontend
        const formattedHotJobs = response.data.map(job => ({
          id: job.job_id,
          position: job.job_title,
          company: job.company_name,
          location: job.main_topics,
          workTime: job.job_type,
          min_salary: job.min_salary,
          max_salary: job.max_salary,
          salary: formatSalary(job.min_salary, job.max_salary),
          views: job.view_count || 0,
          postedTime: formatPostedDate(job.start_date),
          datePosted: job.start_date,
          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',
          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',
          hot: true,
          status: job.status || 'Active',
          description: job.job_description || 'No description provided',
          subTopics: (() => {
            try {
              if (!job.sub_topics) return [];
              if (typeof job.sub_topics === 'string') {
                if (job.sub_topics.trim().startsWith('[')) {
                  return JSON.parse(job.sub_topics);
                } else {
                  return [job.sub_topics];
                }
              } else if (Array.isArray(job.sub_topics)) {
                return job.sub_topics;
              }
              return [];
            } catch (err) {
              console.error("Error parsing sub_topics:", err);
              return [];
            }
          })(),
          dateObj: new Date(job.start_date)
        }));
        
        setHotJobs(formattedHotJobs);
      } catch (err) {
        console.error("Error fetching hot jobs:", err);
        // Don't set error for hot jobs failure, just log it
      }
    };

    fetchJobs();
    fetchHotJobs();
    // Set default tab to "All Jobs"
    setActiveTab('all');
    
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Helper function to format min/max salary as "Rs. min - Rs. max"
  const formatSalary = (min, max) => {
    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) &&
        (max === null || max === undefined || max === '' || isNaN(Number(max)))) {
      return 'Negotiable';
    }
    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {
      return 'Negotiable';
    }
    if (min && max) {
      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;
    }
    if (min) {
      return `Rs. ${Number(min).toLocaleString()}`;
    }
    if (max) {
      return `Rs. ${Number(max).toLocaleString()}`;
    }
    return 'Negotiable';
  };

  // Helper function to format posted date
  const formatPostedDate = (dateString) => {
    if (!dateString) return 'Recently';
    
    try {
      // Parse the date from the server
      const postedDate = new Date(dateString);
      const now = new Date();
      
      // Normalize both dates to midnight in local timezone to compare just the dates
      const postedDateNormalized = new Date(
        postedDate.getFullYear(), 
        postedDate.getMonth(), 
        postedDate.getDate()
      ).getTime();
      
      const todayNormalized = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate()
      ).getTime();
      
      const oneDayMs = 24 * 60 * 60 * 1000; // One day in milliseconds
      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);
      
      // Check if it's today
      if (daysAgo === 0) {
        return 'Today';
      }
      
      // Check if it's yesterday
      if (daysAgo === 1) {
        return 'Yesterday';
      }
      
      // Calculate exact days difference
      if (daysAgo < 7) return `${daysAgo} days ago`;
      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;
      return `${Math.floor(daysAgo / 30)} months ago`;
    } catch (err) {
      console.error("Error formatting date:", err, "for date:", dateString);
      return 'Recently';
    }
  };

  // Handle subtopic selection
  const handleSubTopicChange = (topic) => {
    setSelectedSubTopics(prevSelected => {
      if (prevSelected.includes(topic)) {
        // Remove topic if already selected (uncheck)
        return prevSelected.filter(t => t !== topic);
      } else {
        // Add topic if not already selected (check)
        return [...prevSelected, topic];
      }
    });
    setCurrentPage(1); // Reset to first page when changing filters
  };

  // Filter jobs based on active tab and selected subtopics
  const filteredJobs = jobs
    .filter(job => {
      // Filter by tab
      const matchesTab = activeTab === 'all' || job.location === activeTab;
      
      // Filter by sub topics if any are selected
      const matchesSubTopics = selectedSubTopics.length === 0 || 
        (job.subTopics && job.subTopics.some(topic => selectedSubTopics.includes(topic)));
      
      // Only include active jobs
      const isActive = job.status !== 'Inactive';
      
      return matchesTab && matchesSubTopics && isActive;
    });
  // Filter urgent/hot jobs for the left sidebar - use hotJobs state
  const urgentJobs = hotJobs.slice(0, 2);
  
  // Get paginated jobs - only show 5 per page
  const indexOfLastJob = currentPage * jobsPerPage;
  const indexOfFirstJob = indexOfLastJob - jobsPerPage;
  const currentJobs = filteredJobs
    .filter(job => job.hot !== true) // Don't show hot jobs in the regular listings
    .slice(indexOfFirstJob, indexOfLastJob);
  
  // Calculate total pages
  const totalPages = Math.ceil(
    filteredJobs.filter(job => job.hot !== true).length / jobsPerPage
  );

  // Change page
  const handlePageChange = (pageNumber) => {
    if (pageNumber < 1 || pageNumber > totalPages) return;
    setCurrentPage(pageNumber);
    // Scroll to top of job listings
    window.scrollTo({ top: 400, behavior: 'smooth' });
  };

  // Generate page numbers for pagination
  const pageNumbers = [];
  const displayRange = 5; // How many page numbers to show at once
  
  let startPage = Math.max(1, currentPage - Math.floor(displayRange / 2));
  let endPage = Math.min(totalPages, startPage + displayRange - 1);
  
  if (endPage - startPage + 1 < displayRange) {
    startPage = Math.max(1, endPage - displayRange + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  // Change active tab
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  // Clear all selected subtopics
  const clearSubTopicFilters = () => {
    setSelectedSubTopics([]);
  };

  // Toggle filter sidebar
  const toggleFilterSidebar = () => {
    setIsFilterSidebarOpen(!isFilterSidebarOpen);
  };

  // Function to increment view count
  const incrementViewCount = async (jobId) => {
    try {
      await ApiService.jobs.recordView(jobId);
    } catch (err) {
      console.error("Error incrementing view count:", err);
    }
  };

  const JobLoadingSkeleton = ({ count = 3 }) => {
  return (
    <div className="jobs-loading-container">
      {[...Array(count)].map((_, i) => (
        <div key={i} className="job-card-skeleton">
          <div className="skeleton-thumbnail"></div>
          <div className="skeleton-content">
            <div className="skeleton-line title"></div>
            <div className="skeleton-line company"></div>
            <div className="skeleton-meta">
              <div className="skeleton-tag"></div>
              <div className="skeleton-tag"></div>
              <div className="skeleton-tag"></div>
            </div>
            <div className="skeleton-line description"></div>
            <div className="skeleton-line description short"></div>
          </div>
        </div>
      ))}
    </div>
  );
};

  return (
    <div className="homepage-container">
      <PageHelmet 
        title="Your Path to the Perfect Job" 
        description="Find your dream job across all sectors - Government, Private, Foreign, and Internships."
      />
      <div className="content-spacing"></div>
      
      {/* Search Area */}
      <section className="homepage-search-area">
        <SearchArea />
      </section>
      
      {/* Banner */}
      <section className="homepage-top-banner">
        <Banner />
      </section>
      
      {/* Main Content with constrained width */}
      <div className="home-content-wrapper">
        {/* Header with New Jobs Title */}
        <div className="home-header" >
          <div className="header-left">
            <h1 className="main-title">New Jobs</h1>
            <button 
              className="filter-toggle-btn" 
              onClick={toggleFilterSidebar}
              aria-label="Toggle filters"
            >
              <FontAwesomeIcon icon={faFilter} />
              Filters
            </button>
          </div>
          <div className="header-tabs">
            <span 
              className={`tab ${activeTab === 'all' ? 'active all-active' : ''}`} 
              key="all"
              onClick={() => handleTabChange('all')}
            >
              All Jobs
            </span>
            <span 
              className={`tab ${activeTab === 'Government Jobs' ? 'active gov-active' : ''}`} 
              key="gov"
              onClick={() => handleTabChange('Government Jobs')}
            >
              Government Jobs
            </span>
            <span 
              className={`tab ${activeTab === 'Private Jobs' ? 'active private-active' : ''}`} 
              key="private"
              onClick={() => handleTabChange('Private Jobs')}
            >
              Private Jobs
            </span>
            <span 
              className={`tab ${activeTab === 'Foreign Jobs' ? 'active foreign-active' : ''}`} 
              key="foreign"
              onClick={() => handleTabChange('Foreign Jobs')}
            >
              Foreign Jobs
            </span>
            <span 
              className={`tab ${activeTab === 'Internships' ? 'active internships-active' : ''}`} 
              key="internships"
              onClick={() => handleTabChange('Internships')}
            >
              Internships
            </span>
          </div>
        </div>
        
        {/* Main 3-column Layout */}
        <div className="home-layout">          {/* Left Column - Urgent Jobs */}
          <div className="left-column">
            {loading ? (
              <div className="loading-overlay">
                <FaSpinner className="spinner" />
                <p>Loading urgent jobs...</p>
              </div>
            ) : hotJobs.length > 0 ? (
              hotJobs.slice(0, 2).map(job => (
                <UrgentJobCard 
                  key={`urgent-${job.id}`}
                  job={job}
                  onViewIncrement={incrementViewCount}
                />
              ))
            ) : (
              <div className="no-urgent-jobs">
                <p>No urgent jobs available right now.</p>
              </div>
            )}
            
            {/* Recruiting Box */}
            <div className="recruiting-box">
              <h3>Recruiting?</h3>
              <p>Get your job postings seen by thousands of job seekers.</p>
              <img src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Recruiting" className="recruiting-image" />
              <a href="/contact">
                <button className="post-job-btn">Post a Job</button>
              </a>
            </div>
          </div>
          
          {/* Middle Column - Regular Jobs */}
          <div className="middle-column">
            {error && (
              <div className="error-notification" style={{ 
                color: '#e74c3c', 
                backgroundColor: '#fdf2f2', 
                border: '1px solid #f5c6cb', 
                borderRadius: '8px', 
                padding: '12px 16px', 
                margin: '16px auto', 
                textAlign: 'center', 
                fontSize: '14px', 
                maxWidth: '400px',
                boxShadow: '0 2px 4px rgba(231, 76, 60, 0.1)'
              }}>
                {error}
              </div>
            )}
            {loading ? (
              <div className="loading-overlay">
                <FaSpinner className="spinner" />
                <p>Loading available positions...</p>
              </div>
            ) : currentJobs.length > 0 ? (
              currentJobs.map(job => (
                <Link
                  key={`regular-${job.id}`}
                  to={`/job/${job.id}`}
                  className={`job-card-redesigned job-type-${(job.workTime || '').toLowerCase().replace(/\s+/g, '-')}`}
                  style={{ background: "#fff", textDecoration: "none", color: "inherit", position: "relative" }}
                  onClick={() => incrementViewCount(job.id)}
                >
                  {job.hot && (
                    <span className="urgent-tag jobcard-urgent-tag">🔥 URGENT</span>
                  )}
                  <div className="job-card-header">
                    <div className="company-info-header">
                      {/* Removing the date from here */}
                    </div>
                  </div>
                  <div className="job-card-content">
                    <div className="job-image-container">
                      <img 
                        src={job.image} 
                        alt={job.position} 
                        className="job-thumbnail" 
                        onError={(e) => {
                          e.target.onerror = null; 
                          e.target.src = 'https://via.placeholder.com/300x300?text=Job+Image';
                        }}
                      />
                    </div>
                    <div className="job-main-content">
                      <h3 className="job-title-redesigned">
                        {job.position} 
                      </h3>
                  <div className="company-info-urgent">
                    <div className="company-logo-urgent">
                      <img 
                        src={job.logo} 
                        alt={job.company}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = 'https://via.placeholder.com/30';
                        }}
                      />
                    </div>
                    <div className="company-details-urgent">
                      <h3 className="company-name-urgent">{job.company}</h3>
                    </div>
                  </div>
                      <div className="job-meta-info">
                        <span className="job-tag location">
                          <FontAwesomeIcon icon={faBriefcase} /> {job.location}
                        </span>
                        <span className="job-tag work-type">
                          <FontAwesomeIcon icon={faClock} /> {job.workTime}
                        </span>
                        <span className="job-tag salary">
                          {formatSalary(job.min_salary, job.max_salary)}
                        </span>
                      </div>
                      <div className="job-description-redesigned">
                        <p>{job.description ? job.description.substring(0, 140) + '...' : 'No description available.'}</p>
                      </div>
                    </div>
                  </div>
                  <div className="job-card-footer">
                    <span className="posting-date"> {job.postedTime}</span>
                    <div className="apply-section" style={{ marginLeft: "auto" }}>
                      <button
                        className="apply-btn-redesigned"
                      >
                        Apply Now
                      </button>
                      {/* <button
                        className="job-action-btn share-btn"
                        onClick={e => {
                          e.preventDefault();
                          // Optionally implement share logic here
                        }}
                      >
                        <FontAwesomeIcon icon={faShare} />
                      </button> */}
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="no-jobs-redesigned">
                <div className="no-jobs-icon">
                  <FontAwesomeIcon icon={faBriefcase} />
                </div>
                <h3>No Matching Jobs</h3>
                <p>There are no jobs available in this category right now.</p>
                <p>Try adjusting your filters or check back later.</p>
                {!error && !loading && jobs.length === 0 && (
                  <div style={{ color: 'gray', marginTop: '16px' }}>
                    No jobs found. Please check your API server or try again later.
                  </div>
                )}
              </div>
            )}
            
            {/* Pagination */}
            <div className="pagination">
              <button 
                className="page-arrow" 
                key="prev"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <FontAwesomeIcon icon={faChevronLeft} />
              </button>
              
              {pageNumbers.map(number => (
                <button
                  key={`page-${number}`}
                  className={`page-number ${currentPage === number ? 'active' : ''}`}
                  onClick={() => handlePageChange(number)}
                >
                  {number}
                </button>
              ))}
              
              <button 
                className="page-arrow" 
                key="next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages === 0}
              >
                <FontAwesomeIcon icon={faChevronRight} />
              </button>
              
              {totalPages > displayRange && (
                <span className="page-info" key="more">
                  of {totalPages} pages
                </span>
              )}
            </div>
          </div>
          
          {/* Right Column - Subtopics Filter */}
          <div className="right-column">
            <div className="subtopics-box">
              <div className="subtopics-header">
                <h3>Filter by Specialization</h3>
                <div className="filter-actions">
                  <button
                    className="refresh-subtopics-btn"
                    onClick={loadSubTopicsFromStorage}
                    title="Refresh sub-topics list"
                    style={{
                      background: 'none',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      padding: '4px 8px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      color: '#666',
                      marginRight: '8px'
                    }}
                  >
                    <FontAwesomeIcon icon={faSync} />
                  </button>
                  {selectedSubTopics.length > 0 && (
                    <button className="clear-filters-btn" onClick={clearSubTopicFilters}>
                      Clear all <FontAwesomeIcon icon={faTimes} />
                    </button>
                  )}
                </div>
              </div>
              <div className="subtopics-list">
                {availableSubTopics.map((topic, index) => (
                  <div
                    key={`subtopic-${index}`}
                    className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}
                  >
                    <input
                      type="checkbox"
                      id={`subtopic-${index}`}
                      checked={selectedSubTopics.includes(topic)}
                      onChange={() => handleSubTopicChange(topic)}
                    />
                    <label htmlFor={`subtopic-${index}`} title={topic}>
                      {shortenTopicName(topic)}
                    </label>
                  </div>
                ))}
              </div>
              <div className="selected-filters">
                {selectedSubTopics.length > 0 ? (
                  <div>
                    <div className="filters-label">
                      <FontAwesomeIcon icon={faFilter} /> Applied filters:
                    </div>
                    <div className="selected-topics-list">
                      {selectedSubTopics.map((topic, idx) => (
                        <span key={`selected-${idx}`} className="selected-topic-tag" title={topic}>
                          {shortenTopicName(topic, 20)}
                          <button
                            className="remove-topic"
                            onClick={() => handleSubTopicChange(topic)}
                          >
                            <FontAwesomeIcon icon={faTimes} />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="no-filters">No filters applied</div>
                )}
              </div>
              {availableSubTopics.length === 0 && (
                <div className="no-subtopics-message" style={{
                  fontSize: '12px',
                  color: '#666',
                  fontStyle: 'italic',
                  textAlign: 'center',
                  padding: '10px',
                  border: '1px dashed #ddd',
                  borderRadius: '4px',
                  marginTop: '10px'
                }}>
                  No sub-topics available. Click refresh to load latest options.
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Featured Section */}
        <FeaturedSection />
      </div>

      {/* Filter Sidebar Overlay */}
      {isFilterSidebarOpen && (
        <div className="filter-sidebar-overlay" onClick={toggleFilterSidebar}></div>
      )}

      {/* Sliding Filter Sidebar */}
      <div className={`filter-sidebar ${isFilterSidebarOpen ? 'open' : ''}`}>
        <div className="filter-sidebar-header">
          <h3><FontAwesomeIcon icon={faFilter} /> Filters</h3>
          <button className="close-sidebar-btn" onClick={toggleFilterSidebar}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        
        <div className="filter-sidebar-content">
          <div className="subtopics-box">
            <div className="subtopics-header">
              <h3>Filter by Specialization</h3>
              <div className="filter-actions">
                <button
                  className="refresh-subtopics-btn"
                  onClick={loadSubTopicsFromStorage}
                  title="Refresh sub-topics list"
                  style={{
                    background: 'none',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    padding: '4px 8px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    color: '#666',
                    marginRight: '8px'
                  }}
                >
                  <FontAwesomeIcon icon={faSync} />
                </button>
                {selectedSubTopics.length > 0 && (
                  <button className="clear-filters-btn" onClick={clearSubTopicFilters}>
                    Clear all <FontAwesomeIcon icon={faTimes} />
                  </button>
                )}
              </div>
            </div>
            <div className="subtopics-list">
              {availableSubTopics.map((topic, index) => (
                <div
                  key={`sidebar-subtopic-${index}`}
                  className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}
                >
                  <input
                    type="checkbox"
                    id={`sidebar-subtopic-${index}`}
                    checked={selectedSubTopics.includes(topic)}
                    onChange={() => handleSubTopicChange(topic)}
                  />
                  <label htmlFor={`sidebar-subtopic-${index}`} title={topic}>
                    {shortenTopicName(topic)}
                  </label>
                </div>
              ))}
            </div>
            <div className="selected-filters">
              {selectedSubTopics.length > 0 ? (
                <div>
                  <div className="filters-label">
                    <FontAwesomeIcon icon={faFilter} /> Applied filters:
                  </div>
                  <div className="selected-topics-list">
                    {selectedSubTopics.map((topic, idx) => (
                      <span key={`sidebar-selected-${idx}`} className="selected-topic-tag" title={topic}>
                        {shortenTopicName(topic, 20)}
                        <button 
                          className="remove-topic" 
                          onClick={() => handleSubTopicChange(topic)}
                        >
                          <FontAwesomeIcon icon={faTimes} />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="no-filters">No filters applied</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Featured Companies */}
      {/* <FeaturedCompanies /> */}
      
      {/* Newsletter */}
      {/* <section className="homepage-newsletter">
        <NewsLetter />
      </section> */}
      
      {/* Banner */}
      <section className="homepage-banner">
        <Banner />
      </section>
      <section className="social-media-feeds">
        <SocialMediaFeeds />
      </section>
    </div>
  );
};

export default HomePage;
