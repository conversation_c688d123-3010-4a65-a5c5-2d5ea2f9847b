{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\HomePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../css/HomePage.css';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faMapMarkerAlt, faClock, faEye, faChevronLeft, faChevronRight, faFilter, faTimes, faBriefcase, faBookmark, faShare, faBars } from '@fortawesome/free-solid-svg-icons';\nimport { FaSpinner } from 'react-icons/fa';\nimport ApiService from '../services/apiService';\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../utils/subTopicsUtils';\nimport NewsLetter from './NewsLetter';\nimport Banner from './Banner';\nimport SearchArea from './SearchArea';\nimport FeaturedSection from './FeaturedSection';\nimport SocialMediaFeeds from './SocialMediaFeeds';\nimport FeaturedCompanies from './FeaturedCompanies';\nimport UrgentJobCard from './UrgentJobCard';\nimport PageHelmet from './PageHelmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [jobs, setJobs] = useState([]);\n  const [hotJobs, setHotJobs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [selectedSubTopics, setSelectedSubTopics] = useState([]);\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\n  const jobsPerPage = 8;\n\n  // Load available sub-topics from localStorage\n  const loadSubTopicsFromStorage = () => {\n    try {\n      const activeSubTopicNames = getActiveSubTopicNames();\n      if (activeSubTopicNames.length > 0) {\n        setAvailableSubTopics(activeSubTopicNames);\n      } else {\n        // Use default sub-topics if no saved data\n        const defaultSubTopics = getDefaultSubTopics();\n        setAvailableSubTopics(defaultSubTopics);\n      }\n    } catch (err) {\n      console.error(\"Error loading sub-topics from localStorage:\", err);\n      // Fallback to default sub-topics\n      const defaultSubTopics = getDefaultSubTopics();\n      setAvailableSubTopics(defaultSubTopics);\n    }\n  };\n\n  // Function to shorten long topic names\n  const shortenTopicName = (topic, maxLength = 30) => {\n    if (topic.length <= maxLength) {\n      return topic;\n    }\n    return topic.substring(0, maxLength) + '...';\n  };\n\n  // Initialize available sub-topics\n  useEffect(() => {\n    loadSubTopicsFromStorage();\n  }, []);\n\n  // Listen for sub-topics updates from SubTopicsAdmin\n  useEffect(() => {\n    const handleSubTopicsUpdate = event => {\n      console.log(\"Sub-topics updated in HomePage, refreshing available options...\");\n      loadSubTopicsFromStorage();\n    };\n\n    // Add event listener for sub-topics updates\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n    };\n  }, []);\n\n  // Fetch jobs from API\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        // Add a timestamp parameter to prevent caching\n        const response = await ApiService.jobs.getAll({\n          _t: new Date().getTime()\n        });\n\n        // Transform backend data to match the format used in frontend\n        const formattedJobs = response.data.map(job => ({\n          id: job.job_id,\n          position: job.job_title,\n          company: job.company_name,\n          location: job.main_topics,\n          workTime: job.job_type,\n          // Pass min and max salary for display\n          min_salary: job.min_salary,\n          max_salary: job.max_salary,\n          salary: formatSalary(job.min_salary, job.max_salary),\n          views: job.view_count || 0,\n          // Use actual view count instead of random number\n          postedTime: formatPostedDate(job.start_date),\n          datePosted: job.start_date,\n          // Store the original date string for debugging\n          // Ensure we have valid image paths or fallbacks\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          hot: job.hot || false,\n          // Default if not in database\n          status: job.status || 'Active',\n          description: job.job_description || 'No description provided',\n          subTopics: (() => {\n            try {\n              if (!job.sub_topics) return [];\n              if (typeof job.sub_topics === 'string') {\n                if (job.sub_topics.trim().startsWith('[')) {\n                  return JSON.parse(job.sub_topics);\n                } else {\n                  return [job.sub_topics];\n                }\n              } else if (Array.isArray(job.sub_topics)) {\n                return job.sub_topics;\n              }\n              return [];\n            } catch (err) {\n              console.error(\"Error parsing sub_topics:\", err);\n              return [];\n            }\n          })(),\n          // Add a Date object for sorting purposes - prioritize created_at, then start_date\n          dateObj: new Date(job.created_at || job.start_date || job.posted_date || new Date()),\n          jobId: job.job_id // Store job ID for secondary sorting\n        }));\n\n        // Sort jobs by creation date descending (newest first), then by ID descending as fallback\n        const sortedJobs = formattedJobs.sort((a, b) => {\n          // First, try to sort by creation date\n          const dateA = a.dateObj;\n          const dateB = b.dateObj;\n          if (dateB.getTime() !== dateA.getTime()) {\n            return dateB.getTime() - dateA.getTime();\n          }\n\n          // If dates are the same, sort by ID descending (higher ID = newer)\n          return b.jobId - a.jobId;\n        });\n        setJobs(sortedJobs);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching jobs:\", err);\n        setError(\"Failed to load jobs from server\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    const fetchHotJobs = async () => {\n      try {\n        const response = await ApiService.jobs.getHotJobs();\n\n        // Transform hot jobs data to match the format used in frontend\n        const formattedHotJobs = response.data.map(job => ({\n          id: job.job_id,\n          position: job.job_title,\n          company: job.company_name,\n          location: job.main_topics,\n          workTime: job.job_type,\n          min_salary: job.min_salary,\n          max_salary: job.max_salary,\n          salary: formatSalary(job.min_salary, job.max_salary),\n          views: job.view_count || 0,\n          postedTime: formatPostedDate(job.start_date),\n          datePosted: job.start_date,\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          hot: true,\n          status: job.status || 'Active',\n          description: job.job_description || 'No description provided',\n          subTopics: (() => {\n            try {\n              if (!job.sub_topics) return [];\n              if (typeof job.sub_topics === 'string') {\n                if (job.sub_topics.trim().startsWith('[')) {\n                  return JSON.parse(job.sub_topics);\n                } else {\n                  return [job.sub_topics];\n                }\n              } else if (Array.isArray(job.sub_topics)) {\n                return job.sub_topics;\n              }\n              return [];\n            } catch (err) {\n              console.error(\"Error parsing sub_topics:\", err);\n              return [];\n            }\n          })(),\n          dateObj: new Date(job.start_date)\n        }));\n        setHotJobs(formattedHotJobs);\n      } catch (err) {\n        console.error(\"Error fetching hot jobs:\", err);\n        // Don't set error for hot jobs failure, just log it\n      }\n    };\n    fetchJobs();\n    fetchHotJobs();\n    // Set default tab to \"All Jobs\"\n    setActiveTab('all');\n\n    // Scroll to top when component mounts\n    window.scrollTo(0, 0);\n  }, []);\n\n  // Helper function to format min/max salary as \"Rs. min - Rs. max\"\n  const formatSalary = (min, max) => {\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) && (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\n      return 'Negotiable';\n    }\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\n      return 'Negotiable';\n    }\n    if (min && max) {\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\n    }\n    if (min) {\n      return `Rs. ${Number(min).toLocaleString()}`;\n    }\n    if (max) {\n      return `Rs. ${Number(max).toLocaleString()}`;\n    }\n    return 'Negotiable';\n  };\n\n  // Helper function to format posted date\n  const formatPostedDate = dateString => {\n    if (!dateString) return 'Recently';\n    try {\n      // Parse the date from the server\n      const postedDate = new Date(dateString);\n      const now = new Date();\n\n      // Normalize both dates to midnight in local timezone to compare just the dates\n      const postedDateNormalized = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate()).getTime();\n      const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();\n      const oneDayMs = 24 * 60 * 60 * 1000; // One day in milliseconds\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\n\n      // Check if it's today\n      if (daysAgo === 0) {\n        return 'Today';\n      }\n\n      // Check if it's yesterday\n      if (daysAgo === 1) {\n        return 'Yesterday';\n      }\n\n      // Calculate exact days difference\n      if (daysAgo < 7) return `${daysAgo} days ago`;\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\n      return `${Math.floor(daysAgo / 30)} months ago`;\n    } catch (err) {\n      console.error(\"Error formatting date:\", err, \"for date:\", dateString);\n      return 'Recently';\n    }\n  };\n\n  // Handle subtopic selection\n  const handleSubTopicChange = topic => {\n    setSelectedSubTopics(prevSelected => {\n      if (prevSelected.includes(topic)) {\n        // Remove topic if already selected (uncheck)\n        return prevSelected.filter(t => t !== topic);\n      } else {\n        // Add topic if not already selected (check)\n        return [...prevSelected, topic];\n      }\n    });\n    setCurrentPage(1); // Reset to first page when changing filters\n  };\n\n  // Filter jobs based on active tab and selected subtopics\n  const filteredJobs = jobs.filter(job => {\n    // Filter by tab\n    const matchesTab = activeTab === 'all' || job.location === activeTab;\n\n    // Filter by sub topics if any are selected\n    const matchesSubTopics = selectedSubTopics.length === 0 || job.subTopics && job.subTopics.some(topic => selectedSubTopics.includes(topic));\n\n    // Only include active jobs\n    const isActive = job.status !== 'Inactive';\n    return matchesTab && matchesSubTopics && isActive;\n  });\n  // Filter urgent/hot jobs for the left sidebar - use hotJobs state\n  const urgentJobs = hotJobs.slice(0, 7);\n\n  // Get paginated jobs - show 8 per page\n  const indexOfLastJob = currentPage * jobsPerPage;\n  const indexOfFirstJob = indexOfLastJob - jobsPerPage;\n  const currentJobs = filteredJobs.filter(job => job.hot !== true) // Don't show hot jobs in the regular listings\n  .slice(indexOfFirstJob, indexOfLastJob);\n\n  // Calculate total pages\n  const totalPages = Math.ceil(filteredJobs.filter(job => job.hot !== true).length / jobsPerPage);\n\n  // Change page\n  const handlePageChange = pageNumber => {\n    if (pageNumber < 1 || pageNumber > totalPages) return;\n    setCurrentPage(pageNumber);\n    // Scroll to top of job listings\n    window.scrollTo({\n      top: 400,\n      behavior: 'smooth'\n    });\n  };\n\n  // Generate page numbers for pagination\n  const pageNumbers = [];\n  const displayRange = 5; // How many page numbers to show at once\n\n  let startPage = Math.max(1, currentPage - Math.floor(displayRange / 2));\n  let endPage = Math.min(totalPages, startPage + displayRange - 1);\n  if (endPage - startPage + 1 < displayRange) {\n    startPage = Math.max(1, endPage - displayRange + 1);\n  }\n  for (let i = startPage; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Change active tab\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n    setCurrentPage(1); // Reset to first page when changing tabs\n  };\n\n  // Clear all selected subtopics\n  const clearSubTopicFilters = () => {\n    setSelectedSubTopics([]);\n  };\n\n  // Toggle filter sidebar\n  const toggleFilterSidebar = () => {\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\n  };\n\n  // Function to increment view count\n  const incrementViewCount = async jobId => {\n    try {\n      await ApiService.jobs.recordView(jobId);\n    } catch (err) {\n      console.error(\"Error incrementing view count:\", err);\n    }\n  };\n  const JobLoadingSkeleton = ({\n    count = 3\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"jobs-loading-container\",\n      children: [...Array(count)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-card-skeleton\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-thumbnail\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line company\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line description short\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homepage-container\",\n    children: [/*#__PURE__*/_jsxDEV(PageHelmet, {\n      title: \"Your Path to the Perfect Job\",\n      description: \"Find your dream job across all sectors - Government, Private, Foreign, and Internships.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-spacing\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-search-area\",\n      children: /*#__PURE__*/_jsxDEV(SearchArea, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-top-banner\",\n      children: /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"main-title\",\n            children: \"New Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-toggle-btn\",\n            onClick: toggleFilterSidebar,\n            \"aria-label\": \"Toggle filters\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faFilter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), \"Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'all' ? 'active all-active' : ''}`,\n            onClick: () => handleTabChange('all'),\n            children: \"All Jobs\"\n          }, \"all\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Government Jobs' ? 'active gov-active' : ''}`,\n            onClick: () => handleTabChange('Government Jobs'),\n            children: \"Government Jobs\"\n          }, \"gov\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Private Jobs' ? 'active private-active' : ''}`,\n            onClick: () => handleTabChange('Private Jobs'),\n            children: \"Private Jobs\"\n          }, \"private\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Foreign Jobs' ? 'active foreign-active' : ''}`,\n            onClick: () => handleTabChange('Foreign Jobs'),\n            children: \"Foreign Jobs\"\n          }, \"foreign\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Internships' ? 'active internships-active' : ''}`,\n            onClick: () => handleTabChange('Internships'),\n            children: \"Internships\"\n          }, \"internships\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-layout\",\n        children: [\"          \", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"left-column\",\n          children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-overlay\",\n            children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading urgent jobs...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this) : urgentJobs.length > 0 ? urgentJobs.map(job => /*#__PURE__*/_jsxDEV(UrgentJobCard, {\n            job: job,\n            onViewIncrement: incrementViewCount\n          }, `urgent-${job.id}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-urgent-jobs\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No urgent jobs available right now.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recruiting-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Recruiting?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get your job postings seen by thousands of job seekers.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\",\n              alt: \"Recruiting\",\n              className: \"recruiting-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/contact\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"post-job-btn\",\n                children: \"Post a Job\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"middle-column\",\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-notification\",\n            style: {\n              color: '#e74c3c',\n              backgroundColor: '#fdf2f2',\n              border: '1px solid #f5c6cb',\n              borderRadius: '8px',\n              padding: '12px 16px',\n              margin: '16px auto',\n              textAlign: 'center',\n              fontSize: '14px',\n              maxWidth: '400px',\n              boxShadow: '0 2px 4px rgba(231, 76, 60, 0.1)'\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-overlay\",\n            children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading available positions...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 15\n          }, this) : currentJobs.length > 0 ? currentJobs.map(job => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/job/${job.id}`,\n            className: `job-card-redesigned job-type-${(job.workTime || '').toLowerCase().replace(/\\s+/g, '-')}`,\n            style: {\n              background: \"#fff\",\n              textDecoration: \"none\",\n              color: \"inherit\",\n              position: \"relative\"\n            },\n            onClick: () => incrementViewCount(job.id),\n            children: [job.hot && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"urgent-tag jobcard-urgent-tag\",\n              children: \"\\uD83D\\uDD25 URGENT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"company-info-header\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"job-image-container\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: job.image,\n                  alt: job.position,\n                  className: \"job-thumbnail\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = 'https://via.placeholder.com/300x300?text=Job+Image';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"job-main-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"job-title-redesigned\",\n                  children: job.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"company-info-urgent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-logo-urgent\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: job.logo,\n                      alt: job.company,\n                      onError: e => {\n                        e.target.onerror = null;\n                        e.target.src = 'https://via.placeholder.com/30';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-details-urgent\",\n                    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"company-name-urgent\",\n                      children: job.company\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-meta-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag location\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faBriefcase\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 27\n                    }, this), \" \", job.location]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag work-type\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faClock\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 27\n                    }, this), \" \", job.workTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag salary\",\n                    children: formatSalary(job.min_salary, job.max_salary)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-description-redesigned\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: job.description ? job.description.substring(0, 140) + '...' : 'No description available.'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"posting-date\",\n                children: [\" \", job.postedTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"apply-section\",\n                style: {\n                  marginLeft: \"auto\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"apply-btn-redesigned\",\n                  children: \"Apply Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 19\n            }, this)]\n          }, `regular-${job.id}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-jobs-redesigned\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-jobs-icon\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faBriefcase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"No Matching Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"There are no jobs available in this category right now.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Try adjusting your filters or check back later.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this), !error && !loading && jobs.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: 'gray',\n                marginTop: '16px'\n              },\n              children: \"No jobs found. Please check your API server or try again later.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"page-arrow\",\n              onClick: () => handlePageChange(currentPage - 1),\n              disabled: currentPage === 1,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faChevronLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)\n            }, \"prev\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `page-number ${currentPage === number ? 'active' : ''}`,\n              onClick: () => handlePageChange(number),\n              children: number\n            }, `page-${number}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"page-arrow\",\n              onClick: () => handlePageChange(currentPage + 1),\n              disabled: currentPage === totalPages || totalPages === 0,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faChevronRight\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this)\n            }, \"next\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), totalPages > displayRange && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"page-info\",\n              children: [\"of \", totalPages, \" pages\"]\n            }, \"more\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"right-column\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtopics-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Filter by Specialization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), selectedSubTopics.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"clear-filters-btn\",\n                onClick: clearSubTopicFilters,\n                children: [\"Clear all \", /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: faTimes\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 31\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtopics-list\",\n              children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: `subtopic-${index}`,\n                  checked: selectedSubTopics.includes(topic),\n                  onChange: () => handleSubTopicChange(topic)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `subtopic-${index}`,\n                  title: topic,\n                  children: shortenTopicName(topic)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 21\n                }, this)]\n              }, `subtopic-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-filters\",\n              children: selectedSubTopics.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filters-label\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFilter\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 23\n                  }, this), \" Applied filters:\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"selected-topics-list\",\n                  children: selectedSubTopics.map((topic, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"selected-topic-tag\",\n                    title: topic,\n                    children: [shortenTopicName(topic, 20), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"remove-topic\",\n                      onClick: () => handleSubTopicChange(topic),\n                      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faTimes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 27\n                    }, this)]\n                  }, `selected-${idx}`, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-filters\",\n                children: \"No filters applied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this), availableSubTopics.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-subtopics-message\",\n              style: {\n                fontSize: '12px',\n                color: '#666',\n                fontStyle: 'italic',\n                textAlign: 'center',\n                padding: '10px',\n                border: '1px dashed #ddd',\n                borderRadius: '4px',\n                marginTop: '10px'\n              },\n              children: \"No sub-topics available. Click refresh to load latest options.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"browse-jobs-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Looking for More?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Explore all available job opportunities across different categories and locations.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\",\n              alt: \"Browse Jobs\",\n              className: \"browse-jobs-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/jobs\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"browse-jobs-btn\",\n                children: \"Browse All Jobs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeaturedSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this), isFilterSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-sidebar-overlay\",\n      onClick: toggleFilterSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 749,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `filter-sidebar ${isFilterSidebarOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faFilter\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this), \" Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-sidebar-btn\",\n          onClick: toggleFilterSidebar,\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faTimes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subtopics-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Filter by Specialization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this), selectedSubTopics.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"clear-filters-btn\",\n              onClick: clearSubTopicFilters,\n              children: [\"Clear all \", /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-list\",\n            children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: `sidebar-subtopic-${index}`,\n                checked: selectedSubTopics.includes(topic),\n                onChange: () => handleSubTopicChange(topic)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: `sidebar-subtopic-${index}`,\n                title: topic,\n                children: shortenTopicName(topic)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this)]\n            }, `sidebar-subtopic-${index}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-filters\",\n            children: selectedSubTopics.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filters-label\",\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: faFilter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 21\n                }, this), \" Applied filters:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-topics-list\",\n                children: selectedSubTopics.map((topic, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"selected-topic-tag\",\n                  title: topic,\n                  children: [shortenTopicName(topic, 20), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"remove-topic\",\n                    onClick: () => handleSubTopicChange(topic),\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTimes\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 25\n                  }, this)]\n                }, `sidebar-selected-${idx}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-filters\",\n              children: \"No filters applied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-banner\",\n      children: /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 826,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"social-media-feeds\",\n      children: /*#__PURE__*/_jsxDEV(SocialMediaFeeds, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 830,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 829,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"Ems2Y8x7RztFy8ARBdCl3gVsv+Q=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "FontAwesomeIcon", "faMapMarkerAlt", "faClock", "faEye", "faChevronLeft", "faChevronRight", "faFilter", "faTimes", "faBriefcase", "faBookmark", "faShare", "faBars", "FaSpinner", "ApiService", "getActiveSubTopicNames", "getDefaultSubTopics", "NewsLetter", "Banner", "SearchArea", "FeaturedSection", "SocialMediaFeeds", "FeaturedCompanies", "UrgentJobCard", "PageHelmet", "jsxDEV", "_jsxDEV", "HomePage", "_s", "jobs", "setJobs", "hotJobs", "setHotJobs", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "currentPage", "setCurrentPage", "selectedSubTopics", "setSelectedSubTopics", "isFilterSidebarOpen", "setIsFilterSidebarOpen", "availableSubTopics", "setAvailableSubTopics", "jobsPerPage", "loadSubTopicsFromStorage", "activeSubTopicNames", "length", "defaultSubTopics", "err", "console", "shortenTopicName", "topic", "max<PERSON><PERSON><PERSON>", "substring", "handleSubTopicsUpdate", "event", "log", "window", "addEventListener", "removeEventListener", "fetchJobs", "response", "getAll", "_t", "Date", "getTime", "formattedJobs", "data", "map", "job", "id", "job_id", "position", "job_title", "company", "company_name", "location", "main_topics", "workTime", "job_type", "min_salary", "max_salary", "salary", "formatSalary", "views", "view_count", "postedTime", "formatPostedDate", "start_date", "datePosted", "image", "job_post_thumbnail", "job_post_image", "logo", "company_logo", "company_logo_url", "hot", "status", "description", "job_description", "subTopics", "sub_topics", "trim", "startsWith", "JSON", "parse", "Array", "isArray", "date<PERSON><PERSON>j", "created_at", "posted_date", "jobId", "sortedJobs", "sort", "a", "b", "dateA", "dateB", "fetchHotJobs", "getHotJobs", "formattedHotJobs", "scrollTo", "min", "max", "undefined", "isNaN", "Number", "toLocaleString", "dateString", "postedDate", "now", "postedDateNormalized", "getFullYear", "getMonth", "getDate", "todayNormalized", "oneDayMs", "daysAgo", "Math", "round", "floor", "handleSubTopicChange", "prevSelected", "includes", "filter", "t", "filteredJobs", "matchesTab", "matchesSubTopics", "some", "isActive", "urgentJobs", "slice", "indexOfLastJob", "indexOfFirstJob", "currentJobs", "totalPages", "ceil", "handlePageChange", "pageNumber", "top", "behavior", "pageNumbers", "displayRange", "startPage", "endPage", "i", "push", "handleTabChange", "tabName", "clearSubTopicFilters", "toggleFilterSidebar", "incrementViewCount", "recordView", "JobLoadingSkeleton", "count", "className", "children", "_", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "icon", "onViewIncrement", "src", "alt", "href", "style", "color", "backgroundColor", "border", "borderRadius", "padding", "margin", "textAlign", "fontSize", "max<PERSON><PERSON><PERSON>", "boxShadow", "to", "toLowerCase", "replace", "background", "textDecoration", "onError", "e", "target", "onerror", "marginLeft", "marginTop", "disabled", "number", "index", "type", "checked", "onChange", "htmlFor", "idx", "fontStyle", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/HomePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport '../css/HomePage.css';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faMapMarkerAlt, faClock, faEye, faChevronLeft, faChevronRight, faFilter, faTimes, faBriefcase, faBookmark, faShare, faBars } from '@fortawesome/free-solid-svg-icons';\r\nimport { FaSpinner } from 'react-icons/fa';\r\nimport ApiService from '../services/apiService';\r\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../utils/subTopicsUtils';\r\nimport NewsLetter from './NewsLetter';\r\nimport Banner from './Banner';\r\nimport SearchArea from './SearchArea';\r\nimport FeaturedSection from './FeaturedSection';\r\nimport SocialMediaFeeds from './SocialMediaFeeds';\r\nimport FeaturedCompanies from './FeaturedCompanies';\r\nimport UrgentJobCard from './UrgentJobCard';\r\nimport PageHelmet from './PageHelmet';\r\n\r\nconst HomePage = () => {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [hotJobs, setHotJobs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('all');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedSubTopics, setSelectedSubTopics] = useState([]);\r\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\r\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\r\n  const jobsPerPage = 8;\r\n\r\n  // Load available sub-topics from localStorage\r\n  const loadSubTopicsFromStorage = () => {\r\n    try {\r\n      const activeSubTopicNames = getActiveSubTopicNames();\r\n      if (activeSubTopicNames.length > 0) {\r\n        setAvailableSubTopics(activeSubTopicNames);\r\n      } else {\r\n        // Use default sub-topics if no saved data\r\n        const defaultSubTopics = getDefaultSubTopics();\r\n        setAvailableSubTopics(defaultSubTopics);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error loading sub-topics from localStorage:\", err);\r\n      // Fallback to default sub-topics\r\n      const defaultSubTopics = getDefaultSubTopics();\r\n      setAvailableSubTopics(defaultSubTopics);\r\n    }\r\n  };\r\n\r\n  // Function to shorten long topic names\r\n  const shortenTopicName = (topic, maxLength = 30) => {\r\n    if (topic.length <= maxLength) {\r\n      return topic;\r\n    }\r\n    return topic.substring(0, maxLength) + '...';\r\n  };\r\n\r\n  // Initialize available sub-topics\r\n  useEffect(() => {\r\n    loadSubTopicsFromStorage();\r\n  }, []);\r\n\r\n  // Listen for sub-topics updates from SubTopicsAdmin\r\n  useEffect(() => {\r\n    const handleSubTopicsUpdate = (event) => {\r\n      console.log(\"Sub-topics updated in HomePage, refreshing available options...\");\r\n      loadSubTopicsFromStorage();\r\n    };\r\n\r\n    // Add event listener for sub-topics updates\r\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Fetch jobs from API\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        // Add a timestamp parameter to prevent caching\r\n        const response = await ApiService.jobs.getAll({ _t: new Date().getTime() });\r\n        \r\n        // Transform backend data to match the format used in frontend\r\n        const formattedJobs = response.data.map(job => ({\r\n          id: job.job_id,\r\n          position: job.job_title,\r\n          company: job.company_name,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          // Pass min and max salary for display\r\n          min_salary: job.min_salary,\r\n          max_salary: job.max_salary,\r\n          salary: formatSalary(job.min_salary, job.max_salary),\r\n          views: job.view_count || 0, // Use actual view count instead of random number\r\n          postedTime: formatPostedDate(job.start_date),\r\n          datePosted: job.start_date, // Store the original date string for debugging\r\n          // Ensure we have valid image paths or fallbacks\r\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\r\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          hot: job.hot || false, // Default if not in database\r\n          status: job.status || 'Active',\r\n          description: job.job_description || 'No description provided',\r\n          subTopics: (() => {\r\n            try {\r\n              if (!job.sub_topics) return [];\r\n              if (typeof job.sub_topics === 'string') {\r\n                if (job.sub_topics.trim().startsWith('[')) {\r\n                  return JSON.parse(job.sub_topics);\r\n                } else {\r\n                  return [job.sub_topics];\r\n                }\r\n              } else if (Array.isArray(job.sub_topics)) {\r\n                return job.sub_topics;\r\n              }\r\n              return [];\r\n            } catch (err) {\r\n              console.error(\"Error parsing sub_topics:\", err);\r\n              return [];\r\n            }\r\n          })(),\r\n          // Add a Date object for sorting purposes - prioritize created_at, then start_date\r\n          dateObj: new Date(job.created_at || job.start_date || job.posted_date || new Date()),\r\n          jobId: job.job_id // Store job ID for secondary sorting\r\n        }));\r\n\r\n        // Sort jobs by creation date descending (newest first), then by ID descending as fallback\r\n        const sortedJobs = formattedJobs.sort((a, b) => {\r\n          // First, try to sort by creation date\r\n          const dateA = a.dateObj;\r\n          const dateB = b.dateObj;\r\n\r\n          if (dateB.getTime() !== dateA.getTime()) {\r\n            return dateB.getTime() - dateA.getTime();\r\n          }\r\n\r\n          // If dates are the same, sort by ID descending (higher ID = newer)\r\n          return b.jobId - a.jobId;\r\n        });\r\n        \r\n        setJobs(sortedJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching jobs:\", err);\r\n        setError(\"Failed to load jobs from server\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    const fetchHotJobs = async () => {\r\n      try {\r\n        const response = await ApiService.jobs.getHotJobs();\r\n        \r\n        // Transform hot jobs data to match the format used in frontend\r\n        const formattedHotJobs = response.data.map(job => ({\r\n          id: job.job_id,\r\n          position: job.job_title,\r\n          company: job.company_name,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          min_salary: job.min_salary,\r\n          max_salary: job.max_salary,\r\n          salary: formatSalary(job.min_salary, job.max_salary),\r\n          views: job.view_count || 0,\r\n          postedTime: formatPostedDate(job.start_date),\r\n          datePosted: job.start_date,\r\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\r\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          hot: true,\r\n          status: job.status || 'Active',\r\n          description: job.job_description || 'No description provided',\r\n          subTopics: (() => {\r\n            try {\r\n              if (!job.sub_topics) return [];\r\n              if (typeof job.sub_topics === 'string') {\r\n                if (job.sub_topics.trim().startsWith('[')) {\r\n                  return JSON.parse(job.sub_topics);\r\n                } else {\r\n                  return [job.sub_topics];\r\n                }\r\n              } else if (Array.isArray(job.sub_topics)) {\r\n                return job.sub_topics;\r\n              }\r\n              return [];\r\n            } catch (err) {\r\n              console.error(\"Error parsing sub_topics:\", err);\r\n              return [];\r\n            }\r\n          })(),\r\n          dateObj: new Date(job.start_date)\r\n        }));\r\n        \r\n        setHotJobs(formattedHotJobs);\r\n      } catch (err) {\r\n        console.error(\"Error fetching hot jobs:\", err);\r\n        // Don't set error for hot jobs failure, just log it\r\n      }\r\n    };\r\n\r\n    fetchJobs();\r\n    fetchHotJobs();\r\n    // Set default tab to \"All Jobs\"\r\n    setActiveTab('all');\r\n    \r\n    // Scroll to top when component mounts\r\n    window.scrollTo(0, 0);\r\n  }, []);\r\n\r\n  // Helper function to format min/max salary as \"Rs. min - Rs. max\"\r\n  const formatSalary = (min, max) => {\r\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) &&\r\n        (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\r\n      return 'Negotiable';\r\n    }\r\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\r\n      return 'Negotiable';\r\n    }\r\n    if (min && max) {\r\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    if (min) {\r\n      return `Rs. ${Number(min).toLocaleString()}`;\r\n    }\r\n    if (max) {\r\n      return `Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    return 'Negotiable';\r\n  };\r\n\r\n  // Helper function to format posted date\r\n  const formatPostedDate = (dateString) => {\r\n    if (!dateString) return 'Recently';\r\n    \r\n    try {\r\n      // Parse the date from the server\r\n      const postedDate = new Date(dateString);\r\n      const now = new Date();\r\n      \r\n      // Normalize both dates to midnight in local timezone to compare just the dates\r\n      const postedDateNormalized = new Date(\r\n        postedDate.getFullYear(), \r\n        postedDate.getMonth(), \r\n        postedDate.getDate()\r\n      ).getTime();\r\n      \r\n      const todayNormalized = new Date(\r\n        now.getFullYear(),\r\n        now.getMonth(),\r\n        now.getDate()\r\n      ).getTime();\r\n      \r\n      const oneDayMs = 24 * 60 * 60 * 1000; // One day in milliseconds\r\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\r\n      \r\n      // Check if it's today\r\n      if (daysAgo === 0) {\r\n        return 'Today';\r\n      }\r\n      \r\n      // Check if it's yesterday\r\n      if (daysAgo === 1) {\r\n        return 'Yesterday';\r\n      }\r\n      \r\n      // Calculate exact days difference\r\n      if (daysAgo < 7) return `${daysAgo} days ago`;\r\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\r\n      return `${Math.floor(daysAgo / 30)} months ago`;\r\n    } catch (err) {\r\n      console.error(\"Error formatting date:\", err, \"for date:\", dateString);\r\n      return 'Recently';\r\n    }\r\n  };\r\n\r\n  // Handle subtopic selection\r\n  const handleSubTopicChange = (topic) => {\r\n    setSelectedSubTopics(prevSelected => {\r\n      if (prevSelected.includes(topic)) {\r\n        // Remove topic if already selected (uncheck)\r\n        return prevSelected.filter(t => t !== topic);\r\n      } else {\r\n        // Add topic if not already selected (check)\r\n        return [...prevSelected, topic];\r\n      }\r\n    });\r\n    setCurrentPage(1); // Reset to first page when changing filters\r\n  };\r\n\r\n  // Filter jobs based on active tab and selected subtopics\r\n  const filteredJobs = jobs\r\n    .filter(job => {\r\n      // Filter by tab\r\n      const matchesTab = activeTab === 'all' || job.location === activeTab;\r\n      \r\n      // Filter by sub topics if any are selected\r\n      const matchesSubTopics = selectedSubTopics.length === 0 || \r\n        (job.subTopics && job.subTopics.some(topic => selectedSubTopics.includes(topic)));\r\n      \r\n      // Only include active jobs\r\n      const isActive = job.status !== 'Inactive';\r\n      \r\n      return matchesTab && matchesSubTopics && isActive;\r\n    });\r\n  // Filter urgent/hot jobs for the left sidebar - use hotJobs state\r\n  const urgentJobs = hotJobs.slice(0, 7);\r\n  \r\n  // Get paginated jobs - show 8 per page\r\n  const indexOfLastJob = currentPage * jobsPerPage;\r\n  const indexOfFirstJob = indexOfLastJob - jobsPerPage;\r\n  const currentJobs = filteredJobs\r\n    .filter(job => job.hot !== true) // Don't show hot jobs in the regular listings\r\n    .slice(indexOfFirstJob, indexOfLastJob);\r\n  \r\n  // Calculate total pages\r\n  const totalPages = Math.ceil(\r\n    filteredJobs.filter(job => job.hot !== true).length / jobsPerPage\r\n  );\r\n\r\n  // Change page\r\n  const handlePageChange = (pageNumber) => {\r\n    if (pageNumber < 1 || pageNumber > totalPages) return;\r\n    setCurrentPage(pageNumber);\r\n    // Scroll to top of job listings\r\n    window.scrollTo({ top: 400, behavior: 'smooth' });\r\n  };\r\n\r\n  // Generate page numbers for pagination\r\n  const pageNumbers = [];\r\n  const displayRange = 5; // How many page numbers to show at once\r\n  \r\n  let startPage = Math.max(1, currentPage - Math.floor(displayRange / 2));\r\n  let endPage = Math.min(totalPages, startPage + displayRange - 1);\r\n  \r\n  if (endPage - startPage + 1 < displayRange) {\r\n    startPage = Math.max(1, endPage - displayRange + 1);\r\n  }\r\n  \r\n  for (let i = startPage; i <= endPage; i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  // Change active tab\r\n  const handleTabChange = (tabName) => {\r\n    setActiveTab(tabName);\r\n    setCurrentPage(1); // Reset to first page when changing tabs\r\n  };\r\n\r\n  // Clear all selected subtopics\r\n  const clearSubTopicFilters = () => {\r\n    setSelectedSubTopics([]);\r\n  };\r\n\r\n  // Toggle filter sidebar\r\n  const toggleFilterSidebar = () => {\r\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\r\n  };\r\n\r\n  // Function to increment view count\r\n  const incrementViewCount = async (jobId) => {\r\n    try {\r\n      await ApiService.jobs.recordView(jobId);\r\n    } catch (err) {\r\n      console.error(\"Error incrementing view count:\", err);\r\n    }\r\n  };\r\n\r\n  const JobLoadingSkeleton = ({ count = 3 }) => {\r\n  return (\r\n    <div className=\"jobs-loading-container\">\r\n      {[...Array(count)].map((_, i) => (\r\n        <div key={i} className=\"job-card-skeleton\">\r\n          <div className=\"skeleton-thumbnail\"></div>\r\n          <div className=\"skeleton-content\">\r\n            <div className=\"skeleton-line title\"></div>\r\n            <div className=\"skeleton-line company\"></div>\r\n            <div className=\"skeleton-meta\">\r\n              <div className=\"skeleton-tag\"></div>\r\n              <div className=\"skeleton-tag\"></div>\r\n              <div className=\"skeleton-tag\"></div>\r\n            </div>\r\n            <div className=\"skeleton-line description\"></div>\r\n            <div className=\"skeleton-line description short\"></div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\n  return (\r\n    <div className=\"homepage-container\">\r\n      <PageHelmet \r\n        title=\"Your Path to the Perfect Job\" \r\n        description=\"Find your dream job across all sectors - Government, Private, Foreign, and Internships.\"\r\n      />\r\n      <div className=\"content-spacing\"></div>\r\n      \r\n      {/* Search Area */}\r\n      <section className=\"homepage-search-area\">\r\n        <SearchArea />\r\n      </section>\r\n      \r\n      {/* Banner */}\r\n      <section className=\"homepage-top-banner\">\r\n        <Banner />\r\n      </section>\r\n      \r\n      {/* Main Content with constrained width */}\r\n      <div className=\"home-content-wrapper\">\r\n        {/* Header with New Jobs Title */}\r\n        <div className=\"home-header\" >\r\n          <div className=\"header-left\">\r\n            <h1 className=\"main-title\">New Jobs</h1>\r\n            <button \r\n              className=\"filter-toggle-btn\" \r\n              onClick={toggleFilterSidebar}\r\n              aria-label=\"Toggle filters\"\r\n            >\r\n              <FontAwesomeIcon icon={faFilter} />\r\n              Filters\r\n            </button>\r\n          </div>\r\n          <div className=\"header-tabs\">\r\n            <span \r\n              className={`tab ${activeTab === 'all' ? 'active all-active' : ''}`} \r\n              key=\"all\"\r\n              onClick={() => handleTabChange('all')}\r\n            >\r\n              All Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Government Jobs' ? 'active gov-active' : ''}`} \r\n              key=\"gov\"\r\n              onClick={() => handleTabChange('Government Jobs')}\r\n            >\r\n              Government Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Private Jobs' ? 'active private-active' : ''}`} \r\n              key=\"private\"\r\n              onClick={() => handleTabChange('Private Jobs')}\r\n            >\r\n              Private Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Foreign Jobs' ? 'active foreign-active' : ''}`} \r\n              key=\"foreign\"\r\n              onClick={() => handleTabChange('Foreign Jobs')}\r\n            >\r\n              Foreign Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Internships' ? 'active internships-active' : ''}`} \r\n              key=\"internships\"\r\n              onClick={() => handleTabChange('Internships')}\r\n            >\r\n              Internships\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Main 3-column Layout */}\r\n        <div className=\"home-layout\">          {/* Left Column - Urgent Jobs */}\r\n          <div className=\"left-column\">\r\n            {loading ? (\r\n              <div className=\"loading-overlay\">\r\n                <FaSpinner className=\"spinner\" />\r\n                <p>Loading urgent jobs...</p>\r\n              </div>\r\n            ) : urgentJobs.length > 0 ? (\r\n              urgentJobs.map(job => (\r\n                <UrgentJobCard\r\n                  key={`urgent-${job.id}`}\r\n                  job={job}\r\n                  onViewIncrement={incrementViewCount}\r\n                />\r\n              ))\r\n            ) : (\r\n              <div className=\"no-urgent-jobs\">\r\n                <p>No urgent jobs available right now.</p>\r\n              </div>\r\n            )}\r\n            \r\n            {/* Recruiting Box */}\r\n            <div className=\"recruiting-box\">\r\n              <h3>Recruiting?</h3>\r\n              <p>Get your job postings seen by thousands of job seekers.</p>\r\n              <img src=\"https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\" alt=\"Recruiting\" className=\"recruiting-image\" />\r\n              <a href=\"/contact\">\r\n                <button className=\"post-job-btn\">Post a Job</button>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Middle Column - Regular Jobs */}\r\n          <div className=\"middle-column\">\r\n            {error && (\r\n              <div className=\"error-notification\" style={{ \r\n                color: '#e74c3c', \r\n                backgroundColor: '#fdf2f2', \r\n                border: '1px solid #f5c6cb', \r\n                borderRadius: '8px', \r\n                padding: '12px 16px', \r\n                margin: '16px auto', \r\n                textAlign: 'center', \r\n                fontSize: '14px', \r\n                maxWidth: '400px',\r\n                boxShadow: '0 2px 4px rgba(231, 76, 60, 0.1)'\r\n              }}>\r\n                {error}\r\n              </div>\r\n            )}\r\n            {loading ? (\r\n              <div className=\"loading-overlay\">\r\n                <FaSpinner className=\"spinner\" />\r\n                <p>Loading available positions...</p>\r\n              </div>\r\n            ) : currentJobs.length > 0 ? (\r\n              currentJobs.map(job => (\r\n                <Link\r\n                  key={`regular-${job.id}`}\r\n                  to={`/job/${job.id}`}\r\n                  className={`job-card-redesigned job-type-${(job.workTime || '').toLowerCase().replace(/\\s+/g, '-')}`}\r\n                  style={{ background: \"#fff\", textDecoration: \"none\", color: \"inherit\", position: \"relative\" }}\r\n                  onClick={() => incrementViewCount(job.id)}\r\n                >\r\n                  {job.hot && (\r\n                    <span className=\"urgent-tag jobcard-urgent-tag\">🔥 URGENT</span>\r\n                  )}\r\n                  <div className=\"job-card-header\">\r\n                    <div className=\"company-info-header\">\r\n                      {/* Removing the date from here */}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"job-card-content\">\r\n                    <div className=\"job-image-container\">\r\n                      <img \r\n                        src={job.image} \r\n                        alt={job.position} \r\n                        className=\"job-thumbnail\" \r\n                        onError={(e) => {\r\n                          e.target.onerror = null; \r\n                          e.target.src = 'https://via.placeholder.com/300x300?text=Job+Image';\r\n                        }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"job-main-content\">\r\n                      <h3 className=\"job-title-redesigned\">\r\n                        {job.position} \r\n                      </h3>\r\n                  <div className=\"company-info-urgent\">\r\n                    <div className=\"company-logo-urgent\">\r\n                      <img \r\n                        src={job.logo} \r\n                        alt={job.company}\r\n                        onError={(e) => {\r\n                          e.target.onerror = null;\r\n                          e.target.src = 'https://via.placeholder.com/30';\r\n                        }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"company-details-urgent\">\r\n                      <h3 className=\"company-name-urgent\">{job.company}</h3>\r\n                    </div>\r\n                  </div>\r\n                      <div className=\"job-meta-info\">\r\n                        <span className=\"job-tag location\">\r\n                          <FontAwesomeIcon icon={faBriefcase} /> {job.location}\r\n                        </span>\r\n                        <span className=\"job-tag work-type\">\r\n                          <FontAwesomeIcon icon={faClock} /> {job.workTime}\r\n                        </span>\r\n                        <span className=\"job-tag salary\">\r\n                          {formatSalary(job.min_salary, job.max_salary)}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"job-description-redesigned\">\r\n                        <p>{job.description ? job.description.substring(0, 140) + '...' : 'No description available.'}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"job-card-footer\">\r\n                    <span className=\"posting-date\"> {job.postedTime}</span>\r\n                    <div className=\"apply-section\" style={{ marginLeft: \"auto\" }}>\r\n                      <button\r\n                        className=\"apply-btn-redesigned\"\r\n                      >\r\n                        Apply Now\r\n                      </button>\r\n                      {/* <button\r\n                        className=\"job-action-btn share-btn\"\r\n                        onClick={e => {\r\n                          e.preventDefault();\r\n                          // Optionally implement share logic here\r\n                        }}\r\n                      >\r\n                        <FontAwesomeIcon icon={faShare} />\r\n                      </button> */}\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              ))\r\n            ) : (\r\n              <div className=\"no-jobs-redesigned\">\r\n                <div className=\"no-jobs-icon\">\r\n                  <FontAwesomeIcon icon={faBriefcase} />\r\n                </div>\r\n                <h3>No Matching Jobs</h3>\r\n                <p>There are no jobs available in this category right now.</p>\r\n                <p>Try adjusting your filters or check back later.</p>\r\n                {!error && !loading && jobs.length === 0 && (\r\n                  <div style={{ color: 'gray', marginTop: '16px' }}>\r\n                    No jobs found. Please check your API server or try again later.\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n            \r\n            {/* Pagination */}\r\n            <div className=\"pagination\">\r\n              <button \r\n                className=\"page-arrow\" \r\n                key=\"prev\"\r\n                onClick={() => handlePageChange(currentPage - 1)}\r\n                disabled={currentPage === 1}\r\n              >\r\n                <FontAwesomeIcon icon={faChevronLeft} />\r\n              </button>\r\n              \r\n              {pageNumbers.map(number => (\r\n                <button\r\n                  key={`page-${number}`}\r\n                  className={`page-number ${currentPage === number ? 'active' : ''}`}\r\n                  onClick={() => handlePageChange(number)}\r\n                >\r\n                  {number}\r\n                </button>\r\n              ))}\r\n              \r\n              <button \r\n                className=\"page-arrow\" \r\n                key=\"next\"\r\n                onClick={() => handlePageChange(currentPage + 1)}\r\n                disabled={currentPage === totalPages || totalPages === 0}\r\n              >\r\n                <FontAwesomeIcon icon={faChevronRight} />\r\n              </button>\r\n              \r\n              {totalPages > displayRange && (\r\n                <span className=\"page-info\" key=\"more\">\r\n                  of {totalPages} pages\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right Column - Subtopics Filter */}\r\n          <div className=\"right-column\">\r\n            <div className=\"subtopics-box\">\r\n              <div className=\"subtopics-header\">\r\n                <h3>Filter by Specialization</h3>\r\n                {selectedSubTopics.length > 0 && (\r\n                  <button className=\"clear-filters-btn\" onClick={clearSubTopicFilters}>\r\n                    Clear all <FontAwesomeIcon icon={faTimes} />\r\n                  </button>\r\n                )}\r\n              </div>\r\n              <div className=\"subtopics-list\">\r\n                {availableSubTopics.map((topic, index) => (\r\n                  <div\r\n                    key={`subtopic-${index}`}\r\n                    className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}\r\n                  >\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id={`subtopic-${index}`}\r\n                      checked={selectedSubTopics.includes(topic)}\r\n                      onChange={() => handleSubTopicChange(topic)}\r\n                    />\r\n                    <label htmlFor={`subtopic-${index}`} title={topic}>\r\n                      {shortenTopicName(topic)}\r\n                    </label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"selected-filters\">\r\n                {selectedSubTopics.length > 0 ? (\r\n                  <div>\r\n                    <div className=\"filters-label\">\r\n                      <FontAwesomeIcon icon={faFilter} /> Applied filters:\r\n                    </div>\r\n                    <div className=\"selected-topics-list\">\r\n                      {selectedSubTopics.map((topic, idx) => (\r\n                        <span key={`selected-${idx}`} className=\"selected-topic-tag\" title={topic}>\r\n                          {shortenTopicName(topic, 20)}\r\n                          <button\r\n                            className=\"remove-topic\"\r\n                            onClick={() => handleSubTopicChange(topic)}\r\n                          >\r\n                            <FontAwesomeIcon icon={faTimes} />\r\n                          </button>\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"no-filters\">No filters applied</div>\r\n                )}\r\n              </div>\r\n              {availableSubTopics.length === 0 && (\r\n                <div className=\"no-subtopics-message\" style={{\r\n                  fontSize: '12px',\r\n                  color: '#666',\r\n                  fontStyle: 'italic',\r\n                  textAlign: 'center',\r\n                  padding: '10px',\r\n                  border: '1px dashed #ddd',\r\n                  borderRadius: '4px',\r\n                  marginTop: '10px'\r\n                }}>\r\n                  No sub-topics available. Click refresh to load latest options.\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Browse Jobs Box */}\r\n            <div className=\"browse-jobs-box\">\r\n              <h3>Looking for More?</h3>\r\n              <p>Explore all available job opportunities across different categories and locations.</p>\r\n              <img src=\"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\" alt=\"Browse Jobs\" className=\"browse-jobs-image\" />\r\n              <a href=\"/jobs\">\r\n                <button className=\"browse-jobs-btn\">Browse All Jobs</button>\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Featured Section */}\r\n        <FeaturedSection />\r\n      </div>\r\n\r\n      {/* Filter Sidebar Overlay */}\r\n      {isFilterSidebarOpen && (\r\n        <div className=\"filter-sidebar-overlay\" onClick={toggleFilterSidebar}></div>\r\n      )}\r\n\r\n      {/* Sliding Filter Sidebar */}\r\n      <div className={`filter-sidebar ${isFilterSidebarOpen ? 'open' : ''}`}>\r\n        <div className=\"filter-sidebar-header\">\r\n          <h3><FontAwesomeIcon icon={faFilter} /> Filters</h3>\r\n          <button className=\"close-sidebar-btn\" onClick={toggleFilterSidebar}>\r\n            <FontAwesomeIcon icon={faTimes} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"filter-sidebar-content\">\r\n          <div className=\"subtopics-box\">\r\n            <div className=\"subtopics-header\">\r\n              <h3>Filter by Specialization</h3>\r\n              {selectedSubTopics.length > 0 && (\r\n                <button className=\"clear-filters-btn\" onClick={clearSubTopicFilters}>\r\n                  Clear all <FontAwesomeIcon icon={faTimes} />\r\n                </button>\r\n              )}\r\n            </div>\r\n            <div className=\"subtopics-list\">\r\n              {availableSubTopics.map((topic, index) => (\r\n                <div\r\n                  key={`sidebar-subtopic-${index}`}\r\n                  className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id={`sidebar-subtopic-${index}`}\r\n                    checked={selectedSubTopics.includes(topic)}\r\n                    onChange={() => handleSubTopicChange(topic)}\r\n                  />\r\n                  <label htmlFor={`sidebar-subtopic-${index}`} title={topic}>\r\n                    {shortenTopicName(topic)}\r\n                  </label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n            <div className=\"selected-filters\">\r\n              {selectedSubTopics.length > 0 ? (\r\n                <div>\r\n                  <div className=\"filters-label\">\r\n                    <FontAwesomeIcon icon={faFilter} /> Applied filters:\r\n                  </div>\r\n                  <div className=\"selected-topics-list\">\r\n                    {selectedSubTopics.map((topic, idx) => (\r\n                      <span key={`sidebar-selected-${idx}`} className=\"selected-topic-tag\" title={topic}>\r\n                        {shortenTopicName(topic, 20)}\r\n                        <button \r\n                          className=\"remove-topic\" \r\n                          onClick={() => handleSubTopicChange(topic)}\r\n                        >\r\n                          <FontAwesomeIcon icon={faTimes} />\r\n                        </button>\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"no-filters\">No filters applied</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Featured Companies */}\r\n      {/* <FeaturedCompanies /> */}\r\n      \r\n      {/* Newsletter */}\r\n      {/* <section className=\"homepage-newsletter\">\r\n        <NewsLetter />\r\n      </section> */}\r\n      \r\n      {/* Banner */}\r\n      <section className=\"homepage-banner\">\r\n        <Banner />\r\n      </section>\r\n      <section className=\"social-media-feeds\">\r\n        <SocialMediaFeeds />\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,qBAAqB;AAC5B,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,EAAEC,OAAO,EAAEC,KAAK,EAAEC,aAAa,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,QAAQ,mCAAmC;AAC9K,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,sBAAsB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACrF,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAMiD,WAAW,GAAG,CAAC;;EAErB;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI;MACF,MAAMC,mBAAmB,GAAGlC,sBAAsB,CAAC,CAAC;MACpD,IAAIkC,mBAAmB,CAACC,MAAM,GAAG,CAAC,EAAE;QAClCJ,qBAAqB,CAACG,mBAAmB,CAAC;MAC5C,CAAC,MAAM;QACL;QACA,MAAME,gBAAgB,GAAGnC,mBAAmB,CAAC,CAAC;QAC9C8B,qBAAqB,CAACK,gBAAgB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,6CAA6C,EAAEiB,GAAG,CAAC;MACjE;MACA,MAAMD,gBAAgB,GAAGnC,mBAAmB,CAAC,CAAC;MAC9C8B,qBAAqB,CAACK,gBAAgB,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,SAAS,GAAG,EAAE,KAAK;IAClD,IAAID,KAAK,CAACL,MAAM,IAAIM,SAAS,EAAE;MAC7B,OAAOD,KAAK;IACd;IACA,OAAOA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC9C,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACdiD,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjD,SAAS,CAAC,MAAM;IACd,MAAM2D,qBAAqB,GAAIC,KAAK,IAAK;MACvCN,OAAO,CAACO,GAAG,CAAC,iEAAiE,CAAC;MAC9EZ,wBAAwB,CAAC,CAAC;IAC5B,CAAC;;IAED;IACAa,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,qBAAqB,CAAC;;IAElE;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEL,qBAAqB,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3D,SAAS,CAAC,MAAM;IACd,MAAMiE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF9B,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAM+B,QAAQ,GAAG,MAAMnD,UAAU,CAACe,IAAI,CAACqC,MAAM,CAAC;UAAEC,EAAE,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;QAAE,CAAC,CAAC;;QAE3E;QACA,MAAMC,aAAa,GAAGL,QAAQ,CAACM,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAC9CC,EAAE,EAAED,GAAG,CAACE,MAAM;UACdC,QAAQ,EAAEH,GAAG,CAACI,SAAS;UACvBC,OAAO,EAAEL,GAAG,CAACM,YAAY;UACzBC,QAAQ,EAAEP,GAAG,CAACQ,WAAW;UACzBC,QAAQ,EAAET,GAAG,CAACU,QAAQ;UACtB;UACAC,UAAU,EAAEX,GAAG,CAACW,UAAU;UAC1BC,UAAU,EAAEZ,GAAG,CAACY,UAAU;UAC1BC,MAAM,EAAEC,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU,CAAC;UACpDG,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BC,UAAU,EAAEC,gBAAgB,CAAClB,GAAG,CAACmB,UAAU,CAAC;UAC5CC,UAAU,EAAEpB,GAAG,CAACmB,UAAU;UAAE;UAC5B;UACAE,KAAK,EAAErB,GAAG,CAACsB,kBAAkB,IAAItB,GAAG,CAACuB,cAAc,IAAI,8DAA8D;UACrHC,IAAI,EAAExB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAClFD,YAAY,EAAEzB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAC1FC,GAAG,EAAE3B,GAAG,CAAC2B,GAAG,IAAI,KAAK;UAAE;UACvBC,MAAM,EAAE5B,GAAG,CAAC4B,MAAM,IAAI,QAAQ;UAC9BC,WAAW,EAAE7B,GAAG,CAAC8B,eAAe,IAAI,yBAAyB;UAC7DC,SAAS,EAAE,CAAC,MAAM;YAChB,IAAI;cACF,IAAI,CAAC/B,GAAG,CAACgC,UAAU,EAAE,OAAO,EAAE;cAC9B,IAAI,OAAOhC,GAAG,CAACgC,UAAU,KAAK,QAAQ,EAAE;gBACtC,IAAIhC,GAAG,CAACgC,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;kBACzC,OAAOC,IAAI,CAACC,KAAK,CAACpC,GAAG,CAACgC,UAAU,CAAC;gBACnC,CAAC,MAAM;kBACL,OAAO,CAAChC,GAAG,CAACgC,UAAU,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACtC,GAAG,CAACgC,UAAU,CAAC,EAAE;gBACxC,OAAOhC,GAAG,CAACgC,UAAU;cACvB;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOrD,GAAG,EAAE;cACZC,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEiB,GAAG,CAAC;cAC/C,OAAO,EAAE;YACX;UACF,CAAC,EAAE,CAAC;UACJ;UACA4D,OAAO,EAAE,IAAI5C,IAAI,CAACK,GAAG,CAACwC,UAAU,IAAIxC,GAAG,CAACmB,UAAU,IAAInB,GAAG,CAACyC,WAAW,IAAI,IAAI9C,IAAI,CAAC,CAAC,CAAC;UACpF+C,KAAK,EAAE1C,GAAG,CAACE,MAAM,CAAC;QACpB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMyC,UAAU,GAAG9C,aAAa,CAAC+C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UAC9C;UACA,MAAMC,KAAK,GAAGF,CAAC,CAACN,OAAO;UACvB,MAAMS,KAAK,GAAGF,CAAC,CAACP,OAAO;UAEvB,IAAIS,KAAK,CAACpD,OAAO,CAAC,CAAC,KAAKmD,KAAK,CAACnD,OAAO,CAAC,CAAC,EAAE;YACvC,OAAOoD,KAAK,CAACpD,OAAO,CAAC,CAAC,GAAGmD,KAAK,CAACnD,OAAO,CAAC,CAAC;UAC1C;;UAEA;UACA,OAAOkD,CAAC,CAACJ,KAAK,GAAGG,CAAC,CAACH,KAAK;QAC1B,CAAC,CAAC;QAEFrF,OAAO,CAACsF,UAAU,CAAC;QACnBhF,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZC,OAAO,CAAClB,KAAK,CAAC,sBAAsB,EAAEiB,GAAG,CAAC;QAC1ChB,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAMwF,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMzD,QAAQ,GAAG,MAAMnD,UAAU,CAACe,IAAI,CAAC8F,UAAU,CAAC,CAAC;;QAEnD;QACA,MAAMC,gBAAgB,GAAG3D,QAAQ,CAACM,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UACjDC,EAAE,EAAED,GAAG,CAACE,MAAM;UACdC,QAAQ,EAAEH,GAAG,CAACI,SAAS;UACvBC,OAAO,EAAEL,GAAG,CAACM,YAAY;UACzBC,QAAQ,EAAEP,GAAG,CAACQ,WAAW;UACzBC,QAAQ,EAAET,GAAG,CAACU,QAAQ;UACtBC,UAAU,EAAEX,GAAG,CAACW,UAAU;UAC1BC,UAAU,EAAEZ,GAAG,CAACY,UAAU;UAC1BC,MAAM,EAAEC,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU,CAAC;UACpDG,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAC1BC,UAAU,EAAEC,gBAAgB,CAAClB,GAAG,CAACmB,UAAU,CAAC;UAC5CC,UAAU,EAAEpB,GAAG,CAACmB,UAAU;UAC1BE,KAAK,EAAErB,GAAG,CAACsB,kBAAkB,IAAItB,GAAG,CAACuB,cAAc,IAAI,8DAA8D;UACrHC,IAAI,EAAExB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAClFD,YAAY,EAAEzB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAC1FC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE5B,GAAG,CAAC4B,MAAM,IAAI,QAAQ;UAC9BC,WAAW,EAAE7B,GAAG,CAAC8B,eAAe,IAAI,yBAAyB;UAC7DC,SAAS,EAAE,CAAC,MAAM;YAChB,IAAI;cACF,IAAI,CAAC/B,GAAG,CAACgC,UAAU,EAAE,OAAO,EAAE;cAC9B,IAAI,OAAOhC,GAAG,CAACgC,UAAU,KAAK,QAAQ,EAAE;gBACtC,IAAIhC,GAAG,CAACgC,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;kBACzC,OAAOC,IAAI,CAACC,KAAK,CAACpC,GAAG,CAACgC,UAAU,CAAC;gBACnC,CAAC,MAAM;kBACL,OAAO,CAAChC,GAAG,CAACgC,UAAU,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACtC,GAAG,CAACgC,UAAU,CAAC,EAAE;gBACxC,OAAOhC,GAAG,CAACgC,UAAU;cACvB;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOrD,GAAG,EAAE;cACZC,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEiB,GAAG,CAAC;cAC/C,OAAO,EAAE;YACX;UACF,CAAC,EAAE,CAAC;UACJ4D,OAAO,EAAE,IAAI5C,IAAI,CAACK,GAAG,CAACmB,UAAU;QAClC,CAAC,CAAC,CAAC;QAEH5D,UAAU,CAAC4F,gBAAgB,CAAC;MAC9B,CAAC,CAAC,OAAOxE,GAAG,EAAE;QACZC,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEiB,GAAG,CAAC;QAC9C;MACF;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;IACX0D,YAAY,CAAC,CAAC;IACd;IACApF,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACAuB,MAAM,CAACgE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMtC,YAAY,GAAGA,CAACuC,GAAG,EAAEC,GAAG,KAAK;IACjC,IAAI,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,EAAE,IAAIG,KAAK,CAACC,MAAM,CAACJ,GAAG,CAAC,CAAC,MACrEC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAAIE,KAAK,CAACC,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC,EAAE;MAC3E,OAAO,YAAY;IACrB;IACA,IAAI,CAACD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,MAAMC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,CAAC,EAAE;MAC5D,OAAO,YAAY;IACrB;IACA,IAAID,GAAG,IAAIC,GAAG,EAAE;MACd,OAAO,OAAOG,MAAM,CAACJ,GAAG,CAAC,CAACK,cAAc,CAAC,CAAC,UAAUD,MAAM,CAACH,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,EAAE;IACpF;IACA,IAAIL,GAAG,EAAE;MACP,OAAO,OAAOI,MAAM,CAACJ,GAAG,CAAC,CAACK,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,IAAIJ,GAAG,EAAE;MACP,OAAO,OAAOG,MAAM,CAACH,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMxC,gBAAgB,GAAIyC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,IAAIjE,IAAI,CAACgE,UAAU,CAAC;MACvC,MAAME,GAAG,GAAG,IAAIlE,IAAI,CAAC,CAAC;;MAEtB;MACA,MAAMmE,oBAAoB,GAAG,IAAInE,IAAI,CACnCiE,UAAU,CAACG,WAAW,CAAC,CAAC,EACxBH,UAAU,CAACI,QAAQ,CAAC,CAAC,EACrBJ,UAAU,CAACK,OAAO,CAAC,CACrB,CAAC,CAACrE,OAAO,CAAC,CAAC;MAEX,MAAMsE,eAAe,GAAG,IAAIvE,IAAI,CAC9BkE,GAAG,CAACE,WAAW,CAAC,CAAC,EACjBF,GAAG,CAACG,QAAQ,CAAC,CAAC,EACdH,GAAG,CAACI,OAAO,CAAC,CACd,CAAC,CAACrE,OAAO,CAAC,CAAC;MAEX,MAAMuE,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;MACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,eAAe,GAAGJ,oBAAoB,IAAIK,QAAQ,CAAC;;MAE/E;MACA,IAAIC,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,OAAO;MAChB;;MAEA;MACA,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,WAAW;MACpB;;MAEA;MACA,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,GAAGA,OAAO,WAAW;MAC7C,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGC,IAAI,CAACE,KAAK,CAACH,OAAO,GAAG,CAAC,CAAC,YAAY;MAC/D,OAAO,GAAGC,IAAI,CAACE,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC,aAAa;IACjD,CAAC,CAAC,OAAOzF,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEiB,GAAG,EAAE,WAAW,EAAEgF,UAAU,CAAC;MACrE,OAAO,UAAU;IACnB;EACF,CAAC;;EAED;EACA,MAAMa,oBAAoB,GAAI1F,KAAK,IAAK;IACtCb,oBAAoB,CAACwG,YAAY,IAAI;MACnC,IAAIA,YAAY,CAACC,QAAQ,CAAC5F,KAAK,CAAC,EAAE;QAChC;QACA,OAAO2F,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK9F,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAG2F,YAAY,EAAE3F,KAAK,CAAC;MACjC;IACF,CAAC,CAAC;IACFf,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAM8G,YAAY,GAAGzH,IAAI,CACtBuH,MAAM,CAAC3E,GAAG,IAAI;IACb;IACA,MAAM8E,UAAU,GAAGlH,SAAS,KAAK,KAAK,IAAIoC,GAAG,CAACO,QAAQ,KAAK3C,SAAS;;IAEpE;IACA,MAAMmH,gBAAgB,GAAG/G,iBAAiB,CAACS,MAAM,KAAK,CAAC,IACpDuB,GAAG,CAAC+B,SAAS,IAAI/B,GAAG,CAAC+B,SAAS,CAACiD,IAAI,CAAClG,KAAK,IAAId,iBAAiB,CAAC0G,QAAQ,CAAC5F,KAAK,CAAC,CAAE;;IAEnF;IACA,MAAMmG,QAAQ,GAAGjF,GAAG,CAAC4B,MAAM,KAAK,UAAU;IAE1C,OAAOkD,UAAU,IAAIC,gBAAgB,IAAIE,QAAQ;EACnD,CAAC,CAAC;EACJ;EACA,MAAMC,UAAU,GAAG5H,OAAO,CAAC6H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEtC;EACA,MAAMC,cAAc,GAAGtH,WAAW,GAAGQ,WAAW;EAChD,MAAM+G,eAAe,GAAGD,cAAc,GAAG9G,WAAW;EACpD,MAAMgH,WAAW,GAAGT,YAAY,CAC7BF,MAAM,CAAC3E,GAAG,IAAIA,GAAG,CAAC2B,GAAG,KAAK,IAAI,CAAC,CAAC;EAAA,CAChCwD,KAAK,CAACE,eAAe,EAAED,cAAc,CAAC;;EAEzC;EACA,MAAMG,UAAU,GAAGlB,IAAI,CAACmB,IAAI,CAC1BX,YAAY,CAACF,MAAM,CAAC3E,GAAG,IAAIA,GAAG,CAAC2B,GAAG,KAAK,IAAI,CAAC,CAAClD,MAAM,GAAGH,WACxD,CAAC;;EAED;EACA,MAAMmH,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAIA,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAGH,UAAU,EAAE;IAC/CxH,cAAc,CAAC2H,UAAU,CAAC;IAC1B;IACAtG,MAAM,CAACgE,QAAQ,CAAC;MAAEuC,GAAG,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;;EAExB,IAAIC,SAAS,GAAG1B,IAAI,CAACf,GAAG,CAAC,CAAC,EAAExF,WAAW,GAAGuG,IAAI,CAACE,KAAK,CAACuB,YAAY,GAAG,CAAC,CAAC,CAAC;EACvE,IAAIE,OAAO,GAAG3B,IAAI,CAAChB,GAAG,CAACkC,UAAU,EAAEQ,SAAS,GAAGD,YAAY,GAAG,CAAC,CAAC;EAEhE,IAAIE,OAAO,GAAGD,SAAS,GAAG,CAAC,GAAGD,YAAY,EAAE;IAC1CC,SAAS,GAAG1B,IAAI,CAACf,GAAG,CAAC,CAAC,EAAE0C,OAAO,GAAGF,YAAY,GAAG,CAAC,CAAC;EACrD;EAEA,KAAK,IAAIG,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;IACzCJ,WAAW,CAACK,IAAI,CAACD,CAAC,CAAC;EACrB;;EAEA;EACA,MAAME,eAAe,GAAIC,OAAO,IAAK;IACnCvI,YAAY,CAACuI,OAAO,CAAC;IACrBrI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMsI,oBAAoB,GAAGA,CAAA,KAAM;IACjCpI,oBAAoB,CAAC,EAAE,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMqI,mBAAmB,GAAGA,CAAA,KAAM;IAChCnI,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMqI,kBAAkB,GAAG,MAAO7D,KAAK,IAAK;IAC1C,IAAI;MACF,MAAMrG,UAAU,CAACe,IAAI,CAACoJ,UAAU,CAAC9D,KAAK,CAAC;IACzC,CAAC,CAAC,OAAO/D,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,gCAAgC,EAAEiB,GAAG,CAAC;IACtD;EACF,CAAC;EAED,MAAM8H,kBAAkB,GAAGA,CAAC;IAAEC,KAAK,GAAG;EAAE,CAAC,KAAK;IAC9C,oBACEzJ,OAAA;MAAK0J,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpC,CAAC,GAAGvE,KAAK,CAACqE,KAAK,CAAC,CAAC,CAAC3G,GAAG,CAAC,CAAC8G,CAAC,EAAEZ,CAAC,kBAC1BhJ,OAAA;QAAa0J,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACxC3J,OAAA;UAAK0J,SAAS,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1ChK,OAAA;UAAK0J,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B3J,OAAA;YAAK0J,SAAS,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3ChK,OAAA;YAAK0J,SAAS,EAAC;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7ChK,OAAA;YAAK0J,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3J,OAAA;cAAK0J,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpChK,OAAA;cAAK0J,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpChK,OAAA;cAAK0J,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNhK,OAAA;YAAK0J,SAAS,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDhK,OAAA;YAAK0J,SAAS,EAAC;UAAiC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA,GAZEhB,CAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAEC,oBACEhK,OAAA;IAAK0J,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC3J,OAAA,CAACF,UAAU;MACTmK,KAAK,EAAC,8BAA8B;MACpCrF,WAAW,EAAC;IAAyF;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CAAC,eACFhK,OAAA;MAAK0J,SAAS,EAAC;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGvChK,OAAA;MAAS0J,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvC3J,OAAA,CAACP,UAAU;QAAAoK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGVhK,OAAA;MAAS0J,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtC3J,OAAA,CAACR,MAAM;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGVhK,OAAA;MAAK0J,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnC3J,OAAA;QAAK0J,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3J,OAAA;UAAK0J,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3J,OAAA;YAAI0J,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxChK,OAAA;YACE0J,SAAS,EAAC,mBAAmB;YAC7BQ,OAAO,EAAEb,mBAAoB;YAC7B,cAAW,gBAAgB;YAAAM,QAAA,gBAE3B3J,OAAA,CAACzB,eAAe;cAAC4L,IAAI,EAAEtL;YAAS;cAAAgL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhK,OAAA;UAAK0J,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B3J,OAAA;YACE0J,SAAS,EAAE,OAAO/I,SAAS,KAAK,KAAK,GAAG,mBAAmB,GAAG,EAAE,EAAG;YAEnEuJ,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,KAAK,CAAE;YAAAS,QAAA,EACvC;UAED,GAJM,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIL,CAAC,eACPhK,OAAA;YACE0J,SAAS,EAAE,OAAO/I,SAAS,KAAK,iBAAiB,GAAG,mBAAmB,GAAG,EAAE,EAAG;YAE/EuJ,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,iBAAiB,CAAE;YAAAS,QAAA,EACnD;UAED,GAJM,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIL,CAAC,eACPhK,OAAA;YACE0J,SAAS,EAAE,OAAO/I,SAAS,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;YAEhFuJ,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,cAAc,CAAE;YAAAS,QAAA,EAChD;UAED,GAJM,SAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIT,CAAC,eACPhK,OAAA;YACE0J,SAAS,EAAE,OAAO/I,SAAS,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;YAEhFuJ,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,cAAc,CAAE;YAAAS,QAAA,EAChD;UAED,GAJM,SAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIT,CAAC,eACPhK,OAAA;YACE0J,SAAS,EAAE,OAAO/I,SAAS,KAAK,aAAa,GAAG,2BAA2B,GAAG,EAAE,EAAG;YAEnFuJ,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,aAAa,CAAE;YAAAS,QAAA,EAC/C;UAED,GAJM,aAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhK,OAAA;QAAK0J,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,YAAU,eACrC3J,OAAA;UAAK0J,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBpJ,OAAO,gBACNP,OAAA;YAAK0J,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B3J,OAAA,CAACb,SAAS;cAACuK,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjChK,OAAA;cAAA2J,QAAA,EAAG;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,GACJ/B,UAAU,CAACzG,MAAM,GAAG,CAAC,GACvByG,UAAU,CAACnF,GAAG,CAACC,GAAG,iBAChB/C,OAAA,CAACH,aAAa;YAEZkD,GAAG,EAAEA,GAAI;YACTqH,eAAe,EAAEd;UAAmB,GAF/B,UAAUvG,GAAG,CAACC,EAAE,EAAE;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGxB,CACF,CAAC,gBAEFhK,OAAA;YAAK0J,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B3J,OAAA;cAAA2J,QAAA,EAAG;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,eAGDhK,OAAA;YAAK0J,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B3J,OAAA;cAAA2J,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBhK,OAAA;cAAA2J,QAAA,EAAG;YAAuD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DhK,OAAA;cAAKqK,GAAG,EAAC,6GAA6G;cAACC,GAAG,EAAC,YAAY;cAACZ,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvKhK,OAAA;cAAGuK,IAAI,EAAC,UAAU;cAAAZ,QAAA,eAChB3J,OAAA;gBAAQ0J,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhK,OAAA;UAAK0J,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3BlJ,KAAK,iBACJT,OAAA;YAAK0J,SAAS,EAAC,oBAAoB;YAACc,KAAK,EAAE;cACzCC,KAAK,EAAE,SAAS;cAChBC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,WAAW;cACpBC,MAAM,EAAE,WAAW;cACnBC,SAAS,EAAE,QAAQ;cACnBC,QAAQ,EAAE,MAAM;cAChBC,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE;YACb,CAAE;YAAAvB,QAAA,EACClJ;UAAK;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACAzJ,OAAO,gBACNP,OAAA;YAAK0J,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B3J,OAAA,CAACb,SAAS;cAACuK,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjChK,OAAA;cAAA2J,QAAA,EAAG;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,GACJ3B,WAAW,CAAC7G,MAAM,GAAG,CAAC,GACxB6G,WAAW,CAACvF,GAAG,CAACC,GAAG,iBACjB/C,OAAA,CAAC1B,IAAI;YAEH6M,EAAE,EAAE,QAAQpI,GAAG,CAACC,EAAE,EAAG;YACrB0G,SAAS,EAAE,gCAAgC,CAAC3G,GAAG,CAACS,QAAQ,IAAI,EAAE,EAAE4H,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAG;YACrGb,KAAK,EAAE;cAAEc,UAAU,EAAE,MAAM;cAAEC,cAAc,EAAE,MAAM;cAAEd,KAAK,EAAE,SAAS;cAAEvH,QAAQ,EAAE;YAAW,CAAE;YAC9FgH,OAAO,EAAEA,CAAA,KAAMZ,kBAAkB,CAACvG,GAAG,CAACC,EAAE,CAAE;YAAA2G,QAAA,GAEzC5G,GAAG,CAAC2B,GAAG,iBACN1E,OAAA;cAAM0J,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAChE,eACDhK,OAAA;cAAK0J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B3J,OAAA;gBAAK0J,SAAS,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhK,OAAA;cAAK0J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3J,OAAA;gBAAK0J,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAClC3J,OAAA;kBACEqK,GAAG,EAAEtH,GAAG,CAACqB,KAAM;kBACfkG,GAAG,EAAEvH,GAAG,CAACG,QAAS;kBAClBwG,SAAS,EAAC,eAAe;kBACzB8B,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACrB,GAAG,GAAG,oDAAoD;kBACrE;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhK,OAAA;gBAAK0J,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B3J,OAAA;kBAAI0J,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjC5G,GAAG,CAACG;gBAAQ;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACThK,OAAA;kBAAK0J,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClC3J,OAAA;oBAAK0J,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClC3J,OAAA;sBACEqK,GAAG,EAAEtH,GAAG,CAACwB,IAAK;sBACd+F,GAAG,EAAEvH,GAAG,CAACK,OAAQ;sBACjBoI,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;wBACvBF,CAAC,CAACC,MAAM,CAACrB,GAAG,GAAG,gCAAgC;sBACjD;oBAAE;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNhK,OAAA;oBAAK0J,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eACrC3J,OAAA;sBAAI0J,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAE5G,GAAG,CAACK;oBAAO;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFhK,OAAA;kBAAK0J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B3J,OAAA;oBAAM0J,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAChC3J,OAAA,CAACzB,eAAe;sBAAC4L,IAAI,EAAEpL;oBAAY;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAACjH,GAAG,CAACO,QAAQ;kBAAA;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACPhK,OAAA;oBAAM0J,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBACjC3J,OAAA,CAACzB,eAAe;sBAAC4L,IAAI,EAAE1L;oBAAQ;sBAAAoL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAACjH,GAAG,CAACS,QAAQ;kBAAA;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACPhK,OAAA;oBAAM0J,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC7B9F,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU;kBAAC;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNhK,OAAA;kBAAK0J,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,eACzC3J,OAAA;oBAAA2J,QAAA,EAAI5G,GAAG,CAAC6B,WAAW,GAAG7B,GAAG,CAAC6B,WAAW,CAAC7C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG;kBAA2B;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhK,OAAA;cAAK0J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3J,OAAA;gBAAM0J,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAC,GAAC,EAAC5G,GAAG,CAACiB,UAAU;cAAA;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDhK,OAAA;gBAAK0J,SAAS,EAAC,eAAe;gBAACc,KAAK,EAAE;kBAAEoB,UAAU,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,eAC3D3J,OAAA;kBACE0J,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjC;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/ED,WAAWjH,GAAG,CAACC,EAAE,EAAE;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgFpB,CACP,CAAC,gBAEFhK,OAAA;YAAK0J,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC3J,OAAA;cAAK0J,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B3J,OAAA,CAACzB,eAAe;gBAAC4L,IAAI,EAAEpL;cAAY;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNhK,OAAA;cAAA2J,QAAA,EAAI;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBhK,OAAA;cAAA2J,QAAA,EAAG;YAAuD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DhK,OAAA;cAAA2J,QAAA,EAAG;YAA+C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACrD,CAACvJ,KAAK,IAAI,CAACF,OAAO,IAAIJ,IAAI,CAACqB,MAAM,KAAK,CAAC,iBACtCxB,OAAA;cAAKwK,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAO,CAAE;cAAAlC,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGDhK,OAAA;YAAK0J,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3J,OAAA;cACE0J,SAAS,EAAC,YAAY;cAEtBQ,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC3H,WAAW,GAAG,CAAC,CAAE;cACjDiL,QAAQ,EAAEjL,WAAW,KAAK,CAAE;cAAA8I,QAAA,eAE5B3J,OAAA,CAACzB,eAAe;gBAAC4L,IAAI,EAAExL;cAAc;gBAAAkL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAJpC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CAAC,EAERpB,WAAW,CAAC9F,GAAG,CAACiJ,MAAM,iBACrB/L,OAAA;cAEE0J,SAAS,EAAE,eAAe7I,WAAW,KAAKkL,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnE7B,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAACuD,MAAM,CAAE;cAAApC,QAAA,EAEvCoC;YAAM,GAJF,QAAQA,MAAM,EAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKf,CACT,CAAC,eAEFhK,OAAA;cACE0J,SAAS,EAAC,YAAY;cAEtBQ,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC3H,WAAW,GAAG,CAAC,CAAE;cACjDiL,QAAQ,EAAEjL,WAAW,KAAKyH,UAAU,IAAIA,UAAU,KAAK,CAAE;cAAAqB,QAAA,eAEzD3J,OAAA,CAACzB,eAAe;gBAAC4L,IAAI,EAAEvL;cAAe;gBAAAiL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAJrC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CAAC,EAER1B,UAAU,GAAGO,YAAY,iBACxB7I,OAAA;cAAM0J,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAY,KAClC,EAACrB,UAAU,EAAC,QACjB;YAAA,GAFgC,MAAM;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhK,OAAA;UAAK0J,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B3J,OAAA;YAAK0J,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3J,OAAA;cAAK0J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B3J,OAAA;gBAAA2J,QAAA,EAAI;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAChCjJ,iBAAiB,CAACS,MAAM,GAAG,CAAC,iBAC3BxB,OAAA;gBAAQ0J,SAAS,EAAC,mBAAmB;gBAACQ,OAAO,EAAEd,oBAAqB;gBAAAO,QAAA,GAAC,YACzD,eAAA3J,OAAA,CAACzB,eAAe;kBAAC4L,IAAI,EAAErL;gBAAQ;kBAAA+K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNhK,OAAA;cAAK0J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BxI,kBAAkB,CAAC2B,GAAG,CAAC,CAACjB,KAAK,EAAEmK,KAAK,kBACnChM,OAAA;gBAEE0J,SAAS,EAAE,qBAAqB3I,iBAAiB,CAAC0G,QAAQ,CAAC5F,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAA8H,QAAA,gBAEtF3J,OAAA;kBACEiM,IAAI,EAAC,UAAU;kBACfjJ,EAAE,EAAE,YAAYgJ,KAAK,EAAG;kBACxBE,OAAO,EAAEnL,iBAAiB,CAAC0G,QAAQ,CAAC5F,KAAK,CAAE;kBAC3CsK,QAAQ,EAAEA,CAAA,KAAM5E,oBAAoB,CAAC1F,KAAK;gBAAE;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACFhK,OAAA;kBAAOoM,OAAO,EAAE,YAAYJ,KAAK,EAAG;kBAAC/B,KAAK,EAAEpI,KAAM;kBAAA8H,QAAA,EAC/C/H,gBAAgB,CAACC,KAAK;gBAAC;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA,GAXH,YAAYgC,KAAK,EAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYrB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhK,OAAA;cAAK0J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC9B5I,iBAAiB,CAACS,MAAM,GAAG,CAAC,gBAC3BxB,OAAA;gBAAA2J,QAAA,gBACE3J,OAAA;kBAAK0J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B3J,OAAA,CAACzB,eAAe;oBAAC4L,IAAI,EAAEtL;kBAAS;oBAAAgL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBACrC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNhK,OAAA;kBAAK0J,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAClC5I,iBAAiB,CAAC+B,GAAG,CAAC,CAACjB,KAAK,EAAEwK,GAAG,kBAChCrM,OAAA;oBAA8B0J,SAAS,EAAC,oBAAoB;oBAACO,KAAK,EAAEpI,KAAM;oBAAA8H,QAAA,GACvE/H,gBAAgB,CAACC,KAAK,EAAE,EAAE,CAAC,eAC5B7B,OAAA;sBACE0J,SAAS,EAAC,cAAc;sBACxBQ,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC1F,KAAK,CAAE;sBAAA8H,QAAA,eAE3C3J,OAAA,CAACzB,eAAe;wBAAC4L,IAAI,EAAErL;sBAAQ;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA,GAPA,YAAYqC,GAAG,EAAE;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQtB,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENhK,OAAA;gBAAK0J,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACpD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL7I,kBAAkB,CAACK,MAAM,KAAK,CAAC,iBAC9BxB,OAAA;cAAK0J,SAAS,EAAC,sBAAsB;cAACc,KAAK,EAAE;gBAC3CQ,QAAQ,EAAE,MAAM;gBAChBP,KAAK,EAAE,MAAM;gBACb6B,SAAS,EAAE,QAAQ;gBACnBvB,SAAS,EAAE,QAAQ;gBACnBF,OAAO,EAAE,MAAM;gBACfF,MAAM,EAAE,iBAAiB;gBACzBC,YAAY,EAAE,KAAK;gBACnBiB,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNhK,OAAA;YAAK0J,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B3J,OAAA;cAAA2J,QAAA,EAAI;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BhK,OAAA;cAAA2J,QAAA,EAAG;YAAkF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzFhK,OAAA;cAAKqK,GAAG,EAAC,6GAA6G;cAACC,GAAG,EAAC,aAAa;cAACZ,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzKhK,OAAA;cAAGuK,IAAI,EAAC,OAAO;cAAAZ,QAAA,eACb3J,OAAA;gBAAQ0J,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhK,OAAA,CAACN,eAAe;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL/I,mBAAmB,iBAClBjB,OAAA;MAAK0J,SAAS,EAAC,wBAAwB;MAACQ,OAAO,EAAEb;IAAoB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC5E,eAGDhK,OAAA;MAAK0J,SAAS,EAAE,kBAAkBzI,mBAAmB,GAAG,MAAM,GAAG,EAAE,EAAG;MAAA0I,QAAA,gBACpE3J,OAAA;QAAK0J,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC3J,OAAA;UAAA2J,QAAA,gBAAI3J,OAAA,CAACzB,eAAe;YAAC4L,IAAI,EAAEtL;UAAS;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDhK,OAAA;UAAQ0J,SAAS,EAAC,mBAAmB;UAACQ,OAAO,EAAEb,mBAAoB;UAAAM,QAAA,eACjE3J,OAAA,CAACzB,eAAe;YAAC4L,IAAI,EAAErL;UAAQ;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENhK,OAAA;QAAK0J,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC3J,OAAA;UAAK0J,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3J,OAAA;YAAK0J,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B3J,OAAA;cAAA2J,QAAA,EAAI;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChCjJ,iBAAiB,CAACS,MAAM,GAAG,CAAC,iBAC3BxB,OAAA;cAAQ0J,SAAS,EAAC,mBAAmB;cAACQ,OAAO,EAAEd,oBAAqB;cAAAO,QAAA,GAAC,YACzD,eAAA3J,OAAA,CAACzB,eAAe;gBAAC4L,IAAI,EAAErL;cAAQ;gBAAA+K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhK,OAAA;YAAK0J,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BxI,kBAAkB,CAAC2B,GAAG,CAAC,CAACjB,KAAK,EAAEmK,KAAK,kBACnChM,OAAA;cAEE0J,SAAS,EAAE,qBAAqB3I,iBAAiB,CAAC0G,QAAQ,CAAC5F,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cAAA8H,QAAA,gBAEtF3J,OAAA;gBACEiM,IAAI,EAAC,UAAU;gBACfjJ,EAAE,EAAE,oBAAoBgJ,KAAK,EAAG;gBAChCE,OAAO,EAAEnL,iBAAiB,CAAC0G,QAAQ,CAAC5F,KAAK,CAAE;gBAC3CsK,QAAQ,EAAEA,CAAA,KAAM5E,oBAAoB,CAAC1F,KAAK;cAAE;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACFhK,OAAA;gBAAOoM,OAAO,EAAE,oBAAoBJ,KAAK,EAAG;gBAAC/B,KAAK,EAAEpI,KAAM;gBAAA8H,QAAA,EACvD/H,gBAAgB,CAACC,KAAK;cAAC;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA,GAXH,oBAAoBgC,KAAK,EAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAY7B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhK,OAAA;YAAK0J,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B5I,iBAAiB,CAACS,MAAM,GAAG,CAAC,gBAC3BxB,OAAA;cAAA2J,QAAA,gBACE3J,OAAA;gBAAK0J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B3J,OAAA,CAACzB,eAAe;kBAAC4L,IAAI,EAAEtL;gBAAS;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACrC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhK,OAAA;gBAAK0J,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClC5I,iBAAiB,CAAC+B,GAAG,CAAC,CAACjB,KAAK,EAAEwK,GAAG,kBAChCrM,OAAA;kBAAsC0J,SAAS,EAAC,oBAAoB;kBAACO,KAAK,EAAEpI,KAAM;kBAAA8H,QAAA,GAC/E/H,gBAAgB,CAACC,KAAK,EAAE,EAAE,CAAC,eAC5B7B,OAAA;oBACE0J,SAAS,EAAC,cAAc;oBACxBQ,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC1F,KAAK,CAAE;oBAAA8H,QAAA,eAE3C3J,OAAA,CAACzB,eAAe;sBAAC4L,IAAI,EAAErL;oBAAQ;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA,GAPA,oBAAoBqC,GAAG,EAAE;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQ9B,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENhK,OAAA;cAAK0J,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACpD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAWNhK,OAAA;MAAS0J,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAClC3J,OAAA,CAACR,MAAM;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACVhK,OAAA;MAAS0J,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACrC3J,OAAA,CAACL,gBAAgB;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9J,EAAA,CAhzBID,QAAQ;AAAAsM,EAAA,GAARtM,QAAQ;AAkzBd,eAAeA,QAAQ;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}