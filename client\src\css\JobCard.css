/* Urgent tag for job cards */
.jobcard-urgent-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background: linear-gradient(45deg, #ff4757, #ff3742);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.12);
  animation: pulse 2s infinite;
}
  .job-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #f5f5f5;
      margin-bottom: 16px;
      margin-left: 0;
      width: 100%;
      transition: transform 0.2s, box-shadow 0.2s;
      display: flex;
      align-items: start !important;
      text-align: left ;
      flex-direction: column;
      height: 100%;
      padding: 0;
      gap: 0;
  }
  

  .job-card .job-card-content
  {
    text-align: left !important;
    justify-content: flex-end !important;
    align-items: flex-start !important;
  }
  
  .job-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.12);
  } 
  
  /* JobCard specific classes to avoid conflicts with JobListingPage */
  .hot-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff6b00;
    color: white;
    padding: 8px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    text-align: center;
  }
  
  .job-card .job-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background-color: #f5f5f5; /* Fallback background while image loads */
    flex-shrink: 0;
  }
  
  .job-card .job-card-image img {
    width: 100%;
    /* height: 100%; */
    object-fit: contain;
    object-position: center;
    transition: transform 0.3s ease;
    background: #fff;
    display: block;
  }
  
  .job-card:hover .job-card-image img {
    transform: scale(1.05);
  }
  
  .job-card .job-card-content {
    padding: 0px 16px 12px 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    text-align: left;
  }
  
  .job-card .company-info,
  .job-card .company-info-urgent {
    display: flex;
    align-items: center;
    text-align: left;
  }
  
  .job-card .company-logo {
    width: 30px;
    height: 30px;
    overflow: hidden;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
  
  .job-card .company-logo img,
  .job-card .company-logo-urgent img {
    width: 24px;
    height: 24px;
    object-fit: cover;
    display: block;
    margin: 0 auto;
  }
  
  .job-card .company-name,
  .job-card .company-name-urgent {
    font-size: 14px;
  color: #2d3436;
  }
  
  .job-card .company-details-urgent {
    display: flex;
    flex-direction: column;
    text-align: left;
  }
  
  .job-card .job-title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 6px;
    color: #333;
    line-height: 1.3;
    max-height: 48px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .job-card .job-meta {
    display: flex;
    font-size: 13px;
    color: #777;
    margin-bottom: 8px;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .job-card .job-location, 
  .job-card .job-time {
    display: flex;
    align-items: center;
    justify-content: start;
    margin-right: 10px;
    margin-bottom: -5px;
    text-align: left;
  }
  
  .job-card .icon {
    margin-right: 6px;
    font-size: 14px;
    width: 15px;
    height: 15px;
    color: #8057ff;
  }
  
  .job-card .icon-small {
    margin-right: 4px;
    font-size: 12px;
    color: #999;
  }
  
  .job-card .job-footer {
    display: flex;
    justify-content: space-between !important;
    align-items: center;
    margin-top: 8px;
    width: 100%;
    text-align: left !important;
  }
  
  .job-card .job-salary {
    font-weight: bold;
    color: #8057ff;
    font-size: 18px;
    margin-right: 8px;
    text-align: left !important;
  }
  
  .job-card .job-stats {
     display: flex;
     font-size: 12px;
     color: #999;
     gap: 8px;
     margin-left: auto;
     align-items: center;
   }

   .job-card .time-posted {
     margin-left: auto;
     text-align: right !important;
     justify-content: flex-end !important;
     align-items: center !important;
     flex-shrink: 0;
   }
   
   /* Override global flex-start for job-footer to maintain space-between layout */
   .job-card .job-footer {
     justify-content: space-between !important;
     align-items: center !important;
   }
  
  .job-card .view-count {
    display: flex;
    align-items: center;
  }
  
  /* Responsive adjustments */
  @media (max-width: 576px) {
    .job-card .job-title {
      font-size: 16px;
      max-height: 42px;
    }
  
    .job-card .job-card-image {
      height: 120px;
    }
    
    .job-card .job-salary {
      font-size: 18px;
    }
  }

/* Consolidated styles for job stats and time posted */
.job-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  flex-shrink: 0;
}

/* Universal time-posted styles - applies to all time elements with maximum specificity */
.time-posted,
.job-card .time-posted,
.job-card .job-date,
.job-card .job-footer .time-posted,
.job-card .job-footer .job-date,
.job-card .job-stats .time-posted,
div.job-card .time-posted,
div.job-card .job-date,
[class*="time-posted"],
[class*="job-date"] {
  font-size: 0.85rem !important;
  color: #777 !important;
  margin-left: auto !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
}

.view-count {
  font-size: 0.85rem;
  color: #777;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.views-icon {
  font-size: 0.8rem;
  color: #666;
}

/* Ensure job footer maintains proper layout with maximum specificity */
.job-card .job-footer,
div.job-card .job-footer,
.job-card > .job-footer,
[class*="job-card"] .job-footer,
[class*="job-card"] [class*="job-footer"] {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  min-height: 30px !important;
  flex-wrap: nowrap !important;
  position: relative !important;
}

/* Ensure salary stays on the left with maximum specificity */
.job-card .job-salary,
div.job-card .job-salary,
.job-card .job-footer .job-salary,
[class*="job-card"] .job-salary,
[class*="job-card"] [class*="job-salary"] {
  margin-right: auto !important;
  flex-shrink: 0 !important;
  order: 1 !important;
  position: relative !important;
  z-index: 1 !important;
  max-width: calc(100% - 120px) !important; /* Leave space for date */
}

/* Ensure time/stats stay on the right with maximum specificity */
.job-card .job-footer > *:last-child,
div.job-card .job-footer > *:last-child,
.job-card .job-footer .time-posted,
.job-card .job-footer .job-date,
.job-card .job-footer .job-stats,
[class*="job-card"] .job-footer > *:last-child,
[class*="job-card"] [class*="time-posted"],
[class*="job-card"] [class*="job-date"] {
  margin-left: auto !important;
  flex-shrink: 0 !important;
  order: 2 !important;
  text-align: right !important;
  justify-self: flex-end !important;
}

/* Company info container */
.job-card .company-info-urgent {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  text-align: left;
}

/* Additional overrides for common conflicts */
.job-card * {
  box-sizing: border-box;
}

/* Prevent any margin/padding issues */
.job-card .job-footer * {
  margin: 0;
  padding: 0;
}

.job-card .job-footer .time-posted,
.job-card .job-footer .job-date {
  margin: 0 0 0 auto !important;
  padding: 0 !important;
}

/* Final catch-all rule for time positioning - use flexbox approach */
.job-card .job-footer {
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

/* Force time elements to the right with maximum specificity */
.job-card .job-footer > span:last-child,
.job-card .job-footer > div:last-child,
.job-card .job-footer > p:last-child,
.job-card .job-footer > small:last-child,
.job-card .job-footer .job-date,
.job-card .job-footer div.job-date {
  margin-left: auto !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  order: 999 !important;
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
}

/* Target elements containing time-related text */
.job-card .job-footer *:contains("Today"),
.job-card .job-footer *:contains("ago"),
.job-card .job-footer *:contains("hour"),
.job-card .job-footer *:contains("day"),
.job-card .job-footer *:contains("week"),
.job-card .job-footer *:contains("month") {
  margin-left: auto !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  order: 999 !important;
}

/* Ultra-specific rule for the exact JobCard structure */
.job-card .job-footer .job-salary + .job-date,
.job-card .job-card-content .job-footer .job-date,
div.job-card div.job-footer div.job-date {
  margin-left: auto !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  order: 999 !important;
  position: relative !important;
  float: right !important;
  display: block !important;
}

/* ULTIMATE FIX - Maximum specificity for job-date positioning */
html body div.job-card div.job-card-content div.job-footer div.job-date,
html body .job-card .job-card-content .job-footer .job-date,
html body div[class*="job-card"] div[class*="job-footer"] div[class*="job-date"],
html body div.job-card div.job-footer div.job-date,
html body .job-card .job-footer .job-date,
div.job-card div.job-footer div.job-date,
.job-card .job-footer .job-date {
  margin: 0 !important;
  margin-left: auto !important;
  text-align: right !important;
  flex-shrink: 0 !important;
  order: 999 !important;
  position: absolute !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  padding-right: 16px !important;
  z-index: 100 !important;
  background-color: rgba(255, 0, 0, 0.2) !important; /* Debug red background */
  border: 2px solid red !important; /* Debug red border */
  min-width: 80px !important;
}

/* Ensure parent footer has relative positioning for absolute child */
html body div.job-card div.job-card-content div.job-footer,
html body .job-card .job-card-content .job-footer {
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  min-height: 30px !important;
}