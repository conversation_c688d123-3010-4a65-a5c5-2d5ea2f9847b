/* Sub Topics Admin Styles */
.subtopics-admin-container {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.subtopics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtopics-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3498db;
}

.subtopic-header-actions {
  display: flex;
  gap: 12px;
}

.refresh-button,
.add-subtopic-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.refresh-button {
  background-color: #6c757d;
  color: white;
}

.refresh-button:hover:not(:disabled) {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.add-subtopic-button {
  background-color: #28a745;
  color: white;
}

.add-subtopic-button:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.refresh-button:disabled,
.add-subtopic-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid #f5c6cb;
}

.filters-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-container {
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
  gap: 16px;
}

.subtopics-table {
  width: 100%;
  border-collapse: collapse;
}

.subtopics-table th {
  background-color: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  font-size: 14px;
}

.subtopics-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.subtopics-table tbody tr:hover {
  background-color: #f8f9fa;
}

.subtopic-name {
  font-weight: 500;
  color: #2c3e50;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.subtopic-description {
  color: #6c757d;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.action-button {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.edit-button {
  background-color: #ffc107;
  color: #212529;
}

.edit-button:hover:not(:disabled) {
  background-color: #e0a800;
  transform: translateY(-1px);
}

.delete-button {
  background-color: #dc3545;
  color: white;
}

.delete-button:hover:not(:disabled) {
  background-color: #c82333;
  transform: translateY(-1px);
}

.activate-button {
  background-color: #28a745;
  color: white;
}

.activate-button:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.deactivate-button {
  background-color: #6c757d;
  color: white;
}

.deactivate-button:hover:not(:disabled) {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.no-subtopics-message {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 40px 20px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.subtopic-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-icon {
  color: #3498db;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.modal-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.cancel-button,
.submit-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-button {
  background-color: #6c757d;
  color: white;
}

.cancel-button:hover {
  background-color: #5a6268;
}

.submit-button {
  background-color: #28a745;
  color: white;
}

.submit-button:hover {
  background-color: #218838;
}

/* Responsive Design */
@media (max-width: 768px) {
  .subtopics-admin-container {
    padding: 10px;
  }
  
  .subtopics-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .subtopic-header-actions {
    justify-content: center;
  }
  
  .subtopics-table {
    font-size: 12px;
  }
  
  .subtopics-table th,
  .subtopics-table td {
    padding: 8px 6px;
  }
  
  .subtopic-modal {
    width: 95%;
    margin: 10px;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 15px;
  }
}
