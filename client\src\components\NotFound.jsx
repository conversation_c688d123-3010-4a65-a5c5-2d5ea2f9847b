import React from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHome, faSearch, faBriefcase, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import '../css/NotFound.css';
import PageHelmet from './PageHelmet';

/**
 * NotFound Component - 404 Error Page
 * Displays when user navigates to a non-existent route
 * Provides navigation options to help users find what they're looking for
 */
const NotFound = () => {
  return (
    <>
      <PageHelmet 
        title="Page Not Found - 404 Error"
        description="The page you're looking for doesn't exist. Find jobs and opportunities on our job portal."
      />
      
      <div className="not-found-container">
        <div className="not-found-content">
          {/* Error Icon and Code */}
          <div className="error-icon-section">
            <FontAwesomeIcon icon={faExclamationTriangle} className="error-icon" />
            <h1 className="error-code">404</h1>
          </div>
          
          {/* Main Error Message */}
          <div className="error-message-section">
            <h2 className="error-title">Oops! Page Not Found</h2>
            <p className="error-description">
              The page you're looking for doesn't exist or has been moved. 
              Don't worry, let's get you back on track!
            </p>
          </div>
          
          {/* Navigation Options */}
          <div className="navigation-options">
            <Link to="/" className="nav-button primary">
              <FontAwesomeIcon icon={faHome} />
              Go to Homepage
            </Link>
            
            <Link to="/browse" className="nav-button secondary">
              <FontAwesomeIcon icon={faSearch} />
              Browse Jobs
            </Link>
            
            <Link to="/company" className="nav-button secondary">
              <FontAwesomeIcon icon={faBriefcase} />
              View Companies
            </Link>
          </div>
          
          {/* Helpful Links */}
          <div className="helpful-links">
            <h3>Popular Pages</h3>
            <div className="links-grid">
              <Link to="/blogs" className="helpful-link">
                Job Blog & Articles
              </Link>
              <Link to="/about" className="helpful-link">
                About Us
              </Link>
              <Link to="/contact" className="helpful-link">
                Contact Support
              </Link>
              <Link to="/faq" className="helpful-link">
                FAQ
              </Link>
            </div>
          </div>
          
          {/* Search Suggestion */}
          <div className="search-suggestion">
            <p>Or try searching for what you need:</p>
            <div className="search-box">
              <input 
                type="text" 
                placeholder="Search jobs, companies, or topics..."
                className="search-input"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    window.location.href = `/browse?search=${encodeURIComponent(e.target.value.trim())}`;
                  }
                }}
              />
              <button 
                className="search-button"
                onClick={(e) => {
                  const input = e.target.previousElementSibling;
                  if (input.value.trim()) {
                    window.location.href = `/browse?search=${encodeURIComponent(input.value.trim())}`;
                  }
                }}
              >
                <FontAwesomeIcon icon={faSearch} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotFound;