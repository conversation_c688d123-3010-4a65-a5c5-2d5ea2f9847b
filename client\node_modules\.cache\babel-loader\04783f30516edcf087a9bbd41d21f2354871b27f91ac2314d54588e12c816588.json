{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\HomePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../css/HomePage.css';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faMapMarkerAlt, faClock, faEye, faChevronLeft, faChevronRight, faFilter, faTimes, faBriefcase, faBookmark, faShare, faBars, faSync } from '@fortawesome/free-solid-svg-icons';\nimport { FaSpinner } from 'react-icons/fa';\nimport ApiService from '../services/apiService';\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../utils/subTopicsUtils';\nimport NewsLetter from './NewsLetter';\nimport Banner from './Banner';\nimport SearchArea from './SearchArea';\nimport FeaturedSection from './FeaturedSection';\nimport SocialMediaFeeds from './SocialMediaFeeds';\nimport FeaturedCompanies from './FeaturedCompanies';\nimport UrgentJobCard from './UrgentJobCard';\nimport PageHelmet from './PageHelmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const [jobs, setJobs] = useState([]);\n  const [hotJobs, setHotJobs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [activeTab, setActiveTab] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [selectedSubTopics, setSelectedSubTopics] = useState([]);\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\n  const jobsPerPage = 3;\n\n  // Load available sub-topics from localStorage\n  const loadSubTopicsFromStorage = () => {\n    try {\n      const activeSubTopicNames = getActiveSubTopicNames();\n      if (activeSubTopicNames.length > 0) {\n        setAvailableSubTopics(activeSubTopicNames);\n      } else {\n        // Use default sub-topics if no saved data\n        const defaultSubTopics = getDefaultSubTopics();\n        setAvailableSubTopics(defaultSubTopics);\n      }\n    } catch (err) {\n      console.error(\"Error loading sub-topics from localStorage:\", err);\n      // Fallback to default sub-topics\n      const defaultSubTopics = getDefaultSubTopics();\n      setAvailableSubTopics(defaultSubTopics);\n    }\n  };\n\n  // Function to shorten long topic names\n  const shortenTopicName = (topic, maxLength = 30) => {\n    if (topic.length <= maxLength) {\n      return topic;\n    }\n    return topic.substring(0, maxLength) + '...';\n  };\n\n  // Initialize available sub-topics\n  useEffect(() => {\n    loadSubTopicsFromStorage();\n  }, []);\n\n  // Listen for sub-topics updates from SubTopicsAdmin\n  useEffect(() => {\n    const handleSubTopicsUpdate = event => {\n      console.log(\"Sub-topics updated in HomePage, refreshing available options...\");\n      loadSubTopicsFromStorage();\n    };\n\n    // Add event listener for sub-topics updates\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n    };\n  }, []);\n\n  // Fetch jobs from API\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        // Add a timestamp parameter to prevent caching\n        const response = await ApiService.jobs.getAll({\n          _t: new Date().getTime()\n        });\n\n        // Transform backend data to match the format used in frontend\n        const formattedJobs = response.data.map(job => ({\n          id: job.job_id,\n          position: job.job_title,\n          company: job.company_name,\n          location: job.main_topics,\n          workTime: job.job_type,\n          // Pass min and max salary for display\n          min_salary: job.min_salary,\n          max_salary: job.max_salary,\n          salary: formatSalary(job.min_salary, job.max_salary),\n          views: job.view_count || 0,\n          // Use actual view count instead of random number\n          postedTime: formatPostedDate(job.start_date),\n          datePosted: job.start_date,\n          // Store the original date string for debugging\n          // Ensure we have valid image paths or fallbacks\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          hot: job.hot || false,\n          // Default if not in database\n          status: job.status || 'Active',\n          description: job.job_description || 'No description provided',\n          subTopics: (() => {\n            try {\n              if (!job.sub_topics) return [];\n              if (typeof job.sub_topics === 'string') {\n                if (job.sub_topics.trim().startsWith('[')) {\n                  return JSON.parse(job.sub_topics);\n                } else {\n                  return [job.sub_topics];\n                }\n              } else if (Array.isArray(job.sub_topics)) {\n                return job.sub_topics;\n              }\n              return [];\n            } catch (err) {\n              console.error(\"Error parsing sub_topics:\", err);\n              return [];\n            }\n          })(),\n          // Add a Date object for sorting purposes\n          dateObj: new Date(job.start_date)\n        }));\n\n        // Sort jobs with newest first (by date posted)\n        const sortedJobs = formattedJobs.sort((a, b) => b.dateObj - a.dateObj);\n        setJobs(sortedJobs);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching jobs:\", err);\n        setError(\"Failed to load jobs from server\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    const fetchHotJobs = async () => {\n      try {\n        const response = await ApiService.jobs.getHotJobs();\n\n        // Transform hot jobs data to match the format used in frontend\n        const formattedHotJobs = response.data.map(job => ({\n          id: job.job_id,\n          position: job.job_title,\n          company: job.company_name,\n          location: job.main_topics,\n          workTime: job.job_type,\n          min_salary: job.min_salary,\n          max_salary: job.max_salary,\n          salary: formatSalary(job.min_salary, job.max_salary),\n          views: job.view_count || 0,\n          postedTime: formatPostedDate(job.start_date),\n          datePosted: job.start_date,\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          hot: true,\n          status: job.status || 'Active',\n          description: job.job_description || 'No description provided',\n          subTopics: (() => {\n            try {\n              if (!job.sub_topics) return [];\n              if (typeof job.sub_topics === 'string') {\n                if (job.sub_topics.trim().startsWith('[')) {\n                  return JSON.parse(job.sub_topics);\n                } else {\n                  return [job.sub_topics];\n                }\n              } else if (Array.isArray(job.sub_topics)) {\n                return job.sub_topics;\n              }\n              return [];\n            } catch (err) {\n              console.error(\"Error parsing sub_topics:\", err);\n              return [];\n            }\n          })(),\n          dateObj: new Date(job.start_date)\n        }));\n        setHotJobs(formattedHotJobs);\n      } catch (err) {\n        console.error(\"Error fetching hot jobs:\", err);\n        // Don't set error for hot jobs failure, just log it\n      }\n    };\n    fetchJobs();\n    fetchHotJobs();\n    // Set default tab to \"All Jobs\"\n    setActiveTab('all');\n\n    // Scroll to top when component mounts\n    window.scrollTo(0, 0);\n  }, []);\n\n  // Helper function to format min/max salary as \"Rs. min - Rs. max\"\n  const formatSalary = (min, max) => {\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) && (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\n      return 'Negotiable';\n    }\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\n      return 'Negotiable';\n    }\n    if (min && max) {\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\n    }\n    if (min) {\n      return `Rs. ${Number(min).toLocaleString()}`;\n    }\n    if (max) {\n      return `Rs. ${Number(max).toLocaleString()}`;\n    }\n    return 'Negotiable';\n  };\n\n  // Helper function to format posted date\n  const formatPostedDate = dateString => {\n    if (!dateString) return 'Recently';\n    try {\n      // Parse the date from the server\n      const postedDate = new Date(dateString);\n      const now = new Date();\n\n      // Normalize both dates to midnight in local timezone to compare just the dates\n      const postedDateNormalized = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate()).getTime();\n      const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();\n      const oneDayMs = 24 * 60 * 60 * 1000; // One day in milliseconds\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\n\n      // Check if it's today\n      if (daysAgo === 0) {\n        return 'Today';\n      }\n\n      // Check if it's yesterday\n      if (daysAgo === 1) {\n        return 'Yesterday';\n      }\n\n      // Calculate exact days difference\n      if (daysAgo < 7) return `${daysAgo} days ago`;\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\n      return `${Math.floor(daysAgo / 30)} months ago`;\n    } catch (err) {\n      console.error(\"Error formatting date:\", err, \"for date:\", dateString);\n      return 'Recently';\n    }\n  };\n\n  // Handle subtopic selection\n  const handleSubTopicChange = topic => {\n    setSelectedSubTopics(prevSelected => {\n      if (prevSelected.includes(topic)) {\n        // Remove topic if already selected (uncheck)\n        return prevSelected.filter(t => t !== topic);\n      } else {\n        // Add topic if not already selected (check)\n        return [...prevSelected, topic];\n      }\n    });\n    setCurrentPage(1); // Reset to first page when changing filters\n  };\n\n  // Filter jobs based on active tab and selected subtopics\n  const filteredJobs = jobs.filter(job => {\n    // Filter by tab\n    const matchesTab = activeTab === 'all' || job.location === activeTab;\n\n    // Filter by sub topics if any are selected\n    const matchesSubTopics = selectedSubTopics.length === 0 || job.subTopics && job.subTopics.some(topic => selectedSubTopics.includes(topic));\n\n    // Only include active jobs\n    const isActive = job.status !== 'Inactive';\n    return matchesTab && matchesSubTopics && isActive;\n  });\n  // Filter urgent/hot jobs for the left sidebar - use hotJobs state\n  const urgentJobs = hotJobs.slice(0, 2);\n\n  // Get paginated jobs - only show 5 per page\n  const indexOfLastJob = currentPage * jobsPerPage;\n  const indexOfFirstJob = indexOfLastJob - jobsPerPage;\n  const currentJobs = filteredJobs.filter(job => job.hot !== true) // Don't show hot jobs in the regular listings\n  .slice(indexOfFirstJob, indexOfLastJob);\n\n  // Calculate total pages\n  const totalPages = Math.ceil(filteredJobs.filter(job => job.hot !== true).length / jobsPerPage);\n\n  // Change page\n  const handlePageChange = pageNumber => {\n    if (pageNumber < 1 || pageNumber > totalPages) return;\n    setCurrentPage(pageNumber);\n    // Scroll to top of job listings\n    window.scrollTo({\n      top: 400,\n      behavior: 'smooth'\n    });\n  };\n\n  // Generate page numbers for pagination\n  const pageNumbers = [];\n  const displayRange = 5; // How many page numbers to show at once\n\n  let startPage = Math.max(1, currentPage - Math.floor(displayRange / 2));\n  let endPage = Math.min(totalPages, startPage + displayRange - 1);\n  if (endPage - startPage + 1 < displayRange) {\n    startPage = Math.max(1, endPage - displayRange + 1);\n  }\n  for (let i = startPage; i <= endPage; i++) {\n    pageNumbers.push(i);\n  }\n\n  // Change active tab\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n    setCurrentPage(1); // Reset to first page when changing tabs\n  };\n\n  // Clear all selected subtopics\n  const clearSubTopicFilters = () => {\n    setSelectedSubTopics([]);\n  };\n\n  // Toggle filter sidebar\n  const toggleFilterSidebar = () => {\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\n  };\n\n  // Function to increment view count\n  const incrementViewCount = async jobId => {\n    try {\n      await ApiService.jobs.recordView(jobId);\n    } catch (err) {\n      console.error(\"Error incrementing view count:\", err);\n    }\n  };\n  const JobLoadingSkeleton = ({\n    count = 3\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"jobs-loading-container\",\n      children: [...Array(count)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-card-skeleton\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-thumbnail\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skeleton-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line title\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line company\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-tag\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-line description short\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homepage-container\",\n    children: [/*#__PURE__*/_jsxDEV(PageHelmet, {\n      title: \"Your Path to the Perfect Job\",\n      description: \"Find your dream job across all sectors - Government, Private, Foreign, and Internships.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-spacing\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-search-area\",\n      children: /*#__PURE__*/_jsxDEV(SearchArea, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-top-banner\",\n      children: /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"home-content-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"main-title\",\n            children: \"New Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-toggle-btn\",\n            onClick: toggleFilterSidebar,\n            \"aria-label\": \"Toggle filters\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faFilter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this), \"Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-tabs\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'all' ? 'active all-active' : ''}`,\n            onClick: () => handleTabChange('all'),\n            children: \"All Jobs\"\n          }, \"all\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Government Jobs' ? 'active gov-active' : ''}`,\n            onClick: () => handleTabChange('Government Jobs'),\n            children: \"Government Jobs\"\n          }, \"gov\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Private Jobs' ? 'active private-active' : ''}`,\n            onClick: () => handleTabChange('Private Jobs'),\n            children: \"Private Jobs\"\n          }, \"private\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Foreign Jobs' ? 'active foreign-active' : ''}`,\n            onClick: () => handleTabChange('Foreign Jobs'),\n            children: \"Foreign Jobs\"\n          }, \"foreign\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `tab ${activeTab === 'Internships' ? 'active internships-active' : ''}`,\n            onClick: () => handleTabChange('Internships'),\n            children: \"Internships\"\n          }, \"internships\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"home-layout\",\n        children: [\"          \", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"left-column\",\n          children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-overlay\",\n            children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading urgent jobs...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this) : hotJobs.length > 0 ? hotJobs.slice(0, 2).map(job => /*#__PURE__*/_jsxDEV(UrgentJobCard, {\n            job: job,\n            onViewIncrement: incrementViewCount\n          }, `urgent-${job.id}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-urgent-jobs\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No urgent jobs available right now.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"recruiting-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Recruiting?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get your job postings seen by thousands of job seekers.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\",\n              alt: \"Recruiting\",\n              className: \"recruiting-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/contact\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"post-job-btn\",\n                children: \"Post a Job\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"middle-column\",\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"error-notification\",\n            style: {\n              color: '#e74c3c',\n              backgroundColor: '#fdf2f2',\n              border: '1px solid #f5c6cb',\n              borderRadius: '8px',\n              padding: '12px 16px',\n              margin: '16px auto',\n              textAlign: 'center',\n              fontSize: '14px',\n              maxWidth: '400px',\n              boxShadow: '0 2px 4px rgba(231, 76, 60, 0.1)'\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-overlay\",\n            children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading available positions...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this) : currentJobs.length > 0 ? currentJobs.map(job => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/job/${job.id}`,\n            className: `job-card-redesigned job-type-${(job.workTime || '').toLowerCase().replace(/\\s+/g, '-')}`,\n            style: {\n              background: \"#fff\",\n              textDecoration: \"none\",\n              color: \"inherit\",\n              position: \"relative\"\n            },\n            onClick: () => incrementViewCount(job.id),\n            children: [job.hot && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"urgent-tag jobcard-urgent-tag\",\n              children: \"\\uD83D\\uDD25 URGENT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-header\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"company-info-header\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"job-image-container\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: job.image,\n                  alt: job.position,\n                  className: \"job-thumbnail\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = 'https://via.placeholder.com/300x300?text=Job+Image';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"job-main-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"job-title-redesigned\",\n                  children: job.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"company-info-urgent\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-logo-urgent\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: job.logo,\n                      alt: job.company,\n                      onError: e => {\n                        e.target.onerror = null;\n                        e.target.src = 'https://via.placeholder.com/30';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-details-urgent\",\n                    children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"company-name-urgent\",\n                      children: job.company\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 556,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-meta-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag location\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faBriefcase\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 27\n                    }, this), \" \", job.location]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag work-type\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faClock\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 27\n                    }, this), \" \", job.workTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"job-tag salary\",\n                    children: formatSalary(job.min_salary, job.max_salary)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-description-redesigned\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: job.description ? job.description.substring(0, 140) + '...' : 'No description available.'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"job-card-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"posting-date\",\n                children: [\" \", job.postedTime]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"apply-section\",\n                style: {\n                  marginLeft: \"auto\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"apply-btn-redesigned\",\n                  children: \"Apply Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this)]\n          }, `regular-${job.id}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-jobs-redesigned\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-jobs-icon\",\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faBriefcase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"No Matching Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"There are no jobs available in this category right now.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Try adjusting your filters or check back later.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this), !error && !loading && jobs.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: 'gray',\n                marginTop: '16px'\n              },\n              children: \"No jobs found. Please check your API server or try again later.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pagination\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"page-arrow\",\n              onClick: () => handlePageChange(currentPage - 1),\n              disabled: currentPage === 1,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faChevronLeft\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this)\n            }, \"prev\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `page-number ${currentPage === number ? 'active' : ''}`,\n              onClick: () => handlePageChange(number),\n              children: number\n            }, `page-${number}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"page-arrow\",\n              onClick: () => handlePageChange(currentPage + 1),\n              disabled: currentPage === totalPages || totalPages === 0,\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faChevronRight\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, \"next\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), totalPages > displayRange && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"page-info\",\n              children: [\"of \", totalPages, \" pages\"]\n            }, \"more\", true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"right-column\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtopics-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Filter by Specialization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"refresh-subtopics-btn\",\n                  onClick: loadSubTopicsFromStorage,\n                  title: \"Refresh sub-topics list\",\n                  style: {\n                    background: 'none',\n                    border: '1px solid #ddd',\n                    borderRadius: '4px',\n                    padding: '4px 8px',\n                    cursor: 'pointer',\n                    fontSize: '12px',\n                    color: '#666',\n                    marginRight: '8px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faSync\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 19\n                }, this), selectedSubTopics.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"clear-filters-btn\",\n                  onClick: clearSubTopicFilters,\n                  children: [\"Clear all \", /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faTimes\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtopics-list\",\n              children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: `subtopic-${index}`,\n                  checked: selectedSubTopics.includes(topic),\n                  onChange: () => handleSubTopicChange(topic)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `subtopic-${index}`,\n                  title: topic,\n                  children: shortenTopicName(topic)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this)]\n              }, `subtopic-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"selected-filters\",\n              children: selectedSubTopics.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filters-label\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFilter\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 23\n                  }, this), \" Applied filters:\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"selected-topics-list\",\n                  children: selectedSubTopics.map((topic, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"selected-topic-tag\",\n                    title: topic,\n                    children: [shortenTopicName(topic, 20), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"remove-topic\",\n                      onClick: () => handleSubTopicChange(topic),\n                      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faTimes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 27\n                    }, this)]\n                  }, `selected-${idx}`, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"no-filters\",\n                children: \"No filters applied\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeaturedSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), isFilterSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-sidebar-overlay\",\n      onClick: toggleFilterSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `filter-sidebar ${isFilterSidebarOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faFilter\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 15\n          }, this), \" Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-sidebar-btn\",\n          onClick: toggleFilterSidebar,\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faTimes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subtopics-box\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Filter by Specialization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this), selectedSubTopics.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"clear-filters-btn\",\n              onClick: clearSubTopicFilters,\n              children: [\"Clear all \", /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faTimes\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subtopics-list\",\n            children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: `sidebar-subtopic-${index}`,\n                checked: selectedSubTopics.includes(topic),\n                onChange: () => handleSubTopicChange(topic)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: `sidebar-subtopic-${index}`,\n                title: topic,\n                children: shortenTopicName(topic)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this)]\n            }, `sidebar-subtopic-${index}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-filters\",\n            children: selectedSubTopics.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filters-label\",\n                children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                  icon: faFilter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 21\n                }, this), \" Applied filters:\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-topics-list\",\n                children: selectedSubTopics.map((topic, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"selected-topic-tag\",\n                  title: topic,\n                  children: [shortenTopicName(topic, 20), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"remove-topic\",\n                    onClick: () => handleSubTopicChange(topic),\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTimes\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this)]\n                }, `sidebar-selected-${idx}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-filters\",\n              children: \"No filters applied\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"homepage-banner\",\n      children: /*#__PURE__*/_jsxDEV(Banner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"social-media-feeds\",\n      children: /*#__PURE__*/_jsxDEV(SocialMediaFeeds, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 812,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 384,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"Ems2Y8x7RztFy8ARBdCl3gVsv+Q=\");\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "FontAwesomeIcon", "faMapMarkerAlt", "faClock", "faEye", "faChevronLeft", "faChevronRight", "faFilter", "faTimes", "faBriefcase", "faBookmark", "faShare", "faBars", "faSync", "FaSpinner", "ApiService", "getActiveSubTopicNames", "getDefaultSubTopics", "NewsLetter", "Banner", "SearchArea", "FeaturedSection", "SocialMediaFeeds", "FeaturedCompanies", "UrgentJobCard", "PageHelmet", "jsxDEV", "_jsxDEV", "HomePage", "_s", "jobs", "setJobs", "hotJobs", "setHotJobs", "loading", "setLoading", "error", "setError", "activeTab", "setActiveTab", "currentPage", "setCurrentPage", "selectedSubTopics", "setSelectedSubTopics", "isFilterSidebarOpen", "setIsFilterSidebarOpen", "availableSubTopics", "setAvailableSubTopics", "jobsPerPage", "loadSubTopicsFromStorage", "activeSubTopicNames", "length", "defaultSubTopics", "err", "console", "shortenTopicName", "topic", "max<PERSON><PERSON><PERSON>", "substring", "handleSubTopicsUpdate", "event", "log", "window", "addEventListener", "removeEventListener", "fetchJobs", "response", "getAll", "_t", "Date", "getTime", "formattedJobs", "data", "map", "job", "id", "job_id", "position", "job_title", "company", "company_name", "location", "main_topics", "workTime", "job_type", "min_salary", "max_salary", "salary", "formatSalary", "views", "view_count", "postedTime", "formatPostedDate", "start_date", "datePosted", "image", "job_post_thumbnail", "job_post_image", "logo", "company_logo", "company_logo_url", "hot", "status", "description", "job_description", "subTopics", "sub_topics", "trim", "startsWith", "JSON", "parse", "Array", "isArray", "date<PERSON><PERSON>j", "sortedJobs", "sort", "a", "b", "fetchHotJobs", "getHotJobs", "formattedHotJobs", "scrollTo", "min", "max", "undefined", "isNaN", "Number", "toLocaleString", "dateString", "postedDate", "now", "postedDateNormalized", "getFullYear", "getMonth", "getDate", "todayNormalized", "oneDayMs", "daysAgo", "Math", "round", "floor", "handleSubTopicChange", "prevSelected", "includes", "filter", "t", "filteredJobs", "matchesTab", "matchesSubTopics", "some", "isActive", "urgentJobs", "slice", "indexOfLastJob", "indexOfFirstJob", "currentJobs", "totalPages", "ceil", "handlePageChange", "pageNumber", "top", "behavior", "pageNumbers", "displayRange", "startPage", "endPage", "i", "push", "handleTabChange", "tabName", "clearSubTopicFilters", "toggleFilterSidebar", "incrementViewCount", "jobId", "recordView", "JobLoadingSkeleton", "count", "className", "children", "_", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "icon", "onViewIncrement", "src", "alt", "href", "style", "color", "backgroundColor", "border", "borderRadius", "padding", "margin", "textAlign", "fontSize", "max<PERSON><PERSON><PERSON>", "boxShadow", "to", "toLowerCase", "replace", "background", "textDecoration", "onError", "e", "target", "onerror", "marginLeft", "marginTop", "disabled", "number", "cursor", "marginRight", "index", "type", "checked", "onChange", "htmlFor", "idx", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/HomePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport '../css/HomePage.css';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faMapMarkerAlt, faClock, faEye, faChevronLeft, faChevronRight, faFilter, faTimes, faBriefcase, faBookmark, faShare, faBars, faSync } from '@fortawesome/free-solid-svg-icons';\r\nimport { FaSpinner } from 'react-icons/fa';\r\nimport ApiService from '../services/apiService';\r\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../utils/subTopicsUtils';\r\nimport NewsLetter from './NewsLetter';\r\nimport Banner from './Banner';\r\nimport SearchArea from './SearchArea';\r\nimport FeaturedSection from './FeaturedSection';\r\nimport SocialMediaFeeds from './SocialMediaFeeds';\r\nimport FeaturedCompanies from './FeaturedCompanies';\r\nimport UrgentJobCard from './UrgentJobCard';\r\nimport PageHelmet from './PageHelmet';\r\n\r\nconst HomePage = () => {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [hotJobs, setHotJobs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('all');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedSubTopics, setSelectedSubTopics] = useState([]);\r\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\r\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\r\n  const jobsPerPage = 3;\r\n\r\n  // Load available sub-topics from localStorage\r\n  const loadSubTopicsFromStorage = () => {\r\n    try {\r\n      const activeSubTopicNames = getActiveSubTopicNames();\r\n      if (activeSubTopicNames.length > 0) {\r\n        setAvailableSubTopics(activeSubTopicNames);\r\n      } else {\r\n        // Use default sub-topics if no saved data\r\n        const defaultSubTopics = getDefaultSubTopics();\r\n        setAvailableSubTopics(defaultSubTopics);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error loading sub-topics from localStorage:\", err);\r\n      // Fallback to default sub-topics\r\n      const defaultSubTopics = getDefaultSubTopics();\r\n      setAvailableSubTopics(defaultSubTopics);\r\n    }\r\n  };\r\n\r\n  // Function to shorten long topic names\r\n  const shortenTopicName = (topic, maxLength = 30) => {\r\n    if (topic.length <= maxLength) {\r\n      return topic;\r\n    }\r\n    return topic.substring(0, maxLength) + '...';\r\n  };\r\n\r\n  // Initialize available sub-topics\r\n  useEffect(() => {\r\n    loadSubTopicsFromStorage();\r\n  }, []);\r\n\r\n  // Listen for sub-topics updates from SubTopicsAdmin\r\n  useEffect(() => {\r\n    const handleSubTopicsUpdate = (event) => {\r\n      console.log(\"Sub-topics updated in HomePage, refreshing available options...\");\r\n      loadSubTopicsFromStorage();\r\n    };\r\n\r\n    // Add event listener for sub-topics updates\r\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Fetch jobs from API\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        // Add a timestamp parameter to prevent caching\r\n        const response = await ApiService.jobs.getAll({ _t: new Date().getTime() });\r\n        \r\n        // Transform backend data to match the format used in frontend\r\n        const formattedJobs = response.data.map(job => ({\r\n          id: job.job_id,\r\n          position: job.job_title,\r\n          company: job.company_name,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          // Pass min and max salary for display\r\n          min_salary: job.min_salary,\r\n          max_salary: job.max_salary,\r\n          salary: formatSalary(job.min_salary, job.max_salary),\r\n          views: job.view_count || 0, // Use actual view count instead of random number\r\n          postedTime: formatPostedDate(job.start_date),\r\n          datePosted: job.start_date, // Store the original date string for debugging\r\n          // Ensure we have valid image paths or fallbacks\r\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\r\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          hot: job.hot || false, // Default if not in database\r\n          status: job.status || 'Active',\r\n          description: job.job_description || 'No description provided',\r\n          subTopics: (() => {\r\n            try {\r\n              if (!job.sub_topics) return [];\r\n              if (typeof job.sub_topics === 'string') {\r\n                if (job.sub_topics.trim().startsWith('[')) {\r\n                  return JSON.parse(job.sub_topics);\r\n                } else {\r\n                  return [job.sub_topics];\r\n                }\r\n              } else if (Array.isArray(job.sub_topics)) {\r\n                return job.sub_topics;\r\n              }\r\n              return [];\r\n            } catch (err) {\r\n              console.error(\"Error parsing sub_topics:\", err);\r\n              return [];\r\n            }\r\n          })(),\r\n          // Add a Date object for sorting purposes\r\n          dateObj: new Date(job.start_date)\r\n        }));\r\n        \r\n        // Sort jobs with newest first (by date posted)\r\n        const sortedJobs = formattedJobs.sort((a, b) => b.dateObj - a.dateObj);\r\n        \r\n        setJobs(sortedJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching jobs:\", err);\r\n        setError(\"Failed to load jobs from server\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    const fetchHotJobs = async () => {\r\n      try {\r\n        const response = await ApiService.jobs.getHotJobs();\r\n        \r\n        // Transform hot jobs data to match the format used in frontend\r\n        const formattedHotJobs = response.data.map(job => ({\r\n          id: job.job_id,\r\n          position: job.job_title,\r\n          company: job.company_name,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          min_salary: job.min_salary,\r\n          max_salary: job.max_salary,\r\n          salary: formatSalary(job.min_salary, job.max_salary),\r\n          views: job.view_count || 0,\r\n          postedTime: formatPostedDate(job.start_date),\r\n          datePosted: job.start_date,\r\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\r\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          company_logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n          hot: true,\r\n          status: job.status || 'Active',\r\n          description: job.job_description || 'No description provided',\r\n          subTopics: (() => {\r\n            try {\r\n              if (!job.sub_topics) return [];\r\n              if (typeof job.sub_topics === 'string') {\r\n                if (job.sub_topics.trim().startsWith('[')) {\r\n                  return JSON.parse(job.sub_topics);\r\n                } else {\r\n                  return [job.sub_topics];\r\n                }\r\n              } else if (Array.isArray(job.sub_topics)) {\r\n                return job.sub_topics;\r\n              }\r\n              return [];\r\n            } catch (err) {\r\n              console.error(\"Error parsing sub_topics:\", err);\r\n              return [];\r\n            }\r\n          })(),\r\n          dateObj: new Date(job.start_date)\r\n        }));\r\n        \r\n        setHotJobs(formattedHotJobs);\r\n      } catch (err) {\r\n        console.error(\"Error fetching hot jobs:\", err);\r\n        // Don't set error for hot jobs failure, just log it\r\n      }\r\n    };\r\n\r\n    fetchJobs();\r\n    fetchHotJobs();\r\n    // Set default tab to \"All Jobs\"\r\n    setActiveTab('all');\r\n    \r\n    // Scroll to top when component mounts\r\n    window.scrollTo(0, 0);\r\n  }, []);\r\n\r\n  // Helper function to format min/max salary as \"Rs. min - Rs. max\"\r\n  const formatSalary = (min, max) => {\r\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) &&\r\n        (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\r\n      return 'Negotiable';\r\n    }\r\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\r\n      return 'Negotiable';\r\n    }\r\n    if (min && max) {\r\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    if (min) {\r\n      return `Rs. ${Number(min).toLocaleString()}`;\r\n    }\r\n    if (max) {\r\n      return `Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    return 'Negotiable';\r\n  };\r\n\r\n  // Helper function to format posted date\r\n  const formatPostedDate = (dateString) => {\r\n    if (!dateString) return 'Recently';\r\n    \r\n    try {\r\n      // Parse the date from the server\r\n      const postedDate = new Date(dateString);\r\n      const now = new Date();\r\n      \r\n      // Normalize both dates to midnight in local timezone to compare just the dates\r\n      const postedDateNormalized = new Date(\r\n        postedDate.getFullYear(), \r\n        postedDate.getMonth(), \r\n        postedDate.getDate()\r\n      ).getTime();\r\n      \r\n      const todayNormalized = new Date(\r\n        now.getFullYear(),\r\n        now.getMonth(),\r\n        now.getDate()\r\n      ).getTime();\r\n      \r\n      const oneDayMs = 24 * 60 * 60 * 1000; // One day in milliseconds\r\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\r\n      \r\n      // Check if it's today\r\n      if (daysAgo === 0) {\r\n        return 'Today';\r\n      }\r\n      \r\n      // Check if it's yesterday\r\n      if (daysAgo === 1) {\r\n        return 'Yesterday';\r\n      }\r\n      \r\n      // Calculate exact days difference\r\n      if (daysAgo < 7) return `${daysAgo} days ago`;\r\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\r\n      return `${Math.floor(daysAgo / 30)} months ago`;\r\n    } catch (err) {\r\n      console.error(\"Error formatting date:\", err, \"for date:\", dateString);\r\n      return 'Recently';\r\n    }\r\n  };\r\n\r\n  // Handle subtopic selection\r\n  const handleSubTopicChange = (topic) => {\r\n    setSelectedSubTopics(prevSelected => {\r\n      if (prevSelected.includes(topic)) {\r\n        // Remove topic if already selected (uncheck)\r\n        return prevSelected.filter(t => t !== topic);\r\n      } else {\r\n        // Add topic if not already selected (check)\r\n        return [...prevSelected, topic];\r\n      }\r\n    });\r\n    setCurrentPage(1); // Reset to first page when changing filters\r\n  };\r\n\r\n  // Filter jobs based on active tab and selected subtopics\r\n  const filteredJobs = jobs\r\n    .filter(job => {\r\n      // Filter by tab\r\n      const matchesTab = activeTab === 'all' || job.location === activeTab;\r\n      \r\n      // Filter by sub topics if any are selected\r\n      const matchesSubTopics = selectedSubTopics.length === 0 || \r\n        (job.subTopics && job.subTopics.some(topic => selectedSubTopics.includes(topic)));\r\n      \r\n      // Only include active jobs\r\n      const isActive = job.status !== 'Inactive';\r\n      \r\n      return matchesTab && matchesSubTopics && isActive;\r\n    });\r\n  // Filter urgent/hot jobs for the left sidebar - use hotJobs state\r\n  const urgentJobs = hotJobs.slice(0, 2);\r\n  \r\n  // Get paginated jobs - only show 5 per page\r\n  const indexOfLastJob = currentPage * jobsPerPage;\r\n  const indexOfFirstJob = indexOfLastJob - jobsPerPage;\r\n  const currentJobs = filteredJobs\r\n    .filter(job => job.hot !== true) // Don't show hot jobs in the regular listings\r\n    .slice(indexOfFirstJob, indexOfLastJob);\r\n  \r\n  // Calculate total pages\r\n  const totalPages = Math.ceil(\r\n    filteredJobs.filter(job => job.hot !== true).length / jobsPerPage\r\n  );\r\n\r\n  // Change page\r\n  const handlePageChange = (pageNumber) => {\r\n    if (pageNumber < 1 || pageNumber > totalPages) return;\r\n    setCurrentPage(pageNumber);\r\n    // Scroll to top of job listings\r\n    window.scrollTo({ top: 400, behavior: 'smooth' });\r\n  };\r\n\r\n  // Generate page numbers for pagination\r\n  const pageNumbers = [];\r\n  const displayRange = 5; // How many page numbers to show at once\r\n  \r\n  let startPage = Math.max(1, currentPage - Math.floor(displayRange / 2));\r\n  let endPage = Math.min(totalPages, startPage + displayRange - 1);\r\n  \r\n  if (endPage - startPage + 1 < displayRange) {\r\n    startPage = Math.max(1, endPage - displayRange + 1);\r\n  }\r\n  \r\n  for (let i = startPage; i <= endPage; i++) {\r\n    pageNumbers.push(i);\r\n  }\r\n\r\n  // Change active tab\r\n  const handleTabChange = (tabName) => {\r\n    setActiveTab(tabName);\r\n    setCurrentPage(1); // Reset to first page when changing tabs\r\n  };\r\n\r\n  // Clear all selected subtopics\r\n  const clearSubTopicFilters = () => {\r\n    setSelectedSubTopics([]);\r\n  };\r\n\r\n  // Toggle filter sidebar\r\n  const toggleFilterSidebar = () => {\r\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\r\n  };\r\n\r\n  // Function to increment view count\r\n  const incrementViewCount = async (jobId) => {\r\n    try {\r\n      await ApiService.jobs.recordView(jobId);\r\n    } catch (err) {\r\n      console.error(\"Error incrementing view count:\", err);\r\n    }\r\n  };\r\n\r\n  const JobLoadingSkeleton = ({ count = 3 }) => {\r\n  return (\r\n    <div className=\"jobs-loading-container\">\r\n      {[...Array(count)].map((_, i) => (\r\n        <div key={i} className=\"job-card-skeleton\">\r\n          <div className=\"skeleton-thumbnail\"></div>\r\n          <div className=\"skeleton-content\">\r\n            <div className=\"skeleton-line title\"></div>\r\n            <div className=\"skeleton-line company\"></div>\r\n            <div className=\"skeleton-meta\">\r\n              <div className=\"skeleton-tag\"></div>\r\n              <div className=\"skeleton-tag\"></div>\r\n              <div className=\"skeleton-tag\"></div>\r\n            </div>\r\n            <div className=\"skeleton-line description\"></div>\r\n            <div className=\"skeleton-line description short\"></div>\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\n  return (\r\n    <div className=\"homepage-container\">\r\n      <PageHelmet \r\n        title=\"Your Path to the Perfect Job\" \r\n        description=\"Find your dream job across all sectors - Government, Private, Foreign, and Internships.\"\r\n      />\r\n      <div className=\"content-spacing\"></div>\r\n      \r\n      {/* Search Area */}\r\n      <section className=\"homepage-search-area\">\r\n        <SearchArea />\r\n      </section>\r\n      \r\n      {/* Banner */}\r\n      <section className=\"homepage-top-banner\">\r\n        <Banner />\r\n      </section>\r\n      \r\n      {/* Main Content with constrained width */}\r\n      <div className=\"home-content-wrapper\">\r\n        {/* Header with New Jobs Title */}\r\n        <div className=\"home-header\" >\r\n          <div className=\"header-left\">\r\n            <h1 className=\"main-title\">New Jobs</h1>\r\n            <button \r\n              className=\"filter-toggle-btn\" \r\n              onClick={toggleFilterSidebar}\r\n              aria-label=\"Toggle filters\"\r\n            >\r\n              <FontAwesomeIcon icon={faFilter} />\r\n              Filters\r\n            </button>\r\n          </div>\r\n          <div className=\"header-tabs\">\r\n            <span \r\n              className={`tab ${activeTab === 'all' ? 'active all-active' : ''}`} \r\n              key=\"all\"\r\n              onClick={() => handleTabChange('all')}\r\n            >\r\n              All Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Government Jobs' ? 'active gov-active' : ''}`} \r\n              key=\"gov\"\r\n              onClick={() => handleTabChange('Government Jobs')}\r\n            >\r\n              Government Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Private Jobs' ? 'active private-active' : ''}`} \r\n              key=\"private\"\r\n              onClick={() => handleTabChange('Private Jobs')}\r\n            >\r\n              Private Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Foreign Jobs' ? 'active foreign-active' : ''}`} \r\n              key=\"foreign\"\r\n              onClick={() => handleTabChange('Foreign Jobs')}\r\n            >\r\n              Foreign Jobs\r\n            </span>\r\n            <span \r\n              className={`tab ${activeTab === 'Internships' ? 'active internships-active' : ''}`} \r\n              key=\"internships\"\r\n              onClick={() => handleTabChange('Internships')}\r\n            >\r\n              Internships\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Main 3-column Layout */}\r\n        <div className=\"home-layout\">          {/* Left Column - Urgent Jobs */}\r\n          <div className=\"left-column\">\r\n            {loading ? (\r\n              <div className=\"loading-overlay\">\r\n                <FaSpinner className=\"spinner\" />\r\n                <p>Loading urgent jobs...</p>\r\n              </div>\r\n            ) : hotJobs.length > 0 ? (\r\n              hotJobs.slice(0, 2).map(job => (\r\n                <UrgentJobCard \r\n                  key={`urgent-${job.id}`}\r\n                  job={job}\r\n                  onViewIncrement={incrementViewCount}\r\n                />\r\n              ))\r\n            ) : (\r\n              <div className=\"no-urgent-jobs\">\r\n                <p>No urgent jobs available right now.</p>\r\n              </div>\r\n            )}\r\n            \r\n            {/* Recruiting Box */}\r\n            <div className=\"recruiting-box\">\r\n              <h3>Recruiting?</h3>\r\n              <p>Get your job postings seen by thousands of job seekers.</p>\r\n              <img src=\"https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80\" alt=\"Recruiting\" className=\"recruiting-image\" />\r\n              <a href=\"/contact\">\r\n                <button className=\"post-job-btn\">Post a Job</button>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Middle Column - Regular Jobs */}\r\n          <div className=\"middle-column\">\r\n            {error && (\r\n              <div className=\"error-notification\" style={{ \r\n                color: '#e74c3c', \r\n                backgroundColor: '#fdf2f2', \r\n                border: '1px solid #f5c6cb', \r\n                borderRadius: '8px', \r\n                padding: '12px 16px', \r\n                margin: '16px auto', \r\n                textAlign: 'center', \r\n                fontSize: '14px', \r\n                maxWidth: '400px',\r\n                boxShadow: '0 2px 4px rgba(231, 76, 60, 0.1)'\r\n              }}>\r\n                {error}\r\n              </div>\r\n            )}\r\n            {loading ? (\r\n              <div className=\"loading-overlay\">\r\n                <FaSpinner className=\"spinner\" />\r\n                <p>Loading available positions...</p>\r\n              </div>\r\n            ) : currentJobs.length > 0 ? (\r\n              currentJobs.map(job => (\r\n                <Link\r\n                  key={`regular-${job.id}`}\r\n                  to={`/job/${job.id}`}\r\n                  className={`job-card-redesigned job-type-${(job.workTime || '').toLowerCase().replace(/\\s+/g, '-')}`}\r\n                  style={{ background: \"#fff\", textDecoration: \"none\", color: \"inherit\", position: \"relative\" }}\r\n                  onClick={() => incrementViewCount(job.id)}\r\n                >\r\n                  {job.hot && (\r\n                    <span className=\"urgent-tag jobcard-urgent-tag\">🔥 URGENT</span>\r\n                  )}\r\n                  <div className=\"job-card-header\">\r\n                    <div className=\"company-info-header\">\r\n                      {/* Removing the date from here */}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"job-card-content\">\r\n                    <div className=\"job-image-container\">\r\n                      <img \r\n                        src={job.image} \r\n                        alt={job.position} \r\n                        className=\"job-thumbnail\" \r\n                        onError={(e) => {\r\n                          e.target.onerror = null; \r\n                          e.target.src = 'https://via.placeholder.com/300x300?text=Job+Image';\r\n                        }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"job-main-content\">\r\n                      <h3 className=\"job-title-redesigned\">\r\n                        {job.position} \r\n                      </h3>\r\n                  <div className=\"company-info-urgent\">\r\n                    <div className=\"company-logo-urgent\">\r\n                      <img \r\n                        src={job.logo} \r\n                        alt={job.company}\r\n                        onError={(e) => {\r\n                          e.target.onerror = null;\r\n                          e.target.src = 'https://via.placeholder.com/30';\r\n                        }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"company-details-urgent\">\r\n                      <h3 className=\"company-name-urgent\">{job.company}</h3>\r\n                    </div>\r\n                  </div>\r\n                      <div className=\"job-meta-info\">\r\n                        <span className=\"job-tag location\">\r\n                          <FontAwesomeIcon icon={faBriefcase} /> {job.location}\r\n                        </span>\r\n                        <span className=\"job-tag work-type\">\r\n                          <FontAwesomeIcon icon={faClock} /> {job.workTime}\r\n                        </span>\r\n                        <span className=\"job-tag salary\">\r\n                          {formatSalary(job.min_salary, job.max_salary)}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"job-description-redesigned\">\r\n                        <p>{job.description ? job.description.substring(0, 140) + '...' : 'No description available.'}</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"job-card-footer\">\r\n                    <span className=\"posting-date\"> {job.postedTime}</span>\r\n                    <div className=\"apply-section\" style={{ marginLeft: \"auto\" }}>\r\n                      <button\r\n                        className=\"apply-btn-redesigned\"\r\n                      >\r\n                        Apply Now\r\n                      </button>\r\n                      {/* <button\r\n                        className=\"job-action-btn share-btn\"\r\n                        onClick={e => {\r\n                          e.preventDefault();\r\n                          // Optionally implement share logic here\r\n                        }}\r\n                      >\r\n                        <FontAwesomeIcon icon={faShare} />\r\n                      </button> */}\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              ))\r\n            ) : (\r\n              <div className=\"no-jobs-redesigned\">\r\n                <div className=\"no-jobs-icon\">\r\n                  <FontAwesomeIcon icon={faBriefcase} />\r\n                </div>\r\n                <h3>No Matching Jobs</h3>\r\n                <p>There are no jobs available in this category right now.</p>\r\n                <p>Try adjusting your filters or check back later.</p>\r\n                {!error && !loading && jobs.length === 0 && (\r\n                  <div style={{ color: 'gray', marginTop: '16px' }}>\r\n                    No jobs found. Please check your API server or try again later.\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n            \r\n            {/* Pagination */}\r\n            <div className=\"pagination\">\r\n              <button \r\n                className=\"page-arrow\" \r\n                key=\"prev\"\r\n                onClick={() => handlePageChange(currentPage - 1)}\r\n                disabled={currentPage === 1}\r\n              >\r\n                <FontAwesomeIcon icon={faChevronLeft} />\r\n              </button>\r\n              \r\n              {pageNumbers.map(number => (\r\n                <button\r\n                  key={`page-${number}`}\r\n                  className={`page-number ${currentPage === number ? 'active' : ''}`}\r\n                  onClick={() => handlePageChange(number)}\r\n                >\r\n                  {number}\r\n                </button>\r\n              ))}\r\n              \r\n              <button \r\n                className=\"page-arrow\" \r\n                key=\"next\"\r\n                onClick={() => handlePageChange(currentPage + 1)}\r\n                disabled={currentPage === totalPages || totalPages === 0}\r\n              >\r\n                <FontAwesomeIcon icon={faChevronRight} />\r\n              </button>\r\n              \r\n              {totalPages > displayRange && (\r\n                <span className=\"page-info\" key=\"more\">\r\n                  of {totalPages} pages\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right Column - Subtopics Filter */}\r\n          <div className=\"right-column\">\r\n            <div className=\"subtopics-box\">\r\n              <div className=\"subtopics-header\">\r\n                <h3>Filter by Specialization</h3>\r\n                <div className=\"filter-actions\">\r\n                  <button\r\n                    className=\"refresh-subtopics-btn\"\r\n                    onClick={loadSubTopicsFromStorage}\r\n                    title=\"Refresh sub-topics list\"\r\n                    style={{\r\n                      background: 'none',\r\n                      border: '1px solid #ddd',\r\n                      borderRadius: '4px',\r\n                      padding: '4px 8px',\r\n                      cursor: 'pointer',\r\n                      fontSize: '12px',\r\n                      color: '#666',\r\n                      marginRight: '8px'\r\n                    }}\r\n                  >\r\n                    <FontAwesomeIcon icon={faSync} />\r\n                  </button>\r\n                  {selectedSubTopics.length > 0 && (\r\n                    <button className=\"clear-filters-btn\" onClick={clearSubTopicFilters}>\r\n                      Clear all <FontAwesomeIcon icon={faTimes} />\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              <div className=\"subtopics-list\">\r\n                {availableSubTopics.map((topic, index) => (\r\n                  <div\r\n                    key={`subtopic-${index}`}\r\n                    className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}\r\n                  >\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id={`subtopic-${index}`}\r\n                      checked={selectedSubTopics.includes(topic)}\r\n                      onChange={() => handleSubTopicChange(topic)}\r\n                    />\r\n                    <label htmlFor={`subtopic-${index}`} title={topic}>\r\n                      {shortenTopicName(topic)}\r\n                    </label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"selected-filters\">\r\n                {selectedSubTopics.length > 0 ? (\r\n                  <div>\r\n                    <div className=\"filters-label\">\r\n                      <FontAwesomeIcon icon={faFilter} /> Applied filters:\r\n                    </div>\r\n                    <div className=\"selected-topics-list\">\r\n                      {selectedSubTopics.map((topic, idx) => (\r\n                        <span key={`selected-${idx}`} className=\"selected-topic-tag\" title={topic}>\r\n                          {shortenTopicName(topic, 20)}\r\n                          <button \r\n                            className=\"remove-topic\" \r\n                            onClick={() => handleSubTopicChange(topic)}\r\n                          >\r\n                            <FontAwesomeIcon icon={faTimes} />\r\n                          </button>\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"no-filters\">No filters applied</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Featured Section */}\r\n        <FeaturedSection />\r\n      </div>\r\n\r\n      {/* Filter Sidebar Overlay */}\r\n      {isFilterSidebarOpen && (\r\n        <div className=\"filter-sidebar-overlay\" onClick={toggleFilterSidebar}></div>\r\n      )}\r\n\r\n      {/* Sliding Filter Sidebar */}\r\n      <div className={`filter-sidebar ${isFilterSidebarOpen ? 'open' : ''}`}>\r\n        <div className=\"filter-sidebar-header\">\r\n          <h3><FontAwesomeIcon icon={faFilter} /> Filters</h3>\r\n          <button className=\"close-sidebar-btn\" onClick={toggleFilterSidebar}>\r\n            <FontAwesomeIcon icon={faTimes} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"filter-sidebar-content\">\r\n          <div className=\"subtopics-box\">\r\n            <div className=\"subtopics-header\">\r\n              <h3>Filter by Specialization</h3>\r\n              {selectedSubTopics.length > 0 && (\r\n                <button className=\"clear-filters-btn\" onClick={clearSubTopicFilters}>\r\n                  Clear all <FontAwesomeIcon icon={faTimes} />\r\n                </button>\r\n              )}\r\n            </div>\r\n            <div className=\"subtopics-list\">\r\n              {availableSubTopics.map((topic, index) => (\r\n                <div\r\n                  key={`sidebar-subtopic-${index}`}\r\n                  className={`subtopic-checkbox ${selectedSubTopics.includes(topic) ? 'selected' : ''}`}\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    id={`sidebar-subtopic-${index}`}\r\n                    checked={selectedSubTopics.includes(topic)}\r\n                    onChange={() => handleSubTopicChange(topic)}\r\n                  />\r\n                  <label htmlFor={`sidebar-subtopic-${index}`} title={topic}>\r\n                    {shortenTopicName(topic)}\r\n                  </label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n            <div className=\"selected-filters\">\r\n              {selectedSubTopics.length > 0 ? (\r\n                <div>\r\n                  <div className=\"filters-label\">\r\n                    <FontAwesomeIcon icon={faFilter} /> Applied filters:\r\n                  </div>\r\n                  <div className=\"selected-topics-list\">\r\n                    {selectedSubTopics.map((topic, idx) => (\r\n                      <span key={`sidebar-selected-${idx}`} className=\"selected-topic-tag\" title={topic}>\r\n                        {shortenTopicName(topic, 20)}\r\n                        <button \r\n                          className=\"remove-topic\" \r\n                          onClick={() => handleSubTopicChange(topic)}\r\n                        >\r\n                          <FontAwesomeIcon icon={faTimes} />\r\n                        </button>\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"no-filters\">No filters applied</div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Featured Companies */}\r\n      {/* <FeaturedCompanies /> */}\r\n      \r\n      {/* Newsletter */}\r\n      {/* <section className=\"homepage-newsletter\">\r\n        <NewsLetter />\r\n      </section> */}\r\n      \r\n      {/* Banner */}\r\n      <section className=\"homepage-banner\">\r\n        <Banner />\r\n      </section>\r\n      <section className=\"social-media-feeds\">\r\n        <SocialMediaFeeds />\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,qBAAqB;AAC5B,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,EAAEC,OAAO,EAAEC,KAAK,EAAEC,aAAa,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,QAAQ,mCAAmC;AACtL,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,sBAAsB,EAAEC,mBAAmB,QAAQ,yBAAyB;AACrF,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAMkD,WAAW,GAAG,CAAC;;EAErB;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI;MACF,MAAMC,mBAAmB,GAAGlC,sBAAsB,CAAC,CAAC;MACpD,IAAIkC,mBAAmB,CAACC,MAAM,GAAG,CAAC,EAAE;QAClCJ,qBAAqB,CAACG,mBAAmB,CAAC;MAC5C,CAAC,MAAM;QACL;QACA,MAAME,gBAAgB,GAAGnC,mBAAmB,CAAC,CAAC;QAC9C8B,qBAAqB,CAACK,gBAAgB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,6CAA6C,EAAEiB,GAAG,CAAC;MACjE;MACA,MAAMD,gBAAgB,GAAGnC,mBAAmB,CAAC,CAAC;MAC9C8B,qBAAqB,CAACK,gBAAgB,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,SAAS,GAAG,EAAE,KAAK;IAClD,IAAID,KAAK,CAACL,MAAM,IAAIM,SAAS,EAAE;MAC7B,OAAOD,KAAK;IACd;IACA,OAAOA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC9C,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACdkD,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlD,SAAS,CAAC,MAAM;IACd,MAAM4D,qBAAqB,GAAIC,KAAK,IAAK;MACvCN,OAAO,CAACO,GAAG,CAAC,iEAAiE,CAAC;MAC9EZ,wBAAwB,CAAC,CAAC;IAC5B,CAAC;;IAED;IACAa,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,qBAAqB,CAAC;;IAElE;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEL,qBAAqB,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5D,SAAS,CAAC,MAAM;IACd,MAAMkE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF9B,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAM+B,QAAQ,GAAG,MAAMnD,UAAU,CAACe,IAAI,CAACqC,MAAM,CAAC;UAAEC,EAAE,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;QAAE,CAAC,CAAC;;QAE3E;QACA,MAAMC,aAAa,GAAGL,QAAQ,CAACM,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAC9CC,EAAE,EAAED,GAAG,CAACE,MAAM;UACdC,QAAQ,EAAEH,GAAG,CAACI,SAAS;UACvBC,OAAO,EAAEL,GAAG,CAACM,YAAY;UACzBC,QAAQ,EAAEP,GAAG,CAACQ,WAAW;UACzBC,QAAQ,EAAET,GAAG,CAACU,QAAQ;UACtB;UACAC,UAAU,EAAEX,GAAG,CAACW,UAAU;UAC1BC,UAAU,EAAEZ,GAAG,CAACY,UAAU;UAC1BC,MAAM,EAAEC,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU,CAAC;UACpDG,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BC,UAAU,EAAEC,gBAAgB,CAAClB,GAAG,CAACmB,UAAU,CAAC;UAC5CC,UAAU,EAAEpB,GAAG,CAACmB,UAAU;UAAE;UAC5B;UACAE,KAAK,EAAErB,GAAG,CAACsB,kBAAkB,IAAItB,GAAG,CAACuB,cAAc,IAAI,8DAA8D;UACrHC,IAAI,EAAExB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAClFD,YAAY,EAAEzB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAC1FC,GAAG,EAAE3B,GAAG,CAAC2B,GAAG,IAAI,KAAK;UAAE;UACvBC,MAAM,EAAE5B,GAAG,CAAC4B,MAAM,IAAI,QAAQ;UAC9BC,WAAW,EAAE7B,GAAG,CAAC8B,eAAe,IAAI,yBAAyB;UAC7DC,SAAS,EAAE,CAAC,MAAM;YAChB,IAAI;cACF,IAAI,CAAC/B,GAAG,CAACgC,UAAU,EAAE,OAAO,EAAE;cAC9B,IAAI,OAAOhC,GAAG,CAACgC,UAAU,KAAK,QAAQ,EAAE;gBACtC,IAAIhC,GAAG,CAACgC,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;kBACzC,OAAOC,IAAI,CAACC,KAAK,CAACpC,GAAG,CAACgC,UAAU,CAAC;gBACnC,CAAC,MAAM;kBACL,OAAO,CAAChC,GAAG,CAACgC,UAAU,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACtC,GAAG,CAACgC,UAAU,CAAC,EAAE;gBACxC,OAAOhC,GAAG,CAACgC,UAAU;cACvB;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOrD,GAAG,EAAE;cACZC,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEiB,GAAG,CAAC;cAC/C,OAAO,EAAE;YACX;UACF,CAAC,EAAE,CAAC;UACJ;UACA4D,OAAO,EAAE,IAAI5C,IAAI,CAACK,GAAG,CAACmB,UAAU;QAClC,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMqB,UAAU,GAAG3C,aAAa,CAAC4C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,OAAO,GAAGG,CAAC,CAACH,OAAO,CAAC;QAEtElF,OAAO,CAACmF,UAAU,CAAC;QACnB7E,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZC,OAAO,CAAClB,KAAK,CAAC,sBAAsB,EAAEiB,GAAG,CAAC;QAC1ChB,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAMmF,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMpD,QAAQ,GAAG,MAAMnD,UAAU,CAACe,IAAI,CAACyF,UAAU,CAAC,CAAC;;QAEnD;QACA,MAAMC,gBAAgB,GAAGtD,QAAQ,CAACM,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UACjDC,EAAE,EAAED,GAAG,CAACE,MAAM;UACdC,QAAQ,EAAEH,GAAG,CAACI,SAAS;UACvBC,OAAO,EAAEL,GAAG,CAACM,YAAY;UACzBC,QAAQ,EAAEP,GAAG,CAACQ,WAAW;UACzBC,QAAQ,EAAET,GAAG,CAACU,QAAQ;UACtBC,UAAU,EAAEX,GAAG,CAACW,UAAU;UAC1BC,UAAU,EAAEZ,GAAG,CAACY,UAAU;UAC1BC,MAAM,EAAEC,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU,CAAC;UACpDG,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAC1BC,UAAU,EAAEC,gBAAgB,CAAClB,GAAG,CAACmB,UAAU,CAAC;UAC5CC,UAAU,EAAEpB,GAAG,CAACmB,UAAU;UAC1BE,KAAK,EAAErB,GAAG,CAACsB,kBAAkB,IAAItB,GAAG,CAACuB,cAAc,IAAI,8DAA8D;UACrHC,IAAI,EAAExB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAClFD,YAAY,EAAEzB,GAAG,CAACyB,YAAY,IAAIzB,GAAG,CAAC0B,gBAAgB,IAAI,gCAAgC;UAC1FC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE5B,GAAG,CAAC4B,MAAM,IAAI,QAAQ;UAC9BC,WAAW,EAAE7B,GAAG,CAAC8B,eAAe,IAAI,yBAAyB;UAC7DC,SAAS,EAAE,CAAC,MAAM;YAChB,IAAI;cACF,IAAI,CAAC/B,GAAG,CAACgC,UAAU,EAAE,OAAO,EAAE;cAC9B,IAAI,OAAOhC,GAAG,CAACgC,UAAU,KAAK,QAAQ,EAAE;gBACtC,IAAIhC,GAAG,CAACgC,UAAU,CAACC,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;kBACzC,OAAOC,IAAI,CAACC,KAAK,CAACpC,GAAG,CAACgC,UAAU,CAAC;gBACnC,CAAC,MAAM;kBACL,OAAO,CAAChC,GAAG,CAACgC,UAAU,CAAC;gBACzB;cACF,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACtC,GAAG,CAACgC,UAAU,CAAC,EAAE;gBACxC,OAAOhC,GAAG,CAACgC,UAAU;cACvB;cACA,OAAO,EAAE;YACX,CAAC,CAAC,OAAOrD,GAAG,EAAE;cACZC,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEiB,GAAG,CAAC;cAC/C,OAAO,EAAE;YACX;UACF,CAAC,EAAE,CAAC;UACJ4D,OAAO,EAAE,IAAI5C,IAAI,CAACK,GAAG,CAACmB,UAAU;QAClC,CAAC,CAAC,CAAC;QAEH5D,UAAU,CAACuF,gBAAgB,CAAC;MAC9B,CAAC,CAAC,OAAOnE,GAAG,EAAE;QACZC,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEiB,GAAG,CAAC;QAC9C;MACF;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;IACXqD,YAAY,CAAC,CAAC;IACd;IACA/E,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACAuB,MAAM,CAAC2D,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMjC,YAAY,GAAGA,CAACkC,GAAG,EAAEC,GAAG,KAAK;IACjC,IAAI,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,EAAE,IAAIG,KAAK,CAACC,MAAM,CAACJ,GAAG,CAAC,CAAC,MACrEC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAAIE,KAAK,CAACC,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC,EAAE;MAC3E,OAAO,YAAY;IACrB;IACA,IAAI,CAACD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,MAAMC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,CAAC,EAAE;MAC5D,OAAO,YAAY;IACrB;IACA,IAAID,GAAG,IAAIC,GAAG,EAAE;MACd,OAAO,OAAOG,MAAM,CAACJ,GAAG,CAAC,CAACK,cAAc,CAAC,CAAC,UAAUD,MAAM,CAACH,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,EAAE;IACpF;IACA,IAAIL,GAAG,EAAE;MACP,OAAO,OAAOI,MAAM,CAACJ,GAAG,CAAC,CAACK,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,IAAIJ,GAAG,EAAE;MACP,OAAO,OAAOG,MAAM,CAACH,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,OAAO,YAAY;EACrB,CAAC;;EAED;EACA,MAAMnC,gBAAgB,GAAIoC,UAAU,IAAK;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,IAAI5D,IAAI,CAAC2D,UAAU,CAAC;MACvC,MAAME,GAAG,GAAG,IAAI7D,IAAI,CAAC,CAAC;;MAEtB;MACA,MAAM8D,oBAAoB,GAAG,IAAI9D,IAAI,CACnC4D,UAAU,CAACG,WAAW,CAAC,CAAC,EACxBH,UAAU,CAACI,QAAQ,CAAC,CAAC,EACrBJ,UAAU,CAACK,OAAO,CAAC,CACrB,CAAC,CAAChE,OAAO,CAAC,CAAC;MAEX,MAAMiE,eAAe,GAAG,IAAIlE,IAAI,CAC9B6D,GAAG,CAACE,WAAW,CAAC,CAAC,EACjBF,GAAG,CAACG,QAAQ,CAAC,CAAC,EACdH,GAAG,CAACI,OAAO,CAAC,CACd,CAAC,CAAChE,OAAO,CAAC,CAAC;MAEX,MAAMkE,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;MACtC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,eAAe,GAAGJ,oBAAoB,IAAIK,QAAQ,CAAC;;MAE/E;MACA,IAAIC,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,OAAO;MAChB;;MAEA;MACA,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,WAAW;MACpB;;MAEA;MACA,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,GAAGA,OAAO,WAAW;MAC7C,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGC,IAAI,CAACE,KAAK,CAACH,OAAO,GAAG,CAAC,CAAC,YAAY;MAC/D,OAAO,GAAGC,IAAI,CAACE,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC,aAAa;IACjD,CAAC,CAAC,OAAOpF,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,wBAAwB,EAAEiB,GAAG,EAAE,WAAW,EAAE2E,UAAU,CAAC;MACrE,OAAO,UAAU;IACnB;EACF,CAAC;;EAED;EACA,MAAMa,oBAAoB,GAAIrF,KAAK,IAAK;IACtCb,oBAAoB,CAACmG,YAAY,IAAI;MACnC,IAAIA,YAAY,CAACC,QAAQ,CAACvF,KAAK,CAAC,EAAE;QAChC;QACA,OAAOsF,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKzF,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGsF,YAAY,EAAEtF,KAAK,CAAC;MACjC;IACF,CAAC,CAAC;IACFf,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyG,YAAY,GAAGpH,IAAI,CACtBkH,MAAM,CAACtE,GAAG,IAAI;IACb;IACA,MAAMyE,UAAU,GAAG7G,SAAS,KAAK,KAAK,IAAIoC,GAAG,CAACO,QAAQ,KAAK3C,SAAS;;IAEpE;IACA,MAAM8G,gBAAgB,GAAG1G,iBAAiB,CAACS,MAAM,KAAK,CAAC,IACpDuB,GAAG,CAAC+B,SAAS,IAAI/B,GAAG,CAAC+B,SAAS,CAAC4C,IAAI,CAAC7F,KAAK,IAAId,iBAAiB,CAACqG,QAAQ,CAACvF,KAAK,CAAC,CAAE;;IAEnF;IACA,MAAM8F,QAAQ,GAAG5E,GAAG,CAAC4B,MAAM,KAAK,UAAU;IAE1C,OAAO6C,UAAU,IAAIC,gBAAgB,IAAIE,QAAQ;EACnD,CAAC,CAAC;EACJ;EACA,MAAMC,UAAU,GAAGvH,OAAO,CAACwH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEtC;EACA,MAAMC,cAAc,GAAGjH,WAAW,GAAGQ,WAAW;EAChD,MAAM0G,eAAe,GAAGD,cAAc,GAAGzG,WAAW;EACpD,MAAM2G,WAAW,GAAGT,YAAY,CAC7BF,MAAM,CAACtE,GAAG,IAAIA,GAAG,CAAC2B,GAAG,KAAK,IAAI,CAAC,CAAC;EAAA,CAChCmD,KAAK,CAACE,eAAe,EAAED,cAAc,CAAC;;EAEzC;EACA,MAAMG,UAAU,GAAGlB,IAAI,CAACmB,IAAI,CAC1BX,YAAY,CAACF,MAAM,CAACtE,GAAG,IAAIA,GAAG,CAAC2B,GAAG,KAAK,IAAI,CAAC,CAAClD,MAAM,GAAGH,WACxD,CAAC;;EAED;EACA,MAAM8G,gBAAgB,GAAIC,UAAU,IAAK;IACvC,IAAIA,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAGH,UAAU,EAAE;IAC/CnH,cAAc,CAACsH,UAAU,CAAC;IAC1B;IACAjG,MAAM,CAAC2D,QAAQ,CAAC;MAAEuC,GAAG,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAC;;EAExB,IAAIC,SAAS,GAAG1B,IAAI,CAACf,GAAG,CAAC,CAAC,EAAEnF,WAAW,GAAGkG,IAAI,CAACE,KAAK,CAACuB,YAAY,GAAG,CAAC,CAAC,CAAC;EACvE,IAAIE,OAAO,GAAG3B,IAAI,CAAChB,GAAG,CAACkC,UAAU,EAAEQ,SAAS,GAAGD,YAAY,GAAG,CAAC,CAAC;EAEhE,IAAIE,OAAO,GAAGD,SAAS,GAAG,CAAC,GAAGD,YAAY,EAAE;IAC1CC,SAAS,GAAG1B,IAAI,CAACf,GAAG,CAAC,CAAC,EAAE0C,OAAO,GAAGF,YAAY,GAAG,CAAC,CAAC;EACrD;EAEA,KAAK,IAAIG,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;IACzCJ,WAAW,CAACK,IAAI,CAACD,CAAC,CAAC;EACrB;;EAEA;EACA,MAAME,eAAe,GAAIC,OAAO,IAAK;IACnClI,YAAY,CAACkI,OAAO,CAAC;IACrBhI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiI,oBAAoB,GAAGA,CAAA,KAAM;IACjC/H,oBAAoB,CAAC,EAAE,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgI,mBAAmB,GAAGA,CAAA,KAAM;IAChC9H,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMgI,kBAAkB,GAAG,MAAOC,KAAK,IAAK;IAC1C,IAAI;MACF,MAAM9J,UAAU,CAACe,IAAI,CAACgJ,UAAU,CAACD,KAAK,CAAC;IACzC,CAAC,CAAC,OAAOxH,GAAG,EAAE;MACZC,OAAO,CAAClB,KAAK,CAAC,gCAAgC,EAAEiB,GAAG,CAAC;IACtD;EACF,CAAC;EAED,MAAM0H,kBAAkB,GAAGA,CAAC;IAAEC,KAAK,GAAG;EAAE,CAAC,KAAK;IAC9C,oBACErJ,OAAA;MAAKsJ,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpC,CAAC,GAAGnE,KAAK,CAACiE,KAAK,CAAC,CAAC,CAACvG,GAAG,CAAC,CAAC0G,CAAC,EAAEb,CAAC,kBAC1B3I,OAAA;QAAasJ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACxCvJ,OAAA;UAAKsJ,SAAS,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1C5J,OAAA;UAAKsJ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvJ,OAAA;YAAKsJ,SAAS,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3C5J,OAAA;YAAKsJ,SAAS,EAAC;UAAuB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5J,OAAA;YAAKsJ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvJ,OAAA;cAAKsJ,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC5J,OAAA;cAAKsJ,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpC5J,OAAA;cAAKsJ,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACN5J,OAAA;YAAKsJ,SAAS,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjD5J,OAAA;YAAKsJ,SAAS,EAAC;UAAiC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA,GAZEjB,CAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAEC,oBACE5J,OAAA;IAAKsJ,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCvJ,OAAA,CAACF,UAAU;MACT+J,KAAK,EAAC,8BAA8B;MACpCjF,WAAW,EAAC;IAAyF;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtG,CAAC,eACF5J,OAAA;MAAKsJ,SAAS,EAAC;IAAiB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGvC5J,OAAA;MAASsJ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACvCvJ,OAAA,CAACP,UAAU;QAAAgK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGV5J,OAAA;MAASsJ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCvJ,OAAA,CAACR,MAAM;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGV5J,OAAA;MAAKsJ,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCvJ,OAAA;QAAKsJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BvJ,OAAA;UAAKsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvJ,OAAA;YAAIsJ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC5J,OAAA;YACEsJ,SAAS,EAAC,mBAAmB;YAC7BQ,OAAO,EAAEd,mBAAoB;YAC7B,cAAW,gBAAgB;YAAAO,QAAA,gBAE3BvJ,OAAA,CAAC1B,eAAe;cAACyL,IAAI,EAAEnL;YAAS;cAAA6K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5J,OAAA;UAAKsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BvJ,OAAA;YACEsJ,SAAS,EAAE,OAAO3I,SAAS,KAAK,KAAK,GAAG,mBAAmB,GAAG,EAAE,EAAG;YAEnEmJ,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAC,KAAK,CAAE;YAAAU,QAAA,EACvC;UAED,GAJM,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIL,CAAC,eACP5J,OAAA;YACEsJ,SAAS,EAAE,OAAO3I,SAAS,KAAK,iBAAiB,GAAG,mBAAmB,GAAG,EAAE,EAAG;YAE/EmJ,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAC,iBAAiB,CAAE;YAAAU,QAAA,EACnD;UAED,GAJM,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIL,CAAC,eACP5J,OAAA;YACEsJ,SAAS,EAAE,OAAO3I,SAAS,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;YAEhFmJ,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAC,cAAc,CAAE;YAAAU,QAAA,EAChD;UAED,GAJM,SAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIT,CAAC,eACP5J,OAAA;YACEsJ,SAAS,EAAE,OAAO3I,SAAS,KAAK,cAAc,GAAG,uBAAuB,GAAG,EAAE,EAAG;YAEhFmJ,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAC,cAAc,CAAE;YAAAU,QAAA,EAChD;UAED,GAJM,SAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIT,CAAC,eACP5J,OAAA;YACEsJ,SAAS,EAAE,OAAO3I,SAAS,KAAK,aAAa,GAAG,2BAA2B,GAAG,EAAE,EAAG;YAEnFmJ,OAAO,EAAEA,CAAA,KAAMjB,eAAe,CAAC,aAAa,CAAE;YAAAU,QAAA,EAC/C;UAED,GAJM,aAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5J,OAAA;QAAKsJ,SAAS,EAAC,aAAa;QAAAC,QAAA,GAAC,YAAU,eACrCvJ,OAAA;UAAKsJ,SAAS,EAAC,aAAa;UAAAC,QAAA,GACzBhJ,OAAO,gBACNP,OAAA;YAAKsJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvJ,OAAA,CAACb,SAAS;cAACmK,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC5J,OAAA;cAAAuJ,QAAA,EAAG;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,GACJvJ,OAAO,CAACmB,MAAM,GAAG,CAAC,GACpBnB,OAAO,CAACwH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/E,GAAG,CAACC,GAAG,iBACzB/C,OAAA,CAACH,aAAa;YAEZkD,GAAG,EAAEA,GAAI;YACTiH,eAAe,EAAEf;UAAmB,GAF/B,UAAUlG,GAAG,CAACC,EAAE,EAAE;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGxB,CACF,CAAC,gBAEF5J,OAAA;YAAKsJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BvJ,OAAA;cAAAuJ,QAAA,EAAG;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,eAGD5J,OAAA;YAAKsJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvJ,OAAA;cAAAuJ,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB5J,OAAA;cAAAuJ,QAAA,EAAG;YAAuD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D5J,OAAA;cAAKiK,GAAG,EAAC,6GAA6G;cAACC,GAAG,EAAC,YAAY;cAACZ,SAAS,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvK5J,OAAA;cAAGmK,IAAI,EAAC,UAAU;cAAAZ,QAAA,eAChBvJ,OAAA;gBAAQsJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5J,OAAA;UAAKsJ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3B9I,KAAK,iBACJT,OAAA;YAAKsJ,SAAS,EAAC,oBAAoB;YAACc,KAAK,EAAE;cACzCC,KAAK,EAAE,SAAS;cAChBC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,OAAO,EAAE,WAAW;cACpBC,MAAM,EAAE,WAAW;cACnBC,SAAS,EAAE,QAAQ;cACnBC,QAAQ,EAAE,MAAM;cAChBC,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE;YACb,CAAE;YAAAvB,QAAA,EACC9I;UAAK;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EACArJ,OAAO,gBACNP,OAAA;YAAKsJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BvJ,OAAA,CAACb,SAAS;cAACmK,SAAS,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC5J,OAAA;cAAAuJ,QAAA,EAAG;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,GACJ5B,WAAW,CAACxG,MAAM,GAAG,CAAC,GACxBwG,WAAW,CAAClF,GAAG,CAACC,GAAG,iBACjB/C,OAAA,CAAC3B,IAAI;YAEH0M,EAAE,EAAE,QAAQhI,GAAG,CAACC,EAAE,EAAG;YACrBsG,SAAS,EAAE,gCAAgC,CAACvG,GAAG,CAACS,QAAQ,IAAI,EAAE,EAAEwH,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAG;YACrGb,KAAK,EAAE;cAAEc,UAAU,EAAE,MAAM;cAAEC,cAAc,EAAE,MAAM;cAAEd,KAAK,EAAE,SAAS;cAAEnH,QAAQ,EAAE;YAAW,CAAE;YAC9F4G,OAAO,EAAEA,CAAA,KAAMb,kBAAkB,CAAClG,GAAG,CAACC,EAAE,CAAE;YAAAuG,QAAA,GAEzCxG,GAAG,CAAC2B,GAAG,iBACN1E,OAAA;cAAMsJ,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAChE,eACD5J,OAAA;cAAKsJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BvJ,OAAA;gBAAKsJ,SAAS,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5J,OAAA;cAAKsJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BvJ,OAAA;gBAAKsJ,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,eAClCvJ,OAAA;kBACEiK,GAAG,EAAElH,GAAG,CAACqB,KAAM;kBACf8F,GAAG,EAAEnH,GAAG,CAACG,QAAS;kBAClBoG,SAAS,EAAC,eAAe;kBACzB8B,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACrB,GAAG,GAAG,oDAAoD;kBACrE;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5J,OAAA;gBAAKsJ,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BvJ,OAAA;kBAAIsJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjCxG,GAAG,CAACG;gBAAQ;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACT5J,OAAA;kBAAKsJ,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCvJ,OAAA;oBAAKsJ,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClCvJ,OAAA;sBACEiK,GAAG,EAAElH,GAAG,CAACwB,IAAK;sBACd2F,GAAG,EAAEnH,GAAG,CAACK,OAAQ;sBACjBgI,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;wBACvBF,CAAC,CAACC,MAAM,CAACrB,GAAG,GAAG,gCAAgC;sBACjD;oBAAE;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN5J,OAAA;oBAAKsJ,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eACrCvJ,OAAA;sBAAIsJ,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAExG,GAAG,CAACK;oBAAO;sBAAAqG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5J,OAAA;kBAAKsJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvJ,OAAA;oBAAMsJ,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAChCvJ,OAAA,CAAC1B,eAAe;sBAACyL,IAAI,EAAEjL;oBAAY;sBAAA2K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC7G,GAAG,CAACO,QAAQ;kBAAA;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACP5J,OAAA;oBAAMsJ,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBACjCvJ,OAAA,CAAC1B,eAAe;sBAACyL,IAAI,EAAEvL;oBAAQ;sBAAAiL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,KAAC,EAAC7G,GAAG,CAACS,QAAQ;kBAAA;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACP5J,OAAA;oBAAMsJ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,EAC7B1F,YAAY,CAACd,GAAG,CAACW,UAAU,EAAEX,GAAG,CAACY,UAAU;kBAAC;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5J,OAAA;kBAAKsJ,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,eACzCvJ,OAAA;oBAAAuJ,QAAA,EAAIxG,GAAG,CAAC6B,WAAW,GAAG7B,GAAG,CAAC6B,WAAW,CAAC7C,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG;kBAA2B;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5J,OAAA;cAAKsJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BvJ,OAAA;gBAAMsJ,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAC,GAAC,EAACxG,GAAG,CAACiB,UAAU;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD5J,OAAA;gBAAKsJ,SAAS,EAAC,eAAe;gBAACc,KAAK,EAAE;kBAAEoB,UAAU,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,eAC3DvJ,OAAA;kBACEsJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EACjC;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/ED,WAAW7G,GAAG,CAACC,EAAE,EAAE;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgFpB,CACP,CAAC,gBAEF5J,OAAA;YAAKsJ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCvJ,OAAA;cAAKsJ,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BvJ,OAAA,CAAC1B,eAAe;gBAACyL,IAAI,EAAEjL;cAAY;gBAAA2K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACN5J,OAAA;cAAAuJ,QAAA,EAAI;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB5J,OAAA;cAAAuJ,QAAA,EAAG;YAAuD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D5J,OAAA;cAAAuJ,QAAA,EAAG;YAA+C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACrD,CAACnJ,KAAK,IAAI,CAACF,OAAO,IAAIJ,IAAI,CAACqB,MAAM,KAAK,CAAC,iBACtCxB,OAAA;cAAKoK,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEoB,SAAS,EAAE;cAAO,CAAE;cAAAlC,QAAA,EAAC;YAElD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAGD5J,OAAA;YAAKsJ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvJ,OAAA;cACEsJ,SAAS,EAAC,YAAY;cAEtBQ,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACtH,WAAW,GAAG,CAAC,CAAE;cACjD6K,QAAQ,EAAE7K,WAAW,KAAK,CAAE;cAAA0I,QAAA,eAE5BvJ,OAAA,CAAC1B,eAAe;gBAACyL,IAAI,EAAErL;cAAc;gBAAA+K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAJpC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CAAC,EAERrB,WAAW,CAACzF,GAAG,CAAC6I,MAAM,iBACrB3L,OAAA;cAEEsJ,SAAS,EAAE,eAAezI,WAAW,KAAK8K,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cACnE7B,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACwD,MAAM,CAAE;cAAApC,QAAA,EAEvCoC;YAAM,GAJF,QAAQA,MAAM,EAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKf,CACT,CAAC,eAEF5J,OAAA;cACEsJ,SAAS,EAAC,YAAY;cAEtBQ,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAACtH,WAAW,GAAG,CAAC,CAAE;cACjD6K,QAAQ,EAAE7K,WAAW,KAAKoH,UAAU,IAAIA,UAAU,KAAK,CAAE;cAAAsB,QAAA,eAEzDvJ,OAAA,CAAC1B,eAAe;gBAACyL,IAAI,EAAEpL;cAAe;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAJrC,MAAM;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CAAC,EAER3B,UAAU,GAAGO,YAAY,iBACxBxI,OAAA;cAAMsJ,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAY,KAClC,EAACtB,UAAU,EAAC,QACjB;YAAA,GAFgC,MAAM;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5J,OAAA;UAAKsJ,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BvJ,OAAA;YAAKsJ,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BvJ,OAAA;cAAKsJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BvJ,OAAA;gBAAAuJ,QAAA,EAAI;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC5J,OAAA;gBAAKsJ,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvJ,OAAA;kBACEsJ,SAAS,EAAC,uBAAuB;kBACjCQ,OAAO,EAAExI,wBAAyB;kBAClCuI,KAAK,EAAC,yBAAyB;kBAC/BO,KAAK,EAAE;oBACLc,UAAU,EAAE,MAAM;oBAClBX,MAAM,EAAE,gBAAgB;oBACxBC,YAAY,EAAE,KAAK;oBACnBC,OAAO,EAAE,SAAS;oBAClBmB,MAAM,EAAE,SAAS;oBACjBhB,QAAQ,EAAE,MAAM;oBAChBP,KAAK,EAAE,MAAM;oBACbwB,WAAW,EAAE;kBACf,CAAE;kBAAAtC,QAAA,eAEFvJ,OAAA,CAAC1B,eAAe;oBAACyL,IAAI,EAAE7K;kBAAO;oBAAAuK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,EACR7I,iBAAiB,CAACS,MAAM,GAAG,CAAC,iBAC3BxB,OAAA;kBAAQsJ,SAAS,EAAC,mBAAmB;kBAACQ,OAAO,EAAEf,oBAAqB;kBAAAQ,QAAA,GAAC,YACzD,eAAAvJ,OAAA,CAAC1B,eAAe;oBAACyL,IAAI,EAAElL;kBAAQ;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5J,OAAA;cAAKsJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BpI,kBAAkB,CAAC2B,GAAG,CAAC,CAACjB,KAAK,EAAEiK,KAAK,kBACnC9L,OAAA;gBAEEsJ,SAAS,EAAE,qBAAqBvI,iBAAiB,CAACqG,QAAQ,CAACvF,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAA0H,QAAA,gBAEtFvJ,OAAA;kBACE+L,IAAI,EAAC,UAAU;kBACf/I,EAAE,EAAE,YAAY8I,KAAK,EAAG;kBACxBE,OAAO,EAAEjL,iBAAiB,CAACqG,QAAQ,CAACvF,KAAK,CAAE;kBAC3CoK,QAAQ,EAAEA,CAAA,KAAM/E,oBAAoB,CAACrF,KAAK;gBAAE;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACF5J,OAAA;kBAAOkM,OAAO,EAAE,YAAYJ,KAAK,EAAG;kBAACjC,KAAK,EAAEhI,KAAM;kBAAA0H,QAAA,EAC/C3H,gBAAgB,CAACC,KAAK;gBAAC;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA,GAXH,YAAYkC,KAAK,EAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYrB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5J,OAAA;cAAKsJ,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC9BxI,iBAAiB,CAACS,MAAM,GAAG,CAAC,gBAC3BxB,OAAA;gBAAAuJ,QAAA,gBACEvJ,OAAA;kBAAKsJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BvJ,OAAA,CAAC1B,eAAe;oBAACyL,IAAI,EAAEnL;kBAAS;oBAAA6K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,qBACrC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5J,OAAA;kBAAKsJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAClCxI,iBAAiB,CAAC+B,GAAG,CAAC,CAACjB,KAAK,EAAEsK,GAAG,kBAChCnM,OAAA;oBAA8BsJ,SAAS,EAAC,oBAAoB;oBAACO,KAAK,EAAEhI,KAAM;oBAAA0H,QAAA,GACvE3H,gBAAgB,CAACC,KAAK,EAAE,EAAE,CAAC,eAC5B7B,OAAA;sBACEsJ,SAAS,EAAC,cAAc;sBACxBQ,OAAO,EAAEA,CAAA,KAAM5C,oBAAoB,CAACrF,KAAK,CAAE;sBAAA0H,QAAA,eAE3CvJ,OAAA,CAAC1B,eAAe;wBAACyL,IAAI,EAAElL;sBAAQ;wBAAA4K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA,GAPA,YAAYuC,GAAG,EAAE;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQtB,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEN5J,OAAA;gBAAKsJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACpD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5J,OAAA,CAACN,eAAe;QAAA+J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL3I,mBAAmB,iBAClBjB,OAAA;MAAKsJ,SAAS,EAAC,wBAAwB;MAACQ,OAAO,EAAEd;IAAoB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC5E,eAGD5J,OAAA;MAAKsJ,SAAS,EAAE,kBAAkBrI,mBAAmB,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAsI,QAAA,gBACpEvJ,OAAA;QAAKsJ,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCvJ,OAAA;UAAAuJ,QAAA,gBAAIvJ,OAAA,CAAC1B,eAAe;YAACyL,IAAI,EAAEnL;UAAS;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD5J,OAAA;UAAQsJ,SAAS,EAAC,mBAAmB;UAACQ,OAAO,EAAEd,mBAAoB;UAAAO,QAAA,eACjEvJ,OAAA,CAAC1B,eAAe;YAACyL,IAAI,EAAElL;UAAQ;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5J,OAAA;QAAKsJ,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCvJ,OAAA;UAAKsJ,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BvJ,OAAA;YAAKsJ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BvJ,OAAA;cAAAuJ,QAAA,EAAI;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChC7I,iBAAiB,CAACS,MAAM,GAAG,CAAC,iBAC3BxB,OAAA;cAAQsJ,SAAS,EAAC,mBAAmB;cAACQ,OAAO,EAAEf,oBAAqB;cAAAQ,QAAA,GAAC,YACzD,eAAAvJ,OAAA,CAAC1B,eAAe;gBAACyL,IAAI,EAAElL;cAAQ;gBAAA4K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN5J,OAAA;YAAKsJ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BpI,kBAAkB,CAAC2B,GAAG,CAAC,CAACjB,KAAK,EAAEiK,KAAK,kBACnC9L,OAAA;cAEEsJ,SAAS,EAAE,qBAAqBvI,iBAAiB,CAACqG,QAAQ,CAACvF,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cAAA0H,QAAA,gBAEtFvJ,OAAA;gBACE+L,IAAI,EAAC,UAAU;gBACf/I,EAAE,EAAE,oBAAoB8I,KAAK,EAAG;gBAChCE,OAAO,EAAEjL,iBAAiB,CAACqG,QAAQ,CAACvF,KAAK,CAAE;gBAC3CoK,QAAQ,EAAEA,CAAA,KAAM/E,oBAAoB,CAACrF,KAAK;cAAE;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACF5J,OAAA;gBAAOkM,OAAO,EAAE,oBAAoBJ,KAAK,EAAG;gBAACjC,KAAK,EAAEhI,KAAM;gBAAA0H,QAAA,EACvD3H,gBAAgB,CAACC,KAAK;cAAC;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA,GAXH,oBAAoBkC,KAAK,EAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAY7B,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5J,OAAA;YAAKsJ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BxI,iBAAiB,CAACS,MAAM,GAAG,CAAC,gBAC3BxB,OAAA;cAAAuJ,QAAA,gBACEvJ,OAAA;gBAAKsJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BvJ,OAAA,CAAC1B,eAAe;kBAACyL,IAAI,EAAEnL;gBAAS;kBAAA6K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBACrC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5J,OAAA;gBAAKsJ,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClCxI,iBAAiB,CAAC+B,GAAG,CAAC,CAACjB,KAAK,EAAEsK,GAAG,kBAChCnM,OAAA;kBAAsCsJ,SAAS,EAAC,oBAAoB;kBAACO,KAAK,EAAEhI,KAAM;kBAAA0H,QAAA,GAC/E3H,gBAAgB,CAACC,KAAK,EAAE,EAAE,CAAC,eAC5B7B,OAAA;oBACEsJ,SAAS,EAAC,cAAc;oBACxBQ,OAAO,EAAEA,CAAA,KAAM5C,oBAAoB,CAACrF,KAAK,CAAE;oBAAA0H,QAAA,eAE3CvJ,OAAA,CAAC1B,eAAe;sBAACyL,IAAI,EAAElL;oBAAQ;sBAAA4K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA,GAPA,oBAAoBuC,GAAG,EAAE;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQ9B,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN5J,OAAA;cAAKsJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACpD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAWN5J,OAAA;MAASsJ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAClCvJ,OAAA,CAACR,MAAM;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACV5J,OAAA;MAASsJ,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACrCvJ,OAAA,CAACL,gBAAgB;QAAA8J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1J,EAAA,CA/xBID,QAAQ;AAAAmM,EAAA,GAARnM,QAAQ;AAiyBd,eAAeA,QAAQ;AAAC,IAAAmM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}