/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faEdit,
  faTrash,
  faSearch,
  faExclamationCircle,
  faCheckCircle,
  faTimes,
  faSpinner,
  faExclamationTriangle,
  faSync,
  faTags,
  faSave
} from '@fortawesome/free-solid-svg-icons';
import '../../css/SubTopicsAdmin.css';
import '../../css/shared-delete-dialog.css';
import ApiService from '../../services/apiService';
import { getAllSubTopics, saveSubTopics, initializeSubTopics } from '../../utils/subTopicsUtils';

const SubTopicsAdmin = () => {
  const [subTopics, setSubTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSubTopic, setSelectedSubTopic] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [subTopicToDelete, setSubTopicToDelete] = useState(null);

  // Form state
  const [subTopicForm, setSubTopicForm] = useState({
    name: ''
  });

  // Load sub-topics from localStorage or initialize with predefined list
  useEffect(() => {
    const loadSubTopics = () => {
      try {
        setLoading(true);

        // Clean up old format data that might have description/usageCount
        const existingData = localStorage.getItem('adminSubTopics');
        if (existingData) {
          try {
            const parsed = JSON.parse(existingData);
            // Check if old format (has description or usageCount)
            if (parsed.length > 0 && (parsed[0].description !== undefined || parsed[0].usageCount !== undefined)) {
              // Clear old format data
              localStorage.removeItem('adminSubTopics');
            }
          } catch (e) {
            // If parsing fails, clear the data
            localStorage.removeItem('adminSubTopics');
          }
        }

        const loadedSubTopics = initializeSubTopics();
        setSubTopics(loadedSubTopics);
        setError(null);
      } catch (err) {
        console.error("Error loading sub-topics:", err);
        setError("Failed to load sub-topics");
      } finally {
        setLoading(false);
      }
    };

    loadSubTopics();
  }, []);

  // Save sub-topics to localStorage whenever subTopics state changes
  useEffect(() => {
    if (subTopics.length > 0) {
      saveSubTopics(subTopics);
    }
  }, [subTopics]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const openNewSubTopicModal = () => {
    setIsModalOpen(true);
    setSelectedSubTopic(null);
    setSubTopicForm({
      name: ''
    });
  };

  const handleEditSubTopic = (subTopic) => {
    setSelectedSubTopic(subTopic);
    setSubTopicForm({
      name: subTopic.name
    });
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedSubTopic(null);
    setSubTopicForm({
      name: ''
    });
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setSubTopicForm({
      ...subTopicForm,
      [name]: value
    });
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    // Client-side validation
    if (!subTopicForm.name || !subTopicForm.name.trim()) {
      setError("Sub-topic name is required");
      return;
    }

    // Check for duplicate names
    const trimmedName = subTopicForm.name.trim();
    const isDuplicate = subTopics.some(topic =>
      topic.name.toLowerCase() === trimmedName.toLowerCase() &&
      (!selectedSubTopic || topic.id !== selectedSubTopic.id)
    );

    if (isDuplicate) {
      setError("A sub-topic with this name already exists");
      return;
    }

    try {
      setLoading(true);

      if (selectedSubTopic) {
        // Update existing sub-topic
        const updatedSubTopics = subTopics.map(topic =>
          topic.id === selectedSubTopic.id
            ? {
                ...topic,
                name: trimmedName
              }
            : topic
        );
        setSubTopics(updatedSubTopics);
      } else {
        // Create new sub-topic
        const newSubTopic = {
          id: Math.max(...subTopics.map(t => t.id), 0) + 1,
          name: trimmedName,
          isActive: true,
          createdAt: new Date().toISOString()
        };
        setSubTopics([...subTopics, newSubTopic]);
      }

      setError(null);
      closeModal();
    } catch (err) {
      console.error("Error saving sub-topic:", err);
      setError(selectedSubTopic ? "Failed to update sub-topic" : "Failed to create sub-topic");
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = (subTopic) => {
    setSubTopicToDelete(subTopic);
    setShowDeleteConfirm(true);
  };

  const handleDeleteSubTopic = async () => {
    try {
      setLoading(true);
      const updatedSubTopics = subTopics.filter(topic => topic.id !== subTopicToDelete.id);
      setSubTopics(updatedSubTopics);
      setError(null);
      setShowDeleteConfirm(false);
      setSubTopicToDelete(null);
    } catch (err) {
      console.error("Error deleting sub-topic:", err);
      setError("Failed to delete sub-topic");
    } finally {
      setLoading(false);
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setSubTopicToDelete(null);
  };

  const toggleSubTopicStatus = (subTopicId) => {
    const updatedSubTopics = subTopics.map(topic =>
      topic.id === subTopicId
        ? { ...topic, isActive: !topic.isActive }
        : topic
    );
    setSubTopics(updatedSubTopics);
  };

  const filteredSubTopics = subTopics.filter(topic =>
    topic.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    } catch (err) {
      return '-';
    }
  };

  return (
    <div className="subtopics-admin-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this sub-topic?</p>
              <p><strong>{subTopicToDelete?.name}</strong></p>
              <p>This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={handleDeleteSubTopic}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="subtopics-header">
        <h1 className="subtopics-title">
          <FontAwesomeIcon icon={faTags} className="title-icon" />
          Sub Topics Management
        </h1>
        <div className="subtopic-header-actions">
          <button className="refresh-button" onClick={() => window.location.reload()} disabled={loading}>
            <FontAwesomeIcon icon={faSync} />
            <span>Refresh</span>
          </button>
          <button className="add-subtopic-button" onClick={openNewSubTopicModal} disabled={loading}>
            <FontAwesomeIcon icon={faPlus} />
            <span>Add New Sub Topic</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FontAwesomeIcon icon={faExclamationCircle} />
          <span>{error}</span>
        </div>
      )}

      <div className="filters-container">
        <div className="search-container">
          <div className="search-input-wrapper">
            <FontAwesomeIcon icon={faSearch} className="search-icon" />
            <input
              type="text"
              placeholder="Search sub-topics..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="search-input"
              style={{ paddingLeft: '40px' }}
            />
          </div>
        </div>
      </div>

      <div className="table-container">
        {loading ? (
          <div className="loading-container">
            <FontAwesomeIcon icon={faSpinner} spin size="2x" />
            <span>Loading sub-topics...</span>
          </div>
        ) : (
          <table className="subtopics-table">
            <thead>
              <tr>
                <th>Sub Topic Name</th>
                <th>Status</th>
                <th>Created Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredSubTopics.length > 0 ? (
                filteredSubTopics.map(topic => (
                  <tr key={topic.id}>
                    <td>
                      <div className="subtopic-name" title={topic.name}>
                        {topic.name}
                      </div>
                    </td>
                    <td>
                      <span className={`status-badge ${topic.isActive ? 'active' : 'inactive'}`}>
                        {topic.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td>{formatDate(topic.createdAt)}</td>
                    <td>
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <button 
                          className="action-button edit-button" 
                          onClick={() => handleEditSubTopic(topic)}
                          title="Edit sub-topic"
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                        <button 
                          className={`action-button ${topic.isActive ? 'deactivate-button' : 'activate-button'}`}
                          onClick={() => toggleSubTopicStatus(topic.id)}
                          title={topic.isActive ? 'Deactivate' : 'Activate'}
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={topic.isActive ? faTimes : faCheckCircle} />
                        </button>
                        <button 
                          className="action-button delete-button" 
                          onClick={() => confirmDelete(topic)}
                          title="Delete sub-topic"
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="4" className="no-subtopics-message">
                    {searchTerm ?
                      "No sub-topics match your search criteria" :
                      "No sub-topics available"
                    }
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>

      {/* Sub Topic Modal */}
      {isModalOpen && (
        <div className="modal-overlay" onClick={(e) => {
          if (e.target.className === 'modal-overlay') {
            closeModal();
          }
        }}>
          <div className="subtopic-modal">
            <div className="modal-header">
              <h2>
                <FontAwesomeIcon icon={faTags} className="modal-icon" />
                {selectedSubTopic ? 'Edit Sub Topic' : 'Add New Sub Topic'}
              </h2>
              <button className="close-button" onClick={closeModal}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <div className="modal-content">
              <form onSubmit={handleFormSubmit}>
                <div className="form-group">
                  <label htmlFor="name">Sub Topic Name <span style={{ color: 'red' }}>*</span></label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={subTopicForm.name}
                    onChange={handleFormChange}
                    required
                    placeholder="Enter sub-topic name"
                  />
                </div>

                <div className="modal-footer">
                  <button type="button" className="cancel-button" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="submit-button">
                    <FontAwesomeIcon icon={faSave} />
                    {selectedSubTopic ? 'Update Sub Topic' : 'Create Sub Topic'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubTopicsAdmin;
