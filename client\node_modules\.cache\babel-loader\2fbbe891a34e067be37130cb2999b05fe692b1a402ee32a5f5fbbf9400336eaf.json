{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\JobBoard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport JobCard from './JobCard';\nimport '../css/JobBoard.css';\nimport { ApiService } from '../services/apiService';\n// Import Font Awesome\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faMapMarkerAlt, faBriefcase, faBuilding, faChartLine, faDollarSign, faBell, faChevronDown, faFilter, faTimes } from '@fortawesome/free-solid-svg-icons';\nimport { FaSpinner } from 'react-icons/fa';\nimport { useLocation } from '../contexts/LocationContext';\nimport { useJobFilter } from '../contexts/JobFilterContext';\nimport PageHelmet from './PageHelmet';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst JobBoard = () => {\n  _s();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const {\n    selectedLocation,\n    setSelectedLocation,\n    districts\n  } = useLocation();\n  const [jobsArray, setJobsArray] = useState([]);\n  const [filteredJobs, setFilteredJobs] = useState([]);\n  const [searchKeyword, setSearchKeyword] = useState('');\n  const [workLocationTypes, setWorkLocationTypes] = useState({\n    onsite: false,\n    remote: false,\n    hybrid: false\n  });\n\n  // Use shared job filter context\n  const {\n    jobType: selectedJobType,\n    updateJobType,\n    minSalary,\n    maxSalary,\n    salaryFilterApplied,\n    updateSalaryRange\n  } = useJobFilter();\n\n  // Add state for Employment Type and Experience Level filters\n  const [selectedEmploymentTypes, setSelectedEmploymentTypes] = useState({\n    'Full Time Jobs': false,\n    'Part Time Jobs': false,\n    'Remote Jobs': false,\n    'Freelance': false,\n    'Temporary': false\n  });\n  const [selectedExperienceLevels, setSelectedExperienceLevels] = useState({\n    \"No Experience Required\": false,\n    \"Entry Level\": false,\n    \"Mid Level\": false,\n    \"Senior Level\": false,\n    \"Manager\": false,\n    \"Executive\": false\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        // Add a timestamp to prevent caching, like HomePage (MCP style)\n        const response = await ApiService.jobs.getAll({\n          _t: new Date().getTime()\n        });\n        console.log('RAW API RESPONSE - First 3 jobs:');\n        response.data.slice(0, 3).forEach((job, index) => {\n          console.log(`Job ${index + 1}:`, {\n            job_title: job.job_title,\n            experience_level: job.experience_level,\n            job_type: job.job_type\n          });\n        });\n\n        // Map backend jobs to the format expected by JobCard, and sort newest first (like HomePage)\n        const jobs = response.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\n          logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          // Compose salary string from min/max\n          salary: formatSalary(job.min_salary, job.max_salary),\n          postedTime: job.start_date ? formatPostedDate(job.start_date) : '',\n          views: Math.floor(Math.random() * 1000),\n          status: job.status || 'Active',\n          description: job.job_description || 'No description provided',\n          created_at: job.created_at || job.start_date,\n          // Store raw salary values for filtering\n          minSalary: job.min_salary ? Number(job.min_salary) : null,\n          maxSalary: job.max_salary ? Number(job.max_salary) : null,\n          // Add experience level for filtering\n          experience_level: job.experience_level || ''\n        }));\n        // Debug: Log first few jobs to see what experience_level values we're getting\n        console.log('JobBoard - Sample job data with experience levels:', jobs.slice(0, 3).map(job => ({\n          position: job.position,\n          experience_level: job.experience_level,\n          workTime: job.workTime\n        })));\n\n        // Debug: Log unique experience levels found in data\n        const uniqueExpLevels = [...new Set(jobs.map(job => job.experience_level).filter(Boolean))];\n        console.log('JobBoard - Unique experience levels in data:', uniqueExpLevels);\n        const filteredActiveJobs = jobs\n        // Only show jobs with status 'Active'\n        .filter(job => job.status === 'Active').sort((a, b) => {\n          // Sort by creation date descending (newest first), then by ID descending as fallback\n          const dateA = new Date(a.created_at || a.start_date || a.posted_date || new Date());\n          const dateB = new Date(b.created_at || b.start_date || b.posted_date || new Date());\n          if (dateB.getTime() !== dateA.getTime()) {\n            return dateB.getTime() - dateA.getTime();\n          }\n\n          // If dates are the same, sort by ID descending (higher ID = newer)\n          return b.id - a.id;\n        });\n        setJobsArray(filteredActiveJobs);\n        setFilteredJobs(filteredActiveJobs);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load jobs from server');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchJobs();\n  }, []);\n\n  // Handle URL search parameters\n  useEffect(() => {\n    console.log('=== URL PARAMETERS DEBUG ===');\n    const search = searchParams.get('search');\n    const location = searchParams.get('location');\n    console.log('URL Parameters:', {\n      search,\n      jobType: selectedJobType,\n      location,\n      minSal: minSalary,\n      maxSal: maxSalary\n    });\n    if (search) {\n      console.log('Setting search keyword to:', search);\n      setSearchKeyword(search);\n    }\n    if (location) {\n      console.log('Setting location to:', location);\n      setSelectedLocation(location);\n    }\n    console.log('=== END URL PARAMETERS DEBUG ===');\n  }, [searchParams, setSelectedLocation]);\n\n  // For salary range\n  const [salaryRange, setSalaryRange] = useState([0, 1000000]);\n  const [sliderValues, setSliderValues] = useState([0, 100]); // Percentage values for slider\n\n  // Sync salary range with context\n  useEffect(() => {\n    if (minSalary !== '0' || maxSalary !== '1000000') {\n      const newSalaryRange = [parseInt(minSalary), parseInt(maxSalary)];\n      setSalaryRange(newSalaryRange);\n\n      // Update slider values to match the salary range\n      const minValue = 0;\n      const maxValue = 1000000;\n      const range = maxValue - minValue;\n      const newSliderValues = [(newSalaryRange[0] - minValue) / range * 100, (newSalaryRange[1] - minValue) / range * 100];\n      setSliderValues(newSliderValues);\n    }\n  }, [minSalary, maxSalary]);\n\n  // Filter jobs based on selected job category, location, and salary range\n  useEffect(() => {\n    let filtered = jobsArray;\n\n    // Filter by search keyword\n    if (searchKeyword.trim()) {\n      console.log('=== SEARCH KEYWORD FILTER DEBUG ===');\n      console.log('Search keyword:', searchKeyword);\n      console.log('Jobs before search filtering:', filtered.length);\n      filtered = filtered.filter(job => {\n        const jobTitle = (job.position || '').toLowerCase();\n        const jobDescription = (job.description || '').toLowerCase();\n        const companyName = (job.company || '').toLowerCase();\n        const searchTerm = searchKeyword.toLowerCase();\n        const titleMatch = jobTitle.includes(searchTerm);\n        const descMatch = jobDescription.includes(searchTerm);\n        const companyMatch = companyName.includes(searchTerm);\n        const matches = titleMatch || descMatch || companyMatch;\n        if (matches) {\n          console.log(`MATCH FOUND - Job: \"${job.position}\" at ${job.company}`);\n          console.log(`  Title match: ${titleMatch}, Desc match: ${descMatch}, Company match: ${companyMatch}`);\n        }\n        return matches;\n      });\n      console.log('Jobs after search filtering:', filtered.length);\n      console.log('=== END SEARCH KEYWORD FILTER DEBUG ===');\n    }\n\n    // Filter by job type (matching SearchArea dropdown options)\n    if (selectedJobType !== '' && selectedJobType !== 'all') {\n      console.log('=== JOB TYPE FILTER DEBUG ===');\n      console.log('Selected job type:', selectedJobType);\n      console.log('Jobs before job type filtering:', filtered.length);\n      filtered = filtered.filter(job => {\n        const jobWorkTime = (job.workTime || '').toLowerCase();\n        const selectedType = selectedJobType.toLowerCase();\n        console.log(`Job \"${job.position}\": workTime=\"${job.workTime}\", matches=${jobWorkTime === selectedType}`);\n\n        // Direct match with job's workTime field\n        return jobWorkTime === selectedType;\n      });\n      console.log('Jobs after job type filtering:', filtered.length);\n      console.log('=== END JOB TYPE FILTER DEBUG ===');\n    }\n\n    // Filter by location\n    if (selectedLocation !== '' && selectedLocation !== 'all') {\n      filtered = filtered.filter(job => {\n        // Check if job location/description contains the selected district\n        const jobLocation = job.location || '';\n        const jobDescription = job.description || '';\n        const searchText = (jobLocation + ' ' + jobDescription).toLowerCase();\n        return searchText.includes(selectedLocation.toLowerCase());\n      });\n    } // Filter by salary range if applied\n    if (salaryFilterApplied) {\n      filtered = filtered.filter(job => {\n        const [minFilter, maxFilter] = salaryRange;\n\n        // If minimum filter is 0, show all jobs including those without salary data\n        if (minFilter === 0 && job.minSalary === null && job.maxSalary === null) {\n          return true;\n        }\n\n        // If job has no salary data and min filter > 0, exclude it when filtering\n        if (job.minSalary === null && job.maxSalary === null && minFilter > 0) {\n          return false;\n        }\n\n        // Check if job salary range overlaps with filter range\n        const jobMin = job.minSalary || 0;\n        const jobMax = job.maxSalary || job.minSalary || 999999;\n\n        // Job overlaps with filter if:\n        // - Job's max salary is >= filter's min salary\n        // - Job's min salary is <= filter's max salary\n        return jobMax >= minFilter && jobMin <= maxFilter;\n      });\n    }\n\n    // Filter by work location types (onsite, remote, hybrid)\n    const selectedWorkTypes = Object.keys(workLocationTypes).filter(key => workLocationTypes[key]);\n    if (selectedWorkTypes.length > 0) {\n      filtered = filtered.filter(job => {\n        const jobWorkType = (job.workTime || '').toLowerCase();\n        const jobDescription = (job.description || '').toLowerCase();\n        return selectedWorkTypes.some(type => {\n          switch (type) {\n            case 'remote':\n              return jobWorkType.includes('remote') || jobDescription.includes('remote') || jobWorkType.includes('work from home');\n            case 'onsite':\n              return jobWorkType.includes('onsite') || jobWorkType.includes('on-site') || jobWorkType.includes('office') || !jobWorkType.includes('remote') && !jobWorkType.includes('hybrid') && !jobDescription.includes('remote');\n            case 'hybrid':\n              return jobWorkType.includes('hybrid') || jobDescription.includes('hybrid');\n            default:\n              return false;\n          }\n        });\n      });\n    } // Filter by Employment Type\n    const selectedEmpTypes = Object.keys(selectedEmploymentTypes).filter(key => selectedEmploymentTypes[key]);\n    if (selectedEmpTypes.length > 0) {\n      filtered = filtered.filter(job => {\n        const jobWorkTime = job.workTime || '';\n        return selectedEmpTypes.includes(jobWorkTime);\n      });\n    } // Filter by Experience Level  \n    const selectedExpLevels = Object.keys(selectedExperienceLevels).filter(key => selectedExperienceLevels[key]);\n    if (selectedExpLevels.length > 0) {\n      console.log('=== EXPERIENCE LEVEL FILTER DEBUG ===');\n      console.log('Selected experience levels:', selectedExpLevels);\n      console.log('Jobs before experience filtering:', filtered.length);\n\n      // Log all unique experience levels in the current filtered jobs\n      const allExpLevels = [...new Set(filtered.map(job => job.experience_level).filter(Boolean))];\n      console.log('All unique experience levels in current jobs:', allExpLevels);\n\n      // Log detailed comparison for first few jobs\n      filtered.slice(0, 3).forEach(job => {\n        const jobExperienceLevel = job.experience_level || '';\n        const matches = selectedExpLevels.includes(jobExperienceLevel);\n        console.log(`Job \"${job.position}\": exp_level=\"${jobExperienceLevel}\", matches=${matches}`);\n        console.log(`  - Selected levels: [${selectedExpLevels.join(', ')}]`);\n        console.log(`  - Includes check: ${selectedExpLevels.includes(jobExperienceLevel)}`);\n      });\n      filtered = filtered.filter(job => {\n        const jobExperienceLevel = job.experience_level || '';\n        const matches = selectedExpLevels.includes(jobExperienceLevel);\n        return matches;\n      });\n      console.log('Jobs after experience filtering:', filtered.length);\n      console.log('=== END EXPERIENCE LEVEL FILTER DEBUG ===');\n    }\n    setFilteredJobs(filtered);\n  }, [selectedJobType, selectedLocation, jobsArray, salaryRange, salaryFilterApplied, workLocationTypes, selectedEmploymentTypes, selectedExperienceLevels, searchKeyword]);\n\n  // Job types that should match SearchArea.jsx options\n  const predefinedJobCategories = ['Full time', 'Part time', 'Contract', 'Internship', 'Remote', 'Freelance'];\n\n  // Get available job categories (blog categories for filtering)\n  const getAvailableJobCategories = () => {\n    // Return all predefined blog categories since we're using keyword-based filtering\n    return predefinedJobCategories;\n  };\n  const handleJobTypeChange = e => {\n    updateJobType(e.target.value);\n  };\n  const handleLocationChange = e => {\n    const newLocation = e.target.value;\n    setSelectedLocation(newLocation);\n\n    // Update URL parameters to sync with SearchArea\n    const newSearchParams = new URLSearchParams(searchParams);\n    if (newLocation && newLocation !== '') {\n      newSearchParams.set('location', newLocation);\n    } else {\n      newSearchParams.delete('location');\n    }\n\n    // Update the URL without navigation\n    setSearchParams(newSearchParams);\n  };\n  const handleWorkLocationTypeChange = type => {\n    setWorkLocationTypes(prev => ({\n      ...prev,\n      [type]: !prev[type]\n    }));\n  };\n\n  // Helper function to format salary as \"Rs.\" with comma separators, handling min/max\n  function formatSalary(min, max) {\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) && (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\n      return 'Negotiable';\n    }\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\n      return 'Negotiable';\n    }\n    if (min && max) {\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\n    }\n    if (min) {\n      return `Rs. ${Number(min).toLocaleString()}`;\n    }\n    if (max) {\n      return `Rs. ${Number(max).toLocaleString()}`;\n    }\n    return 'Negotiable';\n  }\n\n  // Helper function (move outside useEffect)\n  function formatPostedDate(dateString) {\n    if (!dateString) return 'Recently';\n    try {\n      const postedDate = new Date(dateString);\n      const now = new Date();\n      const postedDateNormalized = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate()).getTime();\n      const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();\n      const oneDayMs = 24 * 60 * 60 * 1000;\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\n      if (daysAgo === 0) return 'Today';\n      if (daysAgo === 1) return 'Yesterday';\n      if (daysAgo < 7) return `${daysAgo} days ago`;\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\n      return `${Math.floor(daysAgo / 30)} months ago`;\n    } catch {\n      return 'Recently';\n    }\n  }\n  const firstJob = filteredJobs.length > 0 ? filteredJobs[0] : null;\n  const remainingJobs = filteredJobs.length > 0 ? filteredJobs.slice(1) : [];\n  const handleSalaryInputChange = (e, index) => {\n    // Remove commas and handle empty values\n    const inputValue = e.target.value.replace(/,/g, '');\n    const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;\n    const newRange = [...salaryRange];\n    newRange[index] = value;\n    setSalaryRange(newRange);\n\n    // Update slider positions based on input values\n    const minValue = 0;\n    const maxValue = 1000000;\n    const range = maxValue - minValue;\n    const newSliderValues = [...sliderValues];\n    newSliderValues[index] = (value - minValue) / range * 100;\n    setSliderValues(newSliderValues);\n  };\n  const handleSliderChange = (e, index) => {\n    const value = parseInt(e.target.value);\n    const newSliderValues = [...sliderValues];\n    newSliderValues[index] = value;\n    setSliderValues(newSliderValues);\n\n    // Update salary input values based on slider positions\n    const minValue = 0;\n    const maxValue = 1000000;\n    const range = maxValue - minValue;\n    const newSalaryRange = [...salaryRange];\n    newSalaryRange[index] = Math.round(value / 100 * range) + minValue;\n    setSalaryRange(newSalaryRange);\n  };\n  const applySalaryFilter = () => {\n    // Ensure values are valid numbers or default to 0 and 1000000\n    const minValue = isNaN(salaryRange[0]) ? 0 : salaryRange[0];\n    const maxValue = isNaN(salaryRange[1]) ? 1000000 : salaryRange[1];\n    updateSalaryRange(minValue.toString(), maxValue.toString());\n  };\n  const resetFilters = () => {\n    setSalaryRange([0, 1000000]);\n    setSliderValues([0, 100]);\n    updateJobType('');\n    setSelectedEmploymentTypes({\n      'Full Time Jobs': false,\n      'Part Time Jobs': false,\n      'Remote Jobs': false,\n      'Freelance': false,\n      'Temporary': false\n    });\n    setSelectedExperienceLevels({\n      \"No Experience Required\": false,\n      \"Entry Level\": false,\n      \"Mid Level\": false,\n      \"Senior Level\": false,\n      \"Manager\": false,\n      \"Executive\": false\n    });\n    setWorkLocationTypes({\n      onsite: false,\n      remote: false,\n      hybrid: false\n    });\n\n    // Reset salary range in context\n    updateSalaryRange('0', '1000000');\n  };\n\n  // Toggle filter sidebar\n  const toggleFilterSidebar = () => {\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"job-board-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-board-top-spacing\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faBriefcase\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), \" Job Category\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"job-type-dropdown\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"select-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedJobType,\n              onChange: handleJobTypeChange,\n              className: \"job-type-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), getAvailableJobCategories().map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faChevronDown,\n              className: \"dropdown-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faBuilding\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this), \" Employment Type\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"emp-fulltime\",\n            checked: selectedEmploymentTypes['Full Time Jobs'],\n            onChange: () => setSelectedEmploymentTypes(prev => ({\n              ...prev,\n              'Full Time Jobs': !prev['Full Time Jobs']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"emp-fulltime\",\n            children: \"Full Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"emp-parttime\",\n            checked: selectedEmploymentTypes['Part Time Jobs'],\n            onChange: () => setSelectedEmploymentTypes(prev => ({\n              ...prev,\n              'Part Time Jobs': !prev['Part Time Jobs']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"emp-parttime\",\n            children: \"Part Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"emp-remote\",\n            checked: selectedEmploymentTypes['Remote Jobs'],\n            onChange: () => setSelectedEmploymentTypes(prev => ({\n              ...prev,\n              'Remote Jobs': !prev['Remote Jobs']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"emp-remote\",\n            children: \"Remote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"emp-freelance\",\n            checked: selectedEmploymentTypes['Freelance'],\n            onChange: () => setSelectedEmploymentTypes(prev => ({\n              ...prev,\n              'Freelance': !prev['Freelance']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"emp-freelance\",\n            children: \"Freelance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"emp-temporary\",\n            checked: selectedEmploymentTypes['Temporary'],\n            onChange: () => setSelectedEmploymentTypes(prev => ({\n              ...prev,\n              'Temporary': !prev['Temporary']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"emp-temporary\",\n            children: \"Temporary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), \"          \", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faMapMarkerAlt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), \" Location\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedLocation,\n            onChange: handleLocationChange,\n            className: \"location-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Locations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this), districts.map(district => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: district,\n              children: district\n            }, district, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-icon-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faChevronDown,\n              className: \"dropdown-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"work-location-types\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"work-onsite\",\n              checked: workLocationTypes.onsite,\n              onChange: () => handleWorkLocationTypeChange('onsite')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"work-onsite\",\n              children: \"On-site\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"work-remote\",\n              checked: workLocationTypes.remote,\n              onChange: () => handleWorkLocationTypeChange('remote')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"work-remote\",\n              children: \"Remote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"work-hybrid\",\n              checked: workLocationTypes.hybrid,\n              onChange: () => handleWorkLocationTypeChange('hybrid')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"work-hybrid\",\n              children: \"Hybrid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faChartLine\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this), \" Experience Level\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-no-experience\",\n            checked: selectedExperienceLevels['No Experience Required'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'No Experience Required': !prev['No Experience Required']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-no-experience\",\n            children: \"No Experience Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-entry\",\n            checked: selectedExperienceLevels['Entry Level'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'Entry Level': !prev['Entry Level']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-entry\",\n            children: \"Entry Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-mid\",\n            checked: selectedExperienceLevels['Mid Level'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'Mid Level': !prev['Mid Level']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-mid\",\n            children: \"Mid Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-senior\",\n            checked: selectedExperienceLevels['Senior Level'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'Senior Level': !prev['Senior Level']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-senior\",\n            children: \"Senior Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-manager\",\n            checked: selectedExperienceLevels['Manager'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'Manager': !prev['Manager']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-manager\",\n            children: \"Manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"exp-executive\",\n            checked: selectedExperienceLevels['Executive'],\n            onChange: () => setSelectedExperienceLevels(prev => ({\n              ...prev,\n              'Executive': !prev['Executive']\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"exp-executive\",\n            children: \"Executive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-section salary-range-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faDollarSign\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 15\n          }, this), \" Salary Range\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"salary-slider-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-slider-track\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"salary-slider-progress\",\n              style: {\n                left: `${sliderValues[0]}%`,\n                width: `${sliderValues[1] - sliderValues[0]}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"100\",\n            value: sliderValues[0],\n            onChange: e => handleSliderChange(e, 0),\n            className: \"salary-range-input min-value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"0\",\n            max: \"100\",\n            value: sliderValues[1],\n            onChange: e => handleSliderChange(e, 1),\n            className: \"salary-range-input max-value\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"salary-labels\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"From\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"To\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"salary-input-fields\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: (isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString(),\n            onChange: e => handleSalaryInputChange(e, 0),\n            className: \"salary-text-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-separator\",\n            children: \"-\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: (isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString(),\n            onChange: e => handleSalaryInputChange(e, 1),\n            className: \"salary-text-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"salary-filter-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"apply-filter-button\",\n            onClick: applySalaryFilter,\n            children: \"Apply Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reset-filter-button\",\n            onClick: resetFilters,\n            children: \"Reset filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"job-listings\",\n      children: [/*#__PURE__*/_jsxDEV(PageHelmet, {\n        title: \"Browse\",\n        description: \"Expert advice, recruitment trends, and professional development resources for job seekers.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-search-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"job-search-left\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"filter-toggle-btn-jobboard\",\n            onClick: toggleFilterSidebar,\n            \"aria-label\": \"Toggle filters\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faFilter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this), \"Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Showing \", filteredJobs.length, \" out of \", jobsArray.length, \" Jobs\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this), \"          \", /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-grid\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-overlay\",\n          children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading jobs...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 805,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 13\n        }, this) : filteredJobs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-jobs-message\",\n          children: \"No jobs found matching your filters.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [firstJob && /*#__PURE__*/_jsxDEV(JobCard, {\n            id: firstJob.id,\n            hot: firstJob.hot,\n            image: firstJob.image,\n            logo: firstJob.logo,\n            company: firstJob.company,\n            position: firstJob.position,\n            location: firstJob.location,\n            workTime: firstJob.workTime,\n            salary: firstJob.salary,\n            postedTime: firstJob.postedTime,\n            views: firstJob.views\n          }, firstJob.id || 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 17\n          }, this), remainingJobs.map((job, index) => /*#__PURE__*/_jsxDEV(JobCard, {\n            id: job.id,\n            hot: job.hot,\n            image: job.image,\n            logo: job.logo,\n            company: job.company,\n            position: job.position,\n            location: job.location,\n            workTime: job.workTime,\n            salary: job.salary,\n            postedTime: job.postedTime,\n            views: job.views\n          }, job.id || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 785,\n      columnNumber: 7\n    }, this), isFilterSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filter-sidebar-overlay\",\n      onClick: toggleFilterSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `filter-sidebar-mobile ${isFilterSidebarOpen ? 'open' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faFilter\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this), \" Filters\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-sidebar-btn\",\n          onClick: toggleFilterSidebar,\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faTimes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-sidebar-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBriefcase\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), \" Job Category\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"job-type-dropdown\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"select-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedJobType,\n                onChange: handleJobTypeChange,\n                className: \"job-type-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this), getAvailableJobCategories().map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category,\n                  children: category\n                }, category, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faChevronDown,\n                className: \"dropdown-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faMapMarkerAlt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 17\n            }, this), \" Location\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-loc-remote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-loc-remote\",\n              children: \"Remote\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-loc-onsite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-loc-onsite\",\n              children: \"On-site\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-loc-hybrid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-loc-hybrid\",\n              children: \"Hybrid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faChartLine\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 17\n            }, this), \" Experience Level\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-exp-entry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-exp-entry\",\n              children: \"Entry Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-exp-mid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-exp-mid\",\n              children: \"Mid Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-exp-senior\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-exp-senior\",\n              children: \"Senior Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-exp-manager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-exp-manager\",\n              children: \"Manager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-option\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              id: \"mobile-exp-executive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"mobile-exp-executive\",\n              children: \"Executive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-section salary-range-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faDollarSign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 17\n            }, this), \" Salary Range\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-slider-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"salary-slider-track\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"salary-slider-progress\",\n                style: {\n                  left: `${sliderValues[0]}%`,\n                  width: `${sliderValues[1] - sliderValues[0]}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"0\",\n              max: \"100\",\n              value: sliderValues[0],\n              onChange: e => handleSliderChange(e, 0),\n              className: \"salary-range-input min-value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"range\",\n              min: \"0\",\n              max: \"100\",\n              value: sliderValues[1],\n              onChange: e => handleSliderChange(e, 1),\n              className: \"salary-range-input max-value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-labels\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"From\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"To\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-input-fields\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: (isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString(),\n              onChange: e => handleSalaryInputChange(e, 0),\n              className: \"salary-text-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"salary-separator\",\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: (isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString(),\n              onChange: e => handleSalaryInputChange(e, 1),\n              className: \"salary-text-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"salary-filter-buttons\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"apply-filter-button\",\n              onClick: applySalaryFilter,\n              children: \"Apply Filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"reset-filter-button\",\n              onClick: resetFilters,\n              children: \"Reset filter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 987,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(JobBoard, \"zD7VoTebrBW0Imka2AKnXLW2ErM=\", false, function () {\n  return [useSearchParams, useNavigate, useLocation, useJobFilter];\n});\n_c = JobBoard;\nexport default JobBoard;\nvar _c;\n$RefreshReg$(_c, \"JobBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "useNavigate", "JobCard", "ApiService", "FontAwesomeIcon", "faMapMarkerAlt", "faBriefcase", "faBuilding", "faChartLine", "faDollarSign", "faBell", "faChevronDown", "faFilter", "faTimes", "FaSpinner", "useLocation", "useJobFilter", "PageHelmet", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "JobBoard", "_s", "searchParams", "setSearchParams", "navigate", "selectedLocation", "setSelectedLocation", "districts", "jobsArray", "setJobsArray", "filteredJobs", "setFilteredJobs", "searchKeyword", "setSearchKeyword", "workLocationTypes", "setWorkLocationTypes", "onsite", "remote", "hybrid", "jobType", "selectedJobType", "updateJobType", "minSalary", "max<PERSON><PERSON><PERSON>", "salaryFilterApplied", "updateSalaryRange", "selectedEmploymentTypes", "setSelectedEmploymentTypes", "selectedExperienceLevels", "setSelectedExperienceLevels", "loading", "setLoading", "error", "setError", "isFilterSidebarOpen", "setIsFilterSidebarOpen", "fetchJobs", "response", "jobs", "getAll", "_t", "Date", "getTime", "console", "log", "data", "slice", "for<PERSON>ach", "job", "index", "job_title", "experience_level", "job_type", "map", "id", "job_id", "hot", "image", "job_post_thumbnail", "job_post_image", "logo", "company_logo", "company_logo_url", "company", "company_name", "position", "location", "main_topics", "workTime", "salary", "formatSalary", "min_salary", "max_salary", "postedTime", "start_date", "formatPostedDate", "views", "Math", "floor", "random", "status", "description", "job_description", "created_at", "Number", "uniqueExpLevels", "Set", "filter", "Boolean", "filteredActiveJobs", "sort", "a", "b", "dateA", "posted_date", "dateB", "err", "search", "get", "minSal", "maxSal", "salaryRange", "setSalaryRange", "slider<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newSalaryRange", "parseInt", "minValue", "maxValue", "range", "newSliderValues", "filtered", "trim", "length", "jobTitle", "toLowerCase", "jobDescription", "companyName", "searchTerm", "titleMatch", "includes", "descMatch", "companyMatch", "matches", "jobWorkTime", "selectedType", "jobLocation", "searchText", "minFilter", "maxFilter", "jobMin", "jobMax", "selectedWorkTypes", "Object", "keys", "key", "jobWorkType", "some", "type", "selectedEmpTypes", "selectedExpLevels", "allExpLevels", "jobExperienceLevel", "join", "predefinedJobCategories", "getAvailableJobCategories", "handleJobTypeChange", "e", "target", "value", "handleLocationChange", "newLocation", "newSearchParams", "URLSearchParams", "set", "delete", "handleWorkLocationTypeChange", "prev", "min", "max", "undefined", "isNaN", "toLocaleString", "dateString", "postedDate", "now", "postedDateNormalized", "getFullYear", "getMonth", "getDate", "todayNormalized", "oneDayMs", "daysAgo", "round", "first<PERSON>ob", "remainingJobs", "handleSalaryInputChange", "inputValue", "replace", "newRange", "handleSliderChange", "applySalaryFilter", "toString", "resetFilters", "toggleFilterSidebar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onChange", "category", "checked", "htmlFor", "district", "style", "left", "width", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/JobBoard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useSearchParams, useNavigate } from 'react-router-dom';\r\nimport JobCard from './JobCard';\r\nimport '../css/JobBoard.css';\r\nimport { ApiService } from '../services/apiService';\r\n// Import Font Awesome\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { \r\n  faMapMarkerAlt, \r\n  faBriefcase, \r\n  faBuilding, \r\n  faChartLine, \r\n  faDollarSign,\r\n  faBell,\r\n  faChevronDown,\r\n  faFilter,\r\n  faTimes\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport { FaSpinner } from 'react-icons/fa';\r\nimport { useLocation } from '../contexts/LocationContext';\r\nimport { useJobFilter } from '../contexts/JobFilterContext';\r\nimport PageHelmet from './PageHelmet';\r\n\r\nconst JobBoard = () => {\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n  const navigate = useNavigate();\r\n  const { selectedLocation, setSelectedLocation, districts } = useLocation();\r\n  const [jobsArray, setJobsArray] = useState([]);\r\n  const [filteredJobs, setFilteredJobs] = useState([]);\r\n  const [searchKeyword, setSearchKeyword] = useState('');\r\n  const [workLocationTypes, setWorkLocationTypes] = useState({\r\n    onsite: false,\r\n    remote: false,\r\n    hybrid: false\r\n  });\r\n  \r\n  // Use shared job filter context\r\n  const { \r\n    jobType: selectedJobType, \r\n    updateJobType, \r\n    minSalary,\r\n    maxSalary,\r\n    salaryFilterApplied,\r\n    updateSalaryRange\r\n  } = useJobFilter();\r\n  \r\n  // Add state for Employment Type and Experience Level filters\r\n  const [selectedEmploymentTypes, setSelectedEmploymentTypes] = useState({\r\n    'Full Time Jobs': false,\r\n    'Part Time Jobs': false,\r\n    'Remote Jobs': false,\r\n    'Freelance': false,\r\n    'Temporary': false\r\n  });\r\n  \r\n  const [selectedExperienceLevels, setSelectedExperienceLevels] = useState({\r\n    \"No Experience Required\": false,\r\n    \"Entry Level\": false,\r\n    \"Mid Level\": false,\r\n    \"Senior Level\": false,\r\n    \"Manager\": false,\r\n    \"Executive\": false\r\n  });\r\n  \r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {      try {        setLoading(true);\r\n        // Add a timestamp to prevent caching, like HomePage (MCP style)\r\n        const response = await ApiService.jobs.getAll({ _t: new Date().getTime() });        \r\n        console.log('RAW API RESPONSE - First 3 jobs:');\r\n        response.data.slice(0, 3).forEach((job, index) => {\r\n          console.log(`Job ${index + 1}:`, {\r\n            job_title: job.job_title,\r\n            experience_level: job.experience_level,\r\n            job_type: job.job_type\r\n          });\r\n        });\r\n        \r\n        // Map backend jobs to the format expected by JobCard, and sort newest first (like HomePage)\r\n        const jobs = response.data\r\n          .map(job => ({\r\n            id: job.job_id,\r\n            hot: job.hot || false,\r\n            image: job.job_post_thumbnail || job.job_post_image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',\r\n            logo: job.company_logo || job.company_logo_url || 'https://via.placeholder.com/40',\r\n            company: job.company_name,\r\n            position: job.job_title,\r\n            location: job.main_topics,\r\n            workTime: job.job_type,\r\n            // Compose salary string from min/max\r\n            salary: formatSalary(job.min_salary, job.max_salary),\r\n            postedTime: job.start_date ? formatPostedDate(job.start_date) : '',\r\n            views: Math.floor(Math.random() * 1000),\r\n            status: job.status || 'Active',\r\n            description: job.job_description || 'No description provided',\r\n            created_at: job.created_at || job.start_date,\r\n            // Store raw salary values for filtering\r\n            minSalary: job.min_salary ? Number(job.min_salary) : null,\r\n            maxSalary: job.max_salary ? Number(job.max_salary) : null,            // Add experience level for filtering\r\n            experience_level: job.experience_level || ''\r\n          }))\r\n          // Debug: Log first few jobs to see what experience_level values we're getting\r\n        console.log('JobBoard - Sample job data with experience levels:', \r\n          jobs.slice(0, 3).map(job => ({\r\n            position: job.position,\r\n            experience_level: job.experience_level,\r\n            workTime: job.workTime\r\n          }))\r\n        );\r\n        \r\n        // Debug: Log unique experience levels found in data\r\n        const uniqueExpLevels = [...new Set(jobs.map(job => job.experience_level).filter(Boolean))];\r\n        console.log('JobBoard - Unique experience levels in data:', uniqueExpLevels);\r\n        \r\n        const filteredActiveJobs = jobs\r\n          // Only show jobs with status 'Active'\r\n          .filter(job => job.status === 'Active')\r\n          .sort((a, b) => {\r\n            // Sort by creation date descending (newest first), then by ID descending as fallback\r\n            const dateA = new Date(a.created_at || a.start_date || a.posted_date || new Date());\r\n            const dateB = new Date(b.created_at || b.start_date || b.posted_date || new Date());\r\n\r\n            if (dateB.getTime() !== dateA.getTime()) {\r\n              return dateB.getTime() - dateA.getTime();\r\n            }\r\n\r\n            // If dates are the same, sort by ID descending (higher ID = newer)\r\n            return b.id - a.id;\r\n          });\r\n        setJobsArray(filteredActiveJobs);\r\n        setFilteredJobs(filteredActiveJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        setError('Failed to load jobs from server');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchJobs();\r\n  }, []);\r\n\r\n  // Handle URL search parameters\r\n  useEffect(() => {\r\n    console.log('=== URL PARAMETERS DEBUG ===');\r\n    const search = searchParams.get('search');\r\n    const location = searchParams.get('location');\r\n    \r\n    console.log('URL Parameters:', { search, jobType: selectedJobType, location, minSal: minSalary, maxSal: maxSalary });\r\n    \r\n    if (search) {\r\n      console.log('Setting search keyword to:', search);\r\n      setSearchKeyword(search);\r\n    }\r\n    \r\n    if (location) {\r\n      console.log('Setting location to:', location);\r\n      setSelectedLocation(location);\r\n    }\r\n    \r\n    console.log('=== END URL PARAMETERS DEBUG ===');\r\n  }, [searchParams, setSelectedLocation]);\r\n\r\n  // For salary range\r\n  const [salaryRange, setSalaryRange] = useState([0, 1000000]);\r\n  const [sliderValues, setSliderValues] = useState([0, 100]); // Percentage values for slider\r\n  \r\n  // Sync salary range with context\r\n  useEffect(() => {\r\n    if (minSalary !== '0' || maxSalary !== '1000000') {\r\n      const newSalaryRange = [parseInt(minSalary), parseInt(maxSalary)];\r\n      setSalaryRange(newSalaryRange);\r\n      \r\n      // Update slider values to match the salary range\r\n      const minValue = 0;\r\n      const maxValue = 1000000;\r\n      const range = maxValue - minValue;\r\n      const newSliderValues = [\r\n        ((newSalaryRange[0] - minValue) / range) * 100,\r\n        ((newSalaryRange[1] - minValue) / range) * 100\r\n      ];\r\n      setSliderValues(newSliderValues);\r\n    }\r\n  }, [minSalary, maxSalary]);\r\n  \r\n  // Filter jobs based on selected job category, location, and salary range\r\n  useEffect(() => {\r\n    let filtered = jobsArray;\r\n\r\n    // Filter by search keyword\r\n    if (searchKeyword.trim()) {\r\n      console.log('=== SEARCH KEYWORD FILTER DEBUG ===');\r\n      console.log('Search keyword:', searchKeyword);\r\n      console.log('Jobs before search filtering:', filtered.length);\r\n      \r\n      filtered = filtered.filter(job => {\r\n        const jobTitle = (job.position || '').toLowerCase();\r\n        const jobDescription = (job.description || '').toLowerCase();\r\n        const companyName = (job.company || '').toLowerCase();\r\n        const searchTerm = searchKeyword.toLowerCase();\r\n        \r\n        const titleMatch = jobTitle.includes(searchTerm);\r\n        const descMatch = jobDescription.includes(searchTerm);\r\n        const companyMatch = companyName.includes(searchTerm);\r\n        const matches = titleMatch || descMatch || companyMatch;\r\n        \r\n        if (matches) {\r\n          console.log(`MATCH FOUND - Job: \"${job.position}\" at ${job.company}`);\r\n          console.log(`  Title match: ${titleMatch}, Desc match: ${descMatch}, Company match: ${companyMatch}`);\r\n        }\r\n        \r\n        return matches;\r\n      });\r\n      \r\n      console.log('Jobs after search filtering:', filtered.length);\r\n      console.log('=== END SEARCH KEYWORD FILTER DEBUG ===');\r\n    }\r\n\r\n    // Filter by job type (matching SearchArea dropdown options)\r\n    if (selectedJobType !== '' && selectedJobType !== 'all') {\r\n      console.log('=== JOB TYPE FILTER DEBUG ===');\r\n      console.log('Selected job type:', selectedJobType);\r\n      console.log('Jobs before job type filtering:', filtered.length);\r\n      \r\n      filtered = filtered.filter(job => {\r\n        const jobWorkTime = (job.workTime || '').toLowerCase();\r\n        const selectedType = selectedJobType.toLowerCase();\r\n        \r\n        console.log(`Job \"${job.position}\": workTime=\"${job.workTime}\", matches=${jobWorkTime === selectedType}`);\r\n        \r\n        // Direct match with job's workTime field\r\n        return jobWorkTime === selectedType;\r\n      });\r\n      \r\n      console.log('Jobs after job type filtering:', filtered.length);\r\n      console.log('=== END JOB TYPE FILTER DEBUG ===');\r\n    }\r\n\r\n    // Filter by location\r\n    if (selectedLocation !== '' && selectedLocation !== 'all') {\r\n      filtered = filtered.filter(job => {\r\n        // Check if job location/description contains the selected district\r\n        const jobLocation = job.location || '';\r\n        const jobDescription = job.description || '';\r\n        const searchText = (jobLocation + ' ' + jobDescription).toLowerCase();\r\n        return searchText.includes(selectedLocation.toLowerCase());\r\n      });\r\n    }    // Filter by salary range if applied\r\n    if (salaryFilterApplied) {\r\n      filtered = filtered.filter(job => {\r\n        const [minFilter, maxFilter] = salaryRange;\r\n        \r\n        // If minimum filter is 0, show all jobs including those without salary data\r\n        if (minFilter === 0 && (job.minSalary === null && job.maxSalary === null)) {\r\n          return true;\r\n        }\r\n        \r\n        // If job has no salary data and min filter > 0, exclude it when filtering\r\n        if (job.minSalary === null && job.maxSalary === null && minFilter > 0) {\r\n          return false;\r\n        }\r\n        \r\n        // Check if job salary range overlaps with filter range\r\n        const jobMin = job.minSalary || 0;\r\n        const jobMax = job.maxSalary || job.minSalary || 999999;\r\n        \r\n        // Job overlaps with filter if:\r\n        // - Job's max salary is >= filter's min salary\r\n        // - Job's min salary is <= filter's max salary\r\n        return jobMax >= minFilter && jobMin <= maxFilter;\r\n      });\r\n    }\r\n\r\n    // Filter by work location types (onsite, remote, hybrid)\r\n    const selectedWorkTypes = Object.keys(workLocationTypes).filter(key => workLocationTypes[key]);\r\n    if (selectedWorkTypes.length > 0) {\r\n      filtered = filtered.filter(job => {\r\n        const jobWorkType = (job.workTime || '').toLowerCase();\r\n        const jobDescription = (job.description || '').toLowerCase();\r\n        \r\n        return selectedWorkTypes.some(type => {\r\n          switch(type) {\r\n            case 'remote':\r\n              return jobWorkType.includes('remote') || jobDescription.includes('remote') || jobWorkType.includes('work from home');\r\n            case 'onsite':\r\n              return jobWorkType.includes('onsite') || jobWorkType.includes('on-site') || jobWorkType.includes('office') || \r\n                     (!jobWorkType.includes('remote') && !jobWorkType.includes('hybrid') && !jobDescription.includes('remote'));\r\n            case 'hybrid':\r\n              return jobWorkType.includes('hybrid') || jobDescription.includes('hybrid');\r\n            default:\r\n              return false;\r\n          }\r\n        });\r\n      });\r\n    }    // Filter by Employment Type\r\n    const selectedEmpTypes = Object.keys(selectedEmploymentTypes).filter(key => selectedEmploymentTypes[key]);\r\n    if (selectedEmpTypes.length > 0) {\r\n      filtered = filtered.filter(job => {\r\n        const jobWorkTime = job.workTime || '';\r\n        return selectedEmpTypes.includes(jobWorkTime);\r\n      });\r\n    }    // Filter by Experience Level  \r\n    const selectedExpLevels = Object.keys(selectedExperienceLevels).filter(key => selectedExperienceLevels[key]);\r\n    if (selectedExpLevels.length > 0) {\r\n      console.log('=== EXPERIENCE LEVEL FILTER DEBUG ===');\r\n      console.log('Selected experience levels:', selectedExpLevels);\r\n      console.log('Jobs before experience filtering:', filtered.length);\r\n      \r\n      // Log all unique experience levels in the current filtered jobs\r\n      const allExpLevels = [...new Set(filtered.map(job => job.experience_level).filter(Boolean))];\r\n      console.log('All unique experience levels in current jobs:', allExpLevels);\r\n      \r\n      // Log detailed comparison for first few jobs\r\n      filtered.slice(0, 3).forEach(job => {\r\n        const jobExperienceLevel = job.experience_level || '';\r\n        const matches = selectedExpLevels.includes(jobExperienceLevel);\r\n        console.log(`Job \"${job.position}\": exp_level=\"${jobExperienceLevel}\", matches=${matches}`);\r\n        console.log(`  - Selected levels: [${selectedExpLevels.join(', ')}]`);\r\n        console.log(`  - Includes check: ${selectedExpLevels.includes(jobExperienceLevel)}`);\r\n      });\r\n      \r\n      filtered = filtered.filter(job => {\r\n        const jobExperienceLevel = job.experience_level || '';\r\n        const matches = selectedExpLevels.includes(jobExperienceLevel);\r\n        return matches;\r\n      });\r\n      \r\n      console.log('Jobs after experience filtering:', filtered.length);\r\n      console.log('=== END EXPERIENCE LEVEL FILTER DEBUG ===');\r\n    }\r\n\r\n    setFilteredJobs(filtered);\r\n  }, [selectedJobType, selectedLocation, jobsArray, salaryRange, salaryFilterApplied, workLocationTypes, selectedEmploymentTypes, selectedExperienceLevels, searchKeyword]);\r\n\r\n  // Job types that should match SearchArea.jsx options\r\n  const predefinedJobCategories = [\r\n    'Full time',\r\n    'Part time', \r\n    'Contract',\r\n    'Internship',\r\n    'Remote',\r\n    'Freelance'\r\n  ];\r\n\r\n  // Get available job categories (blog categories for filtering)\r\n  const getAvailableJobCategories = () => {\r\n    // Return all predefined blog categories since we're using keyword-based filtering\r\n    return predefinedJobCategories;\r\n  };\r\n  \r\n  const handleJobTypeChange = (e) => {\r\n    updateJobType(e.target.value);\r\n  };\r\n  \r\n  const handleLocationChange = (e) => {\r\n    const newLocation = e.target.value;\r\n    setSelectedLocation(newLocation);\r\n    \r\n    // Update URL parameters to sync with SearchArea\r\n    const newSearchParams = new URLSearchParams(searchParams);\r\n    \r\n    if (newLocation && newLocation !== '') {\r\n      newSearchParams.set('location', newLocation);\r\n    } else {\r\n      newSearchParams.delete('location');\r\n    }\r\n    \r\n    // Update the URL without navigation\r\n    setSearchParams(newSearchParams);\r\n  };\r\n  \r\n  const handleWorkLocationTypeChange = (type) => {\r\n    setWorkLocationTypes(prev => ({\r\n      ...prev,\r\n      [type]: !prev[type]\r\n    }));\r\n  };\r\n\r\n  // Helper function to format salary as \"Rs.\" with comma separators, handling min/max\r\n  function formatSalary(min, max) {\r\n    if ((min === null || min === undefined || min === '' || isNaN(Number(min))) &&\r\n        (max === null || max === undefined || max === '' || isNaN(Number(max)))) {\r\n      return 'Negotiable';\r\n    }\r\n    if ((min === 0 || min === '0') && (max === 0 || max === '0')) {\r\n      return 'Negotiable';\r\n    }\r\n    if (min && max) {\r\n      return `Rs. ${Number(min).toLocaleString()} - Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    if (min) {\r\n      return `Rs. ${Number(min).toLocaleString()}`;\r\n    }\r\n    if (max) {\r\n      return `Rs. ${Number(max).toLocaleString()}`;\r\n    }\r\n    return 'Negotiable';\r\n  }\r\n\r\n  // Helper function (move outside useEffect)\r\n  function formatPostedDate(dateString) {\r\n    if (!dateString) return 'Recently';\r\n    try {\r\n      const postedDate = new Date(dateString);\r\n      const now = new Date();\r\n      const postedDateNormalized = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate()).getTime();\r\n      const todayNormalized = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();\r\n      const oneDayMs = 24 * 60 * 60 * 1000;\r\n      const daysAgo = Math.round((todayNormalized - postedDateNormalized) / oneDayMs);\r\n      if (daysAgo === 0) return 'Today';\r\n      if (daysAgo === 1) return 'Yesterday';\r\n      if (daysAgo < 7) return `${daysAgo} days ago`;\r\n      if (daysAgo < 30) return `${Math.floor(daysAgo / 7)} weeks ago`;\r\n      return `${Math.floor(daysAgo / 30)} months ago`;\r\n    } catch {\r\n      return 'Recently';\r\n    }\r\n  }\r\n\r\n  const firstJob = filteredJobs.length > 0 ? filteredJobs[0] : null;\r\n  const remainingJobs = filteredJobs.length > 0 ? filteredJobs.slice(1) : [];\r\n\r\n  const handleSalaryInputChange = (e, index) => {\r\n    // Remove commas and handle empty values\r\n    const inputValue = e.target.value.replace(/,/g, '');\r\n    const value = inputValue === '' ? 0 : parseInt(inputValue) || 0;\r\n    \r\n    const newRange = [...salaryRange];\r\n    newRange[index] = value;\r\n    setSalaryRange(newRange);\r\n    \r\n    // Update slider positions based on input values\r\n    const minValue = 0;\r\n    const maxValue = 1000000;\r\n    const range = maxValue - minValue;\r\n    \r\n    const newSliderValues = [...sliderValues];\r\n    newSliderValues[index] = ((value - minValue) / range) * 100;\r\n    setSliderValues(newSliderValues);\r\n  };\r\n\r\n  const handleSliderChange = (e, index) => {\r\n    const value = parseInt(e.target.value);\r\n    const newSliderValues = [...sliderValues];\r\n    newSliderValues[index] = value;\r\n    setSliderValues(newSliderValues);\r\n    \r\n    // Update salary input values based on slider positions\r\n    const minValue = 0;\r\n    const maxValue = 1000000;\r\n    const range = maxValue - minValue;\r\n    \r\n    const newSalaryRange = [...salaryRange];\r\n    newSalaryRange[index] = Math.round((value / 100) * range) + minValue;\r\n    setSalaryRange(newSalaryRange);\r\n  };\r\n\r\n  const applySalaryFilter = () => {\r\n    // Ensure values are valid numbers or default to 0 and 1000000\r\n    const minValue = isNaN(salaryRange[0]) ? 0 : salaryRange[0];\r\n    const maxValue = isNaN(salaryRange[1]) ? 1000000 : salaryRange[1];\r\n    \r\n    updateSalaryRange(minValue.toString(), maxValue.toString());\r\n  };\r\n\r\n  const resetFilters = () => {\r\n    setSalaryRange([0, 1000000]);\r\n    setSliderValues([0, 100]);\r\n    updateJobType('');\r\n    setSelectedEmploymentTypes({\r\n      'Full Time Jobs': false,\r\n      'Part Time Jobs': false,\r\n      'Remote Jobs': false,\r\n      'Freelance': false,\r\n      'Temporary': false\r\n    });\r\n    setSelectedExperienceLevels({\r\n      \"No Experience Required\": false,\r\n      \"Entry Level\": false,\r\n      \"Mid Level\": false,\r\n      \"Senior Level\": false,\r\n      \"Manager\": false,\r\n      \"Executive\": false\r\n    });\r\n    setWorkLocationTypes({\r\n      onsite: false,\r\n      remote: false,\r\n      hybrid: false\r\n    });\r\n    \r\n    // Reset salary range in context\r\n    updateSalaryRange('0', '1000000');\r\n  };\r\n\r\n  // Toggle filter sidebar\r\n  const toggleFilterSidebar = () => {\r\n    setIsFilterSidebarOpen(!isFilterSidebarOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"job-board-container\">\r\n      {/* Add padding at the top to prevent navbar overlap */}\r\n      <div className=\"job-board-top-spacing\"></div>\r\n      \r\n      <div className=\"filters-sidebar\">\r\n        <div className=\"filter-section\">\r\n          <h3><FontAwesomeIcon icon={faBriefcase} /> Job Category</h3>\r\n          <div className=\"job-type-dropdown\">\r\n            <div className=\"select-wrapper\">\r\n              <select \r\n                value={selectedJobType} \r\n                onChange={handleJobTypeChange}\r\n                className=\"job-type-select\"\r\n              >\r\n                <option value=\"\">All Categories</option>\r\n                {getAvailableJobCategories().map(category => (\r\n                  <option key={category} value={category}>{category}</option>\r\n                ))}\r\n              </select>\r\n              <FontAwesomeIcon icon={faChevronDown} className=\"dropdown-icon\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"filter-section\">\r\n          <h3><FontAwesomeIcon icon={faBuilding} /> Employment Type</h3>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"emp-fulltime\" \r\n              checked={selectedEmploymentTypes['Full Time Jobs']}\r\n              onChange={() => setSelectedEmploymentTypes(prev => ({ \r\n                ...prev, \r\n                'Full Time Jobs': !prev['Full Time Jobs'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"emp-fulltime\">Full Time</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"emp-parttime\" \r\n              checked={selectedEmploymentTypes['Part Time Jobs']}\r\n              onChange={() => setSelectedEmploymentTypes(prev => ({ \r\n                ...prev, \r\n                'Part Time Jobs': !prev['Part Time Jobs'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"emp-parttime\">Part Time</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"emp-remote\" \r\n              checked={selectedEmploymentTypes['Remote Jobs']}\r\n              onChange={() => setSelectedEmploymentTypes(prev => ({ \r\n                ...prev, \r\n                'Remote Jobs': !prev['Remote Jobs'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"emp-remote\">Remote</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"emp-freelance\" \r\n              checked={selectedEmploymentTypes['Freelance']}\r\n              onChange={() => setSelectedEmploymentTypes(prev => ({ \r\n                ...prev, \r\n                'Freelance': !prev['Freelance'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"emp-freelance\">Freelance</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"emp-temporary\" \r\n              checked={selectedEmploymentTypes['Temporary']}\r\n              onChange={() => setSelectedEmploymentTypes(prev => ({ \r\n                ...prev, \r\n                'Temporary': !prev['Temporary'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"emp-temporary\">Temporary</label>\r\n          </div>\r\n        </div>          <div className=\"filter-section\">\r\n          <h3><FontAwesomeIcon icon={faMapMarkerAlt} /> Location</h3>\r\n          <div className=\"dropdown-wrapper\">\r\n            <select \r\n              value={selectedLocation} \r\n              onChange={handleLocationChange}\r\n              className=\"location-dropdown\"\r\n            >\r\n              <option value=\"\">All Locations</option>\r\n              {districts.map(district => (\r\n                <option key={district} value={district}>{district}</option>\r\n              ))}\r\n            </select>\r\n            <div className=\"dropdown-icon-wrapper\">\r\n              <FontAwesomeIcon icon={faChevronDown} className=\"dropdown-icon\" />\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"work-location-types\">\r\n            <div className=\"filter-option\">\r\n              <input \r\n                type=\"checkbox\" \r\n                id=\"work-onsite\" \r\n                checked={workLocationTypes.onsite}\r\n                onChange={() => handleWorkLocationTypeChange('onsite')}\r\n              />\r\n              <label htmlFor=\"work-onsite\">On-site</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input \r\n                type=\"checkbox\" \r\n                id=\"work-remote\" \r\n                checked={workLocationTypes.remote}\r\n                onChange={() => handleWorkLocationTypeChange('remote')}\r\n              />\r\n              <label htmlFor=\"work-remote\">Remote</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input \r\n                type=\"checkbox\" \r\n                id=\"work-hybrid\" \r\n                checked={workLocationTypes.hybrid}\r\n                onChange={() => handleWorkLocationTypeChange('hybrid')}\r\n              />\r\n              <label htmlFor=\"work-hybrid\">Hybrid</label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"filter-section\">\r\n          <h3><FontAwesomeIcon icon={faChartLine} /> Experience Level</h3>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-no-experience\" \r\n              checked={selectedExperienceLevels['No Experience Required']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'No Experience Required': !prev['No Experience Required'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-no-experience\">No Experience Required</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-entry\" \r\n              checked={selectedExperienceLevels['Entry Level']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'Entry Level': !prev['Entry Level'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-entry\">Entry Level</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-mid\" \r\n              checked={selectedExperienceLevels['Mid Level']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'Mid Level': !prev['Mid Level'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-mid\">Mid Level</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-senior\" \r\n              checked={selectedExperienceLevels['Senior Level']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'Senior Level': !prev['Senior Level'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-senior\">Senior Level</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-manager\" \r\n              checked={selectedExperienceLevels['Manager']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'Manager': !prev['Manager'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-manager\">Manager</label>\r\n          </div>\r\n          <div className=\"filter-option\">\r\n            <input \r\n              type=\"checkbox\" \r\n              id=\"exp-executive\" \r\n              checked={selectedExperienceLevels['Executive']}\r\n              onChange={() => setSelectedExperienceLevels(prev => ({ \r\n                ...prev, \r\n                'Executive': !prev['Executive'] \r\n              }))}\r\n            />\r\n            <label htmlFor=\"exp-executive\">Executive</label>\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"filter-section salary-range-section\">\r\n          <h3><FontAwesomeIcon icon={faDollarSign} /> Salary Range</h3>\r\n          \r\n          <div className=\"salary-slider-container\">\r\n            <div className=\"salary-slider-track\">\r\n              <div \r\n                className=\"salary-slider-progress\" \r\n                style={{\r\n                  left: `${sliderValues[0]}%`,\r\n                  width: `${sliderValues[1] - sliderValues[0]}%`\r\n                }}\r\n              ></div>\r\n            </div>\r\n            \r\n            <input \r\n              type=\"range\" \r\n              min=\"0\" \r\n              max=\"100\" \r\n              value={sliderValues[0]} \r\n              onChange={(e) => handleSliderChange(e, 0)}\r\n              className=\"salary-range-input min-value\"\r\n            />\r\n            \r\n            <input \r\n              type=\"range\" \r\n              min=\"0\" \r\n              max=\"100\" \r\n              value={sliderValues[1]} \r\n              onChange={(e) => handleSliderChange(e, 1)}\r\n              className=\"salary-range-input max-value\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"salary-labels\">\r\n            <div>From</div>\r\n            <div>To</div>\r\n          </div>\r\n          \r\n          <div className=\"salary-input-fields\">\r\n            <input \r\n              type=\"text\" \r\n              value={(isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString()}\r\n              onChange={(e) => handleSalaryInputChange(e, 0)}\r\n              className=\"salary-text-input\"\r\n            />\r\n            <div className=\"salary-separator\">-</div>\r\n            <input \r\n              type=\"text\" \r\n              value={(isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString()}\r\n              onChange={(e) => handleSalaryInputChange(e, 1)}\r\n              className=\"salary-text-input\"\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"salary-filter-buttons\">\r\n            <button className=\"apply-filter-button\" onClick={applySalaryFilter}>\r\n              Apply Filter\r\n            </button>\r\n            <button className=\"reset-filter-button\" onClick={resetFilters}>\r\n              Reset filter\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* <div className=\"job-alert-section\">\r\n          <h3><FontAwesomeIcon icon={faBell} /> Job Reminder</h3>\r\n          <p>Set job reminder to get notifications about jobs</p>\r\n          <button className=\"set-alert-button\">Set Job Reminder</button>\r\n        </div> */}\r\n      </div>\r\n      \r\n      <div className=\"job-listings\">\r\n              <PageHelmet \r\n                title=\"Browse\" \r\n                description=\"Expert advice, recruitment trends, and professional development resources for job seekers.\"\r\n              />\r\n        <div className=\"job-search-header\">\r\n          <div className=\"job-search-left\">\r\n            <button \r\n              className=\"filter-toggle-btn-jobboard\" \r\n              onClick={toggleFilterSidebar}\r\n              aria-label=\"Toggle filters\"\r\n            >\r\n              <FontAwesomeIcon icon={faFilter} />\r\n              Filters\r\n            </button>\r\n            <div>Showing {filteredJobs.length} out of {jobsArray.length} Jobs</div>\r\n          </div>\r\n          {/* Removed sort-options dropdown */}\r\n        </div>          <div className=\"job-grid\">\r\n          {loading ? (\r\n            <div className=\"loading-overlay\">\r\n              <FaSpinner className=\"spinner\" />\r\n              <p>Loading jobs...</p>\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"error-message\">{error}</div>\r\n          ) : filteredJobs.length === 0 ? (\r\n            <div className=\"no-jobs-message\">No jobs found matching your filters.</div>\r\n          ) : (\r\n            <>\r\n              {/* First job card as individual component */}\r\n              {firstJob && (\r\n                <JobCard \r\n                  key={firstJob.id || 0}\r\n                  id={firstJob.id}\r\n                  hot={firstJob.hot}\r\n                  image={firstJob.image}\r\n                  logo={firstJob.logo}\r\n                  company={firstJob.company}\r\n                  position={firstJob.position}\r\n                  location={firstJob.location}\r\n                  workTime={firstJob.workTime}\r\n                  salary={firstJob.salary}\r\n                  postedTime={firstJob.postedTime}\r\n                  views={firstJob.views}\r\n                />\r\n              )}\r\n              \r\n              {/* Remaining job cards */}\r\n              {remainingJobs.map((job, index) => (\r\n                <JobCard \r\n                  key={job.id || index}\r\n                  id={job.id}\r\n                  hot={job.hot}\r\n                  image={job.image}\r\n                  logo={job.logo}\r\n                  company={job.company}\r\n                  position={job.position}\r\n                  location={job.location}\r\n                  workTime={job.workTime}\r\n                  salary={job.salary}\r\n                  postedTime={job.postedTime}\r\n                  views={job.views}\r\n                />\r\n              ))}\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filter Sidebar Overlay */}\r\n      {isFilterSidebarOpen && (\r\n        <div className=\"filter-sidebar-overlay\" onClick={toggleFilterSidebar}></div>\r\n      )}\r\n\r\n      {/* Sliding Filter Sidebar */}\r\n      <div className={`filter-sidebar-mobile ${isFilterSidebarOpen ? 'open' : ''}`}>\r\n        <div className=\"filter-sidebar-header\">\r\n          <h3><FontAwesomeIcon icon={faFilter} /> Filters</h3>\r\n          <button className=\"close-sidebar-btn\" onClick={toggleFilterSidebar}>\r\n            <FontAwesomeIcon icon={faTimes} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"filter-sidebar-content\">\r\n          <div className=\"filter-section\">\r\n            <h3><FontAwesomeIcon icon={faBriefcase} /> Job Category</h3>\r\n            <div className=\"job-type-dropdown\">\r\n              <div className=\"select-wrapper\">\r\n                <select \r\n                  value={selectedJobType} \r\n                  onChange={handleJobTypeChange}\r\n                  className=\"job-type-select\"\r\n                >\r\n                  <option value=\"\">All Categories</option>\r\n                  {getAvailableJobCategories().map(category => (\r\n                    <option key={category} value={category}>{category}</option>\r\n                  ))}\r\n                </select>\r\n                <FontAwesomeIcon icon={faChevronDown} className=\"dropdown-icon\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"filter-section\">\r\n            <h3><FontAwesomeIcon icon={faMapMarkerAlt} /> Location</h3>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-loc-remote\" />\r\n              <label htmlFor=\"mobile-loc-remote\">Remote</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-loc-onsite\" />\r\n              <label htmlFor=\"mobile-loc-onsite\">On-site</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-loc-hybrid\" />\r\n              <label htmlFor=\"mobile-loc-hybrid\">Hybrid</label>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"filter-section\">\r\n            <h3><FontAwesomeIcon icon={faChartLine} /> Experience Level</h3>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-exp-entry\" />\r\n              <label htmlFor=\"mobile-exp-entry\">Entry Level</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-exp-mid\" />\r\n              <label htmlFor=\"mobile-exp-mid\">Mid Level</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-exp-senior\" />\r\n              <label htmlFor=\"mobile-exp-senior\">Senior Level</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-exp-manager\" />\r\n              <label htmlFor=\"mobile-exp-manager\">Manager</label>\r\n            </div>\r\n            <div className=\"filter-option\">\r\n              <input type=\"checkbox\" id=\"mobile-exp-executive\" />\r\n              <label htmlFor=\"mobile-exp-executive\">Executive</label>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"filter-section salary-range-section\">\r\n            <h3><FontAwesomeIcon icon={faDollarSign} /> Salary Range</h3>\r\n            \r\n            <div className=\"salary-slider-container\">\r\n              <div className=\"salary-slider-track\">\r\n                <div \r\n                  className=\"salary-slider-progress\" \r\n                  style={{\r\n                    left: `${sliderValues[0]}%`,\r\n                    width: `${sliderValues[1] - sliderValues[0]}%`\r\n                  }}\r\n                ></div>\r\n              </div>\r\n              \r\n              <input \r\n                type=\"range\" \r\n                min=\"0\" \r\n                max=\"100\" \r\n                value={sliderValues[0]} \r\n                onChange={(e) => handleSliderChange(e, 0)}\r\n                className=\"salary-range-input min-value\"\r\n              />\r\n              \r\n              <input \r\n                type=\"range\" \r\n                min=\"0\" \r\n                max=\"100\" \r\n                value={sliderValues[1]} \r\n                onChange={(e) => handleSliderChange(e, 1)}\r\n                className=\"salary-range-input max-value\"\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"salary-labels\">\r\n              <div>From</div>\r\n              <div>To</div>\r\n            </div>\r\n            \r\n            <div className=\"salary-input-fields\">\r\n              <input \r\n                type=\"text\" \r\n                value={(isNaN(salaryRange[0]) ? 0 : salaryRange[0]).toLocaleString()}\r\n                onChange={(e) => handleSalaryInputChange(e, 0)}\r\n                className=\"salary-text-input\"\r\n              />\r\n              <div className=\"salary-separator\">-</div>\r\n              <input \r\n                type=\"text\" \r\n                value={(isNaN(salaryRange[1]) ? 1000000 : salaryRange[1]).toLocaleString()}\r\n                onChange={(e) => handleSalaryInputChange(e, 1)}\r\n                className=\"salary-text-input\"\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"salary-filter-buttons\">\r\n              <button className=\"apply-filter-button\" onClick={applySalaryFilter}>\r\n                Apply Filter\r\n              </button>\r\n              <button className=\"reset-filter-button\" onClick={resetFilters}>\r\n                Reset filter\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobBoard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAO,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,wBAAwB;AACnD;AACA,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNC,aAAa,EACbC,QAAQ,EACRC,OAAO,QACF,mCAAmC;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,eAAe,CAAC,CAAC;EACzD,MAAM0B,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE0B,gBAAgB;IAAEC,mBAAmB;IAAEC;EAAU,CAAC,GAAGd,WAAW,CAAC,CAAC;EAC1E,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvC,QAAQ,CAAC;IACzDwC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM;IACJC,OAAO,EAAEC,eAAe;IACxBC,aAAa;IACbC,SAAS;IACTC,SAAS;IACTC,mBAAmB;IACnBC;EACF,CAAC,GAAG/B,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM,CAACgC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGnD,QAAQ,CAAC;IACrE,gBAAgB,EAAE,KAAK;IACvB,gBAAgB,EAAE,KAAK;IACvB,aAAa,EAAE,KAAK;IACpB,WAAW,EAAE,KAAK;IAClB,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACoD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGrD,QAAQ,CAAC;IACvE,wBAAwB,EAAE,KAAK;IAC/B,aAAa,EAAE,KAAK;IACpB,WAAW,EAAE,KAAK;IAClB,cAAc,EAAE,KAAK;IACrB,SAAS,EAAE,KAAK;IAChB,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAErEC,SAAS,CAAC,MAAM;IACd,MAAM2D,SAAS,GAAG,MAAAA,CAAA,KAAY;MAAO,IAAI;QAASL,UAAU,CAAC,IAAI,CAAC;QAC9D;QACA,MAAMM,QAAQ,GAAG,MAAMxD,UAAU,CAACyD,IAAI,CAACC,MAAM,CAAC;UAAEC,EAAE,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;QAAE,CAAC,CAAC;QAC3EC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/CP,QAAQ,CAACQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;UAChDN,OAAO,CAACC,GAAG,CAAC,OAAOK,KAAK,GAAG,CAAC,GAAG,EAAE;YAC/BC,SAAS,EAAEF,GAAG,CAACE,SAAS;YACxBC,gBAAgB,EAAEH,GAAG,CAACG,gBAAgB;YACtCC,QAAQ,EAAEJ,GAAG,CAACI;UAChB,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA,MAAMd,IAAI,GAAGD,QAAQ,CAACQ,IAAI,CACvBQ,GAAG,CAACL,GAAG,KAAK;UACXM,EAAE,EAAEN,GAAG,CAACO,MAAM;UACdC,GAAG,EAAER,GAAG,CAACQ,GAAG,IAAI,KAAK;UACrBC,KAAK,EAAET,GAAG,CAACU,kBAAkB,IAAIV,GAAG,CAACW,cAAc,IAAI,8DAA8D;UACrHC,IAAI,EAAEZ,GAAG,CAACa,YAAY,IAAIb,GAAG,CAACc,gBAAgB,IAAI,gCAAgC;UAClFC,OAAO,EAAEf,GAAG,CAACgB,YAAY;UACzBC,QAAQ,EAAEjB,GAAG,CAACE,SAAS;UACvBgB,QAAQ,EAAElB,GAAG,CAACmB,WAAW;UACzBC,QAAQ,EAAEpB,GAAG,CAACI,QAAQ;UACtB;UACAiB,MAAM,EAAEC,YAAY,CAACtB,GAAG,CAACuB,UAAU,EAAEvB,GAAG,CAACwB,UAAU,CAAC;UACpDC,UAAU,EAAEzB,GAAG,CAAC0B,UAAU,GAAGC,gBAAgB,CAAC3B,GAAG,CAAC0B,UAAU,CAAC,GAAG,EAAE;UAClEE,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;UACvCC,MAAM,EAAEhC,GAAG,CAACgC,MAAM,IAAI,QAAQ;UAC9BC,WAAW,EAAEjC,GAAG,CAACkC,eAAe,IAAI,yBAAyB;UAC7DC,UAAU,EAAEnC,GAAG,CAACmC,UAAU,IAAInC,GAAG,CAAC0B,UAAU;UAC5C;UACApD,SAAS,EAAE0B,GAAG,CAACuB,UAAU,GAAGa,MAAM,CAACpC,GAAG,CAACuB,UAAU,CAAC,GAAG,IAAI;UACzDhD,SAAS,EAAEyB,GAAG,CAACwB,UAAU,GAAGY,MAAM,CAACpC,GAAG,CAACwB,UAAU,CAAC,GAAG,IAAI;UAAa;UACtErB,gBAAgB,EAAEH,GAAG,CAACG,gBAAgB,IAAI;QAC5C,CAAC,CAAC,CAAC;QACH;QACFR,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAC9DN,IAAI,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACO,GAAG,CAACL,GAAG,KAAK;UAC3BiB,QAAQ,EAAEjB,GAAG,CAACiB,QAAQ;UACtBd,gBAAgB,EAAEH,GAAG,CAACG,gBAAgB;UACtCiB,QAAQ,EAAEpB,GAAG,CAACoB;QAChB,CAAC,CAAC,CACJ,CAAC;;QAED;QACA,MAAMiB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChD,IAAI,CAACe,GAAG,CAACL,GAAG,IAAIA,GAAG,CAACG,gBAAgB,CAAC,CAACoC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;QAC3F7C,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEyC,eAAe,CAAC;QAE5E,MAAMI,kBAAkB,GAAGnD;QACzB;QAAA,CACCiD,MAAM,CAACvC,GAAG,IAAIA,GAAG,CAACgC,MAAM,KAAK,QAAQ,CAAC,CACtCU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACd;UACA,MAAMC,KAAK,GAAG,IAAIpD,IAAI,CAACkD,CAAC,CAACR,UAAU,IAAIQ,CAAC,CAACjB,UAAU,IAAIiB,CAAC,CAACG,WAAW,IAAI,IAAIrD,IAAI,CAAC,CAAC,CAAC;UACnF,MAAMsD,KAAK,GAAG,IAAItD,IAAI,CAACmD,CAAC,CAACT,UAAU,IAAIS,CAAC,CAAClB,UAAU,IAAIkB,CAAC,CAACE,WAAW,IAAI,IAAIrD,IAAI,CAAC,CAAC,CAAC;UAEnF,IAAIsD,KAAK,CAACrD,OAAO,CAAC,CAAC,KAAKmD,KAAK,CAACnD,OAAO,CAAC,CAAC,EAAE;YACvC,OAAOqD,KAAK,CAACrD,OAAO,CAAC,CAAC,GAAGmD,KAAK,CAACnD,OAAO,CAAC,CAAC;UAC1C;;UAEA;UACA,OAAOkD,CAAC,CAACtC,EAAE,GAAGqC,CAAC,CAACrC,EAAE;QACpB,CAAC,CAAC;QACJ7C,YAAY,CAACgF,kBAAkB,CAAC;QAChC9E,eAAe,CAAC8E,kBAAkB,CAAC;QACnCxD,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO+D,GAAG,EAAE;QACZ/D,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3D,SAAS,CAAC,MAAM;IACdkE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3C,MAAMqD,MAAM,GAAG/F,YAAY,CAACgG,GAAG,CAAC,QAAQ,CAAC;IACzC,MAAMhC,QAAQ,GAAGhE,YAAY,CAACgG,GAAG,CAAC,UAAU,CAAC;IAE7CvD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAAEqD,MAAM;MAAE9E,OAAO,EAAEC,eAAe;MAAE8C,QAAQ;MAAEiC,MAAM,EAAE7E,SAAS;MAAE8E,MAAM,EAAE7E;IAAU,CAAC,CAAC;IAEpH,IAAI0E,MAAM,EAAE;MACVtD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqD,MAAM,CAAC;MACjDpF,gBAAgB,CAACoF,MAAM,CAAC;IAC1B;IAEA,IAAI/B,QAAQ,EAAE;MACZvB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsB,QAAQ,CAAC;MAC7C5D,mBAAmB,CAAC4D,QAAQ,CAAC;IAC/B;IAEAvB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD,CAAC,EAAE,CAAC1C,YAAY,EAAEI,mBAAmB,CAAC,CAAC;;EAEvC;EACA,MAAM,CAAC+F,WAAW,EAAEC,cAAc,CAAC,GAAG9H,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAC5D,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAI6C,SAAS,KAAK,GAAG,IAAIC,SAAS,KAAK,SAAS,EAAE;MAChD,MAAMkF,cAAc,GAAG,CAACC,QAAQ,CAACpF,SAAS,CAAC,EAAEoF,QAAQ,CAACnF,SAAS,CAAC,CAAC;MACjE+E,cAAc,CAACG,cAAc,CAAC;;MAE9B;MACA,MAAME,QAAQ,GAAG,CAAC;MAClB,MAAMC,QAAQ,GAAG,OAAO;MACxB,MAAMC,KAAK,GAAGD,QAAQ,GAAGD,QAAQ;MACjC,MAAMG,eAAe,GAAG,CACrB,CAACL,cAAc,CAAC,CAAC,CAAC,GAAGE,QAAQ,IAAIE,KAAK,GAAI,GAAG,EAC7C,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGE,QAAQ,IAAIE,KAAK,GAAI,GAAG,CAC/C;MACDL,eAAe,CAACM,eAAe,CAAC;IAClC;EACF,CAAC,EAAE,CAACxF,SAAS,EAAEC,SAAS,CAAC,CAAC;;EAE1B;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIsI,QAAQ,GAAGvG,SAAS;;IAExB;IACA,IAAII,aAAa,CAACoG,IAAI,CAAC,CAAC,EAAE;MACxBrE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEhC,aAAa,CAAC;MAC7C+B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmE,QAAQ,CAACE,MAAM,CAAC;MAE7DF,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAMkE,QAAQ,GAAG,CAAClE,GAAG,CAACiB,QAAQ,IAAI,EAAE,EAAEkD,WAAW,CAAC,CAAC;QACnD,MAAMC,cAAc,GAAG,CAACpE,GAAG,CAACiC,WAAW,IAAI,EAAE,EAAEkC,WAAW,CAAC,CAAC;QAC5D,MAAME,WAAW,GAAG,CAACrE,GAAG,CAACe,OAAO,IAAI,EAAE,EAAEoD,WAAW,CAAC,CAAC;QACrD,MAAMG,UAAU,GAAG1G,aAAa,CAACuG,WAAW,CAAC,CAAC;QAE9C,MAAMI,UAAU,GAAGL,QAAQ,CAACM,QAAQ,CAACF,UAAU,CAAC;QAChD,MAAMG,SAAS,GAAGL,cAAc,CAACI,QAAQ,CAACF,UAAU,CAAC;QACrD,MAAMI,YAAY,GAAGL,WAAW,CAACG,QAAQ,CAACF,UAAU,CAAC;QACrD,MAAMK,OAAO,GAAGJ,UAAU,IAAIE,SAAS,IAAIC,YAAY;QAEvD,IAAIC,OAAO,EAAE;UACXhF,OAAO,CAACC,GAAG,CAAC,uBAAuBI,GAAG,CAACiB,QAAQ,QAAQjB,GAAG,CAACe,OAAO,EAAE,CAAC;UACrEpB,OAAO,CAACC,GAAG,CAAC,kBAAkB2E,UAAU,iBAAiBE,SAAS,oBAAoBC,YAAY,EAAE,CAAC;QACvG;QAEA,OAAOC,OAAO;MAChB,CAAC,CAAC;MAEFhF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmE,QAAQ,CAACE,MAAM,CAAC;MAC5DtE,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;;IAEA;IACA,IAAIxB,eAAe,KAAK,EAAE,IAAIA,eAAe,KAAK,KAAK,EAAE;MACvDuB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAExB,eAAe,CAAC;MAClDuB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEmE,QAAQ,CAACE,MAAM,CAAC;MAE/DF,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAM4E,WAAW,GAAG,CAAC5E,GAAG,CAACoB,QAAQ,IAAI,EAAE,EAAE+C,WAAW,CAAC,CAAC;QACtD,MAAMU,YAAY,GAAGzG,eAAe,CAAC+F,WAAW,CAAC,CAAC;QAElDxE,OAAO,CAACC,GAAG,CAAC,QAAQI,GAAG,CAACiB,QAAQ,gBAAgBjB,GAAG,CAACoB,QAAQ,cAAcwD,WAAW,KAAKC,YAAY,EAAE,CAAC;;QAEzG;QACA,OAAOD,WAAW,KAAKC,YAAY;MACrC,CAAC,CAAC;MAEFlF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmE,QAAQ,CAACE,MAAM,CAAC;MAC9DtE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAClD;;IAEA;IACA,IAAIvC,gBAAgB,KAAK,EAAE,IAAIA,gBAAgB,KAAK,KAAK,EAAE;MACzD0G,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC;QACA,MAAM8E,WAAW,GAAG9E,GAAG,CAACkB,QAAQ,IAAI,EAAE;QACtC,MAAMkD,cAAc,GAAGpE,GAAG,CAACiC,WAAW,IAAI,EAAE;QAC5C,MAAM8C,UAAU,GAAG,CAACD,WAAW,GAAG,GAAG,GAAGV,cAAc,EAAED,WAAW,CAAC,CAAC;QACrE,OAAOY,UAAU,CAACP,QAAQ,CAACnH,gBAAgB,CAAC8G,WAAW,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAI;IACL,IAAI3F,mBAAmB,EAAE;MACvBuF,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAM,CAACgF,SAAS,EAAEC,SAAS,CAAC,GAAG5B,WAAW;;QAE1C;QACA,IAAI2B,SAAS,KAAK,CAAC,IAAKhF,GAAG,CAAC1B,SAAS,KAAK,IAAI,IAAI0B,GAAG,CAACzB,SAAS,KAAK,IAAK,EAAE;UACzE,OAAO,IAAI;QACb;;QAEA;QACA,IAAIyB,GAAG,CAAC1B,SAAS,KAAK,IAAI,IAAI0B,GAAG,CAACzB,SAAS,KAAK,IAAI,IAAIyG,SAAS,GAAG,CAAC,EAAE;UACrE,OAAO,KAAK;QACd;;QAEA;QACA,MAAME,MAAM,GAAGlF,GAAG,CAAC1B,SAAS,IAAI,CAAC;QACjC,MAAM6G,MAAM,GAAGnF,GAAG,CAACzB,SAAS,IAAIyB,GAAG,CAAC1B,SAAS,IAAI,MAAM;;QAEvD;QACA;QACA;QACA,OAAO6G,MAAM,IAAIH,SAAS,IAAIE,MAAM,IAAID,SAAS;MACnD,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMG,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACxH,iBAAiB,CAAC,CAACyE,MAAM,CAACgD,GAAG,IAAIzH,iBAAiB,CAACyH,GAAG,CAAC,CAAC;IAC9F,IAAIH,iBAAiB,CAACnB,MAAM,GAAG,CAAC,EAAE;MAChCF,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAMwF,WAAW,GAAG,CAACxF,GAAG,CAACoB,QAAQ,IAAI,EAAE,EAAE+C,WAAW,CAAC,CAAC;QACtD,MAAMC,cAAc,GAAG,CAACpE,GAAG,CAACiC,WAAW,IAAI,EAAE,EAAEkC,WAAW,CAAC,CAAC;QAE5D,OAAOiB,iBAAiB,CAACK,IAAI,CAACC,IAAI,IAAI;UACpC,QAAOA,IAAI;YACT,KAAK,QAAQ;cACX,OAAOF,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAAIJ,cAAc,CAACI,QAAQ,CAAC,QAAQ,CAAC,IAAIgB,WAAW,CAAChB,QAAQ,CAAC,gBAAgB,CAAC;YACtH,KAAK,QAAQ;cACX,OAAOgB,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAAIgB,WAAW,CAAChB,QAAQ,CAAC,SAAS,CAAC,IAAIgB,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAClG,CAACgB,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACgB,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACJ,cAAc,CAACI,QAAQ,CAAC,QAAQ,CAAE;YACnH,KAAK,QAAQ;cACX,OAAOgB,WAAW,CAAChB,QAAQ,CAAC,QAAQ,CAAC,IAAIJ,cAAc,CAACI,QAAQ,CAAC,QAAQ,CAAC;YAC5E;cACE,OAAO,KAAK;UAChB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAI;IACL,MAAMmB,gBAAgB,GAAGN,MAAM,CAACC,IAAI,CAAC5G,uBAAuB,CAAC,CAAC6D,MAAM,CAACgD,GAAG,IAAI7G,uBAAuB,CAAC6G,GAAG,CAAC,CAAC;IACzG,IAAII,gBAAgB,CAAC1B,MAAM,GAAG,CAAC,EAAE;MAC/BF,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAM4E,WAAW,GAAG5E,GAAG,CAACoB,QAAQ,IAAI,EAAE;QACtC,OAAOuE,gBAAgB,CAACnB,QAAQ,CAACI,WAAW,CAAC;MAC/C,CAAC,CAAC;IACJ,CAAC,CAAI;IACL,MAAMgB,iBAAiB,GAAGP,MAAM,CAACC,IAAI,CAAC1G,wBAAwB,CAAC,CAAC2D,MAAM,CAACgD,GAAG,IAAI3G,wBAAwB,CAAC2G,GAAG,CAAC,CAAC;IAC5G,IAAIK,iBAAiB,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAChCtE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpDD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgG,iBAAiB,CAAC;MAC7DjG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmE,QAAQ,CAACE,MAAM,CAAC;;MAEjE;MACA,MAAM4B,YAAY,GAAG,CAAC,GAAG,IAAIvD,GAAG,CAACyB,QAAQ,CAAC1D,GAAG,CAACL,GAAG,IAAIA,GAAG,CAACG,gBAAgB,CAAC,CAACoC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;MAC5F7C,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEiG,YAAY,CAAC;;MAE1E;MACA9B,QAAQ,CAACjE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAACC,GAAG,IAAI;QAClC,MAAM8F,kBAAkB,GAAG9F,GAAG,CAACG,gBAAgB,IAAI,EAAE;QACrD,MAAMwE,OAAO,GAAGiB,iBAAiB,CAACpB,QAAQ,CAACsB,kBAAkB,CAAC;QAC9DnG,OAAO,CAACC,GAAG,CAAC,QAAQI,GAAG,CAACiB,QAAQ,iBAAiB6E,kBAAkB,cAAcnB,OAAO,EAAE,CAAC;QAC3FhF,OAAO,CAACC,GAAG,CAAC,yBAAyBgG,iBAAiB,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QACrEpG,OAAO,CAACC,GAAG,CAAC,uBAAuBgG,iBAAiB,CAACpB,QAAQ,CAACsB,kBAAkB,CAAC,EAAE,CAAC;MACtF,CAAC,CAAC;MAEF/B,QAAQ,GAAGA,QAAQ,CAACxB,MAAM,CAACvC,GAAG,IAAI;QAChC,MAAM8F,kBAAkB,GAAG9F,GAAG,CAACG,gBAAgB,IAAI,EAAE;QACrD,MAAMwE,OAAO,GAAGiB,iBAAiB,CAACpB,QAAQ,CAACsB,kBAAkB,CAAC;QAC9D,OAAOnB,OAAO;MAChB,CAAC,CAAC;MAEFhF,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEmE,QAAQ,CAACE,MAAM,CAAC;MAChEtE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D;IAEAjC,eAAe,CAACoG,QAAQ,CAAC;EAC3B,CAAC,EAAE,CAAC3F,eAAe,EAAEf,gBAAgB,EAAEG,SAAS,EAAE6F,WAAW,EAAE7E,mBAAmB,EAAEV,iBAAiB,EAAEY,uBAAuB,EAAEE,wBAAwB,EAAEhB,aAAa,CAAC,CAAC;;EAEzK;EACA,MAAMoI,uBAAuB,GAAG,CAC9B,WAAW,EACX,WAAW,EACX,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,WAAW,CACZ;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC;IACA,OAAOD,uBAAuB;EAChC,CAAC;EAED,MAAME,mBAAmB,GAAIC,CAAC,IAAK;IACjC9H,aAAa,CAAC8H,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,MAAMI,WAAW,GAAGJ,CAAC,CAACC,MAAM,CAACC,KAAK;IAClC/I,mBAAmB,CAACiJ,WAAW,CAAC;;IAEhC;IACA,MAAMC,eAAe,GAAG,IAAIC,eAAe,CAACvJ,YAAY,CAAC;IAEzD,IAAIqJ,WAAW,IAAIA,WAAW,KAAK,EAAE,EAAE;MACrCC,eAAe,CAACE,GAAG,CAAC,UAAU,EAAEH,WAAW,CAAC;IAC9C,CAAC,MAAM;MACLC,eAAe,CAACG,MAAM,CAAC,UAAU,CAAC;IACpC;;IAEA;IACAxJ,eAAe,CAACqJ,eAAe,CAAC;EAClC,CAAC;EAED,MAAMI,4BAA4B,GAAIlB,IAAI,IAAK;IAC7C3H,oBAAoB,CAAC8I,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACnB,IAAI,GAAG,CAACmB,IAAI,CAACnB,IAAI;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,SAASpE,YAAYA,CAACwF,GAAG,EAAEC,GAAG,EAAE;IAC9B,IAAI,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,EAAE,IAAIG,KAAK,CAAC7E,MAAM,CAAC0E,GAAG,CAAC,CAAC,MACrEC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAAIE,KAAK,CAAC7E,MAAM,CAAC2E,GAAG,CAAC,CAAC,CAAC,EAAE;MAC3E,OAAO,YAAY;IACrB;IACA,IAAI,CAACD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,MAAMC,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,GAAG,CAAC,EAAE;MAC5D,OAAO,YAAY;IACrB;IACA,IAAID,GAAG,IAAIC,GAAG,EAAE;MACd,OAAO,OAAO3E,MAAM,CAAC0E,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,UAAU9E,MAAM,CAAC2E,GAAG,CAAC,CAACG,cAAc,CAAC,CAAC,EAAE;IACpF;IACA,IAAIJ,GAAG,EAAE;MACP,OAAO,OAAO1E,MAAM,CAAC0E,GAAG,CAAC,CAACI,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,IAAIH,GAAG,EAAE;MACP,OAAO,OAAO3E,MAAM,CAAC2E,GAAG,CAAC,CAACG,cAAc,CAAC,CAAC,EAAE;IAC9C;IACA,OAAO,YAAY;EACrB;;EAEA;EACA,SAASvF,gBAAgBA,CAACwF,UAAU,EAAE;IACpC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAClC,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI3H,IAAI,CAAC0H,UAAU,CAAC;MACvC,MAAME,GAAG,GAAG,IAAI5H,IAAI,CAAC,CAAC;MACtB,MAAM6H,oBAAoB,GAAG,IAAI7H,IAAI,CAAC2H,UAAU,CAACG,WAAW,CAAC,CAAC,EAAEH,UAAU,CAACI,QAAQ,CAAC,CAAC,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC/H,OAAO,CAAC,CAAC;MACtH,MAAMgI,eAAe,GAAG,IAAIjI,IAAI,CAAC4H,GAAG,CAACE,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACG,QAAQ,CAAC,CAAC,EAAEH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC/H,OAAO,CAAC,CAAC;MAC5F,MAAMiI,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;MACpC,MAAMC,OAAO,GAAG/F,IAAI,CAACgG,KAAK,CAAC,CAACH,eAAe,GAAGJ,oBAAoB,IAAIK,QAAQ,CAAC;MAC/E,IAAIC,OAAO,KAAK,CAAC,EAAE,OAAO,OAAO;MACjC,IAAIA,OAAO,KAAK,CAAC,EAAE,OAAO,WAAW;MACrC,IAAIA,OAAO,GAAG,CAAC,EAAE,OAAO,GAAGA,OAAO,WAAW;MAC7C,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAG/F,IAAI,CAACC,KAAK,CAAC8F,OAAO,GAAG,CAAC,CAAC,YAAY;MAC/D,OAAO,GAAG/F,IAAI,CAACC,KAAK,CAAC8F,OAAO,GAAG,EAAE,CAAC,aAAa;IACjD,CAAC,CAAC,MAAM;MACN,OAAO,UAAU;IACnB;EACF;EAEA,MAAME,QAAQ,GAAGpK,YAAY,CAACuG,MAAM,GAAG,CAAC,GAAGvG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;EACjE,MAAMqK,aAAa,GAAGrK,YAAY,CAACuG,MAAM,GAAG,CAAC,GAAGvG,YAAY,CAACoC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAE1E,MAAMkI,uBAAuB,GAAGA,CAAC7B,CAAC,EAAElG,KAAK,KAAK;IAC5C;IACA,MAAMgI,UAAU,GAAG9B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC6B,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IACnD,MAAM7B,KAAK,GAAG4B,UAAU,KAAK,EAAE,GAAG,CAAC,GAAGvE,QAAQ,CAACuE,UAAU,CAAC,IAAI,CAAC;IAE/D,MAAME,QAAQ,GAAG,CAAC,GAAG9E,WAAW,CAAC;IACjC8E,QAAQ,CAAClI,KAAK,CAAC,GAAGoG,KAAK;IACvB/C,cAAc,CAAC6E,QAAQ,CAAC;;IAExB;IACA,MAAMxE,QAAQ,GAAG,CAAC;IAClB,MAAMC,QAAQ,GAAG,OAAO;IACxB,MAAMC,KAAK,GAAGD,QAAQ,GAAGD,QAAQ;IAEjC,MAAMG,eAAe,GAAG,CAAC,GAAGP,YAAY,CAAC;IACzCO,eAAe,CAAC7D,KAAK,CAAC,GAAI,CAACoG,KAAK,GAAG1C,QAAQ,IAAIE,KAAK,GAAI,GAAG;IAC3DL,eAAe,CAACM,eAAe,CAAC;EAClC,CAAC;EAED,MAAMsE,kBAAkB,GAAGA,CAACjC,CAAC,EAAElG,KAAK,KAAK;IACvC,MAAMoG,KAAK,GAAG3C,QAAQ,CAACyC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;IACtC,MAAMvC,eAAe,GAAG,CAAC,GAAGP,YAAY,CAAC;IACzCO,eAAe,CAAC7D,KAAK,CAAC,GAAGoG,KAAK;IAC9B7C,eAAe,CAACM,eAAe,CAAC;;IAEhC;IACA,MAAMH,QAAQ,GAAG,CAAC;IAClB,MAAMC,QAAQ,GAAG,OAAO;IACxB,MAAMC,KAAK,GAAGD,QAAQ,GAAGD,QAAQ;IAEjC,MAAMF,cAAc,GAAG,CAAC,GAAGJ,WAAW,CAAC;IACvCI,cAAc,CAACxD,KAAK,CAAC,GAAG4B,IAAI,CAACgG,KAAK,CAAExB,KAAK,GAAG,GAAG,GAAIxC,KAAK,CAAC,GAAGF,QAAQ;IACpEL,cAAc,CAACG,cAAc,CAAC;EAChC,CAAC;EAED,MAAM4E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAM1E,QAAQ,GAAGsD,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;IAC3D,MAAMO,QAAQ,GAAGqD,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAGA,WAAW,CAAC,CAAC,CAAC;IAEjE5E,iBAAiB,CAACkF,QAAQ,CAAC2E,QAAQ,CAAC,CAAC,EAAE1E,QAAQ,CAAC0E,QAAQ,CAAC,CAAC,CAAC;EAC7D,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBjF,cAAc,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC5BE,eAAe,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACzBnF,aAAa,CAAC,EAAE,CAAC;IACjBM,0BAA0B,CAAC;MACzB,gBAAgB,EAAE,KAAK;MACvB,gBAAgB,EAAE,KAAK;MACvB,aAAa,EAAE,KAAK;MACpB,WAAW,EAAE,KAAK;MAClB,WAAW,EAAE;IACf,CAAC,CAAC;IACFE,2BAA2B,CAAC;MAC1B,wBAAwB,EAAE,KAAK;MAC/B,aAAa,EAAE,KAAK;MACpB,WAAW,EAAE,KAAK;MAClB,cAAc,EAAE,KAAK;MACrB,SAAS,EAAE,KAAK;MAChB,WAAW,EAAE;IACf,CAAC,CAAC;IACFd,oBAAoB,CAAC;MACnBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACAO,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC;EACnC,CAAC;;EAED;EACA,MAAM+J,mBAAmB,GAAGA,CAAA,KAAM;IAChCrJ,sBAAsB,CAAC,CAACD,mBAAmB,CAAC;EAC9C,CAAC;EAED,oBACErC,OAAA;IAAK4L,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAElC7L,OAAA;MAAK4L,SAAS,EAAC;IAAuB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE7CjM,OAAA;MAAK4L,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B7L,OAAA;QAAK4L,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAE/M;UAAY;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAAa;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5DjM,OAAA;UAAK4L,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC7L,OAAA;YAAK4L,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B7L,OAAA;cACEwJ,KAAK,EAAEjI,eAAgB;cACvB4K,QAAQ,EAAE9C,mBAAoB;cAC9BuC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAE3B7L,OAAA;gBAAQwJ,KAAK,EAAC,EAAE;gBAAAqC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC7C,yBAAyB,CAAC,CAAC,CAAC5F,GAAG,CAAC4I,QAAQ,iBACvCpM,OAAA;gBAAuBwJ,KAAK,EAAE4C,QAAS;gBAAAP,QAAA,EAAEO;cAAQ,GAApCA,QAAQ;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTjM,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAE1M,aAAc;cAACoM,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjM,OAAA;QAAK4L,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAE9M;UAAW;YAAA0M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAAgB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,cAAc;YACjB4I,OAAO,EAAExK,uBAAuB,CAAC,gBAAgB,CAAE;YACnDsK,QAAQ,EAAEA,CAAA,KAAMrK,0BAA0B,CAACkI,IAAI,KAAK;cAClD,GAAGA,IAAI;cACP,gBAAgB,EAAE,CAACA,IAAI,CAAC,gBAAgB;YAC1C,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,cAAc;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,cAAc;YACjB4I,OAAO,EAAExK,uBAAuB,CAAC,gBAAgB,CAAE;YACnDsK,QAAQ,EAAEA,CAAA,KAAMrK,0BAA0B,CAACkI,IAAI,KAAK;cAClD,GAAGA,IAAI;cACP,gBAAgB,EAAE,CAACA,IAAI,CAAC,gBAAgB;YAC1C,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,cAAc;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,YAAY;YACf4I,OAAO,EAAExK,uBAAuB,CAAC,aAAa,CAAE;YAChDsK,QAAQ,EAAEA,CAAA,KAAMrK,0BAA0B,CAACkI,IAAI,KAAK;cAClD,GAAGA,IAAI;cACP,aAAa,EAAE,CAACA,IAAI,CAAC,aAAa;YACpC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,YAAY;YAAAT,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,eAAe;YAClB4I,OAAO,EAAExK,uBAAuB,CAAC,WAAW,CAAE;YAC9CsK,QAAQ,EAAEA,CAAA,KAAMrK,0BAA0B,CAACkI,IAAI,KAAK;cAClD,GAAGA,IAAI;cACP,WAAW,EAAE,CAACA,IAAI,CAAC,WAAW;YAChC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,eAAe;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,eAAe;YAClB4I,OAAO,EAAExK,uBAAuB,CAAC,WAAW,CAAE;YAC9CsK,QAAQ,EAAEA,CAAA,KAAMrK,0BAA0B,CAACkI,IAAI,KAAK;cAClD,GAAGA,IAAI;cACP,WAAW,EAAE,CAACA,IAAI,CAAC,WAAW;YAChC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,eAAe;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,cAAU,eAAAjM,OAAA;QAAK4L,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7C7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAEhN;UAAe;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAAS;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3DjM,OAAA;UAAK4L,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B7L,OAAA;YACEwJ,KAAK,EAAEhJ,gBAAiB;YACxB2L,QAAQ,EAAE1C,oBAAqB;YAC/BmC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B7L,OAAA;cAAQwJ,KAAK,EAAC,EAAE;cAAAqC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACtCvL,SAAS,CAAC8C,GAAG,CAAC+I,QAAQ,iBACrBvM,OAAA;cAAuBwJ,KAAK,EAAE+C,QAAS;cAAAV,QAAA,EAAEU;YAAQ,GAApCA,QAAQ;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTjM,OAAA;YAAK4L,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpC7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAE1M,aAAc;cAACoM,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC7L,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cACE6I,IAAI,EAAC,UAAU;cACfpF,EAAE,EAAC,aAAa;cAChB4I,OAAO,EAAEpL,iBAAiB,CAACE,MAAO;cAClCgL,QAAQ,EAAEA,CAAA,KAAMpC,4BAA4B,CAAC,QAAQ;YAAE;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACFjM,OAAA;cAAOsM,OAAO,EAAC,aAAa;cAAAT,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cACE6I,IAAI,EAAC,UAAU;cACfpF,EAAE,EAAC,aAAa;cAChB4I,OAAO,EAAEpL,iBAAiB,CAACG,MAAO;cAClC+K,QAAQ,EAAEA,CAAA,KAAMpC,4BAA4B,CAAC,QAAQ;YAAE;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACFjM,OAAA;cAAOsM,OAAO,EAAC,aAAa;cAAAT,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cACE6I,IAAI,EAAC,UAAU;cACfpF,EAAE,EAAC,aAAa;cAChB4I,OAAO,EAAEpL,iBAAiB,CAACI,MAAO;cAClC8K,QAAQ,EAAEA,CAAA,KAAMpC,4BAA4B,CAAC,QAAQ;YAAE;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACFjM,OAAA;cAAOsM,OAAO,EAAC,aAAa;cAAAT,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjM,OAAA;QAAK4L,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAE7M;UAAY;YAAAyM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,mBAAmB;YACtB4I,OAAO,EAAEtK,wBAAwB,CAAC,wBAAwB,CAAE;YAC5DoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,wBAAwB,EAAE,CAACA,IAAI,CAAC,wBAAwB;YAC1D,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,mBAAmB;YAAAT,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,WAAW;YACd4I,OAAO,EAAEtK,wBAAwB,CAAC,aAAa,CAAE;YACjDoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,aAAa,EAAE,CAACA,IAAI,CAAC,aAAa;YACpC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,WAAW;YAAAT,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,SAAS;YACZ4I,OAAO,EAAEtK,wBAAwB,CAAC,WAAW,CAAE;YAC/CoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,WAAW,EAAE,CAACA,IAAI,CAAC,WAAW;YAChC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,SAAS;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,YAAY;YACf4I,OAAO,EAAEtK,wBAAwB,CAAC,cAAc,CAAE;YAClDoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,cAAc,EAAE,CAACA,IAAI,CAAC,cAAc;YACtC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,YAAY;YAAAT,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,aAAa;YAChB4I,OAAO,EAAEtK,wBAAwB,CAAC,SAAS,CAAE;YAC7CoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,SAAS,EAAE,CAACA,IAAI,CAAC,SAAS;YAC5B,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,aAAa;YAAAT,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YACE6I,IAAI,EAAC,UAAU;YACfpF,EAAE,EAAC,eAAe;YAClB4I,OAAO,EAAEtK,wBAAwB,CAAC,WAAW,CAAE;YAC/CoK,QAAQ,EAAEA,CAAA,KAAMnK,2BAA2B,CAACgI,IAAI,KAAK;cACnD,GAAGA,IAAI;cACP,WAAW,EAAE,CAACA,IAAI,CAAC,WAAW;YAChC,CAAC,CAAC;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACFjM,OAAA;YAAOsM,OAAO,EAAC,eAAe;YAAAT,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjM,OAAA;QAAK4L,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClD7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAE5M;UAAa;YAAAwM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAAa;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE7DjM,OAAA;UAAK4L,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC7L,OAAA;YAAK4L,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC7L,OAAA;cACE4L,SAAS,EAAC,wBAAwB;cAClCY,KAAK,EAAE;gBACLC,IAAI,EAAE,GAAG/F,YAAY,CAAC,CAAC,CAAC,GAAG;gBAC3BgG,KAAK,EAAE,GAAGhG,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;cAC7C;YAAE;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENjM,OAAA;YACE6I,IAAI,EAAC,OAAO;YACZoB,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,KAAK;YACTV,KAAK,EAAE9C,YAAY,CAAC,CAAC,CAAE;YACvByF,QAAQ,EAAG7C,CAAC,IAAKiC,kBAAkB,CAACjC,CAAC,EAAE,CAAC,CAAE;YAC1CsC,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAEFjM,OAAA;YACE6I,IAAI,EAAC,OAAO;YACZoB,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,KAAK;YACTV,KAAK,EAAE9C,YAAY,CAAC,CAAC,CAAE;YACvByF,QAAQ,EAAG7C,CAAC,IAAKiC,kBAAkB,CAACjC,CAAC,EAAE,CAAC,CAAE;YAC1CsC,SAAS,EAAC;UAA8B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7L,OAAA;YAAA6L,QAAA,EAAK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACfjM,OAAA;YAAA6L,QAAA,EAAK;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC7L,OAAA;YACE6I,IAAI,EAAC,MAAM;YACXW,KAAK,EAAE,CAACY,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE6D,cAAc,CAAC,CAAE;YACrE8B,QAAQ,EAAG7C,CAAC,IAAK6B,uBAAuB,CAAC7B,CAAC,EAAE,CAAC,CAAE;YAC/CsC,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFjM,OAAA;YAAK4L,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCjM,OAAA;YACE6I,IAAI,EAAC,MAAM;YACXW,KAAK,EAAE,CAACY,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE6D,cAAc,CAAC,CAAE;YAC3E8B,QAAQ,EAAG7C,CAAC,IAAK6B,uBAAuB,CAAC7B,CAAC,EAAE,CAAC,CAAE;YAC/CsC,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC7L,OAAA;YAAQ4L,SAAS,EAAC,qBAAqB;YAACe,OAAO,EAAEnB,iBAAkB;YAAAK,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjM,OAAA;YAAQ4L,SAAS,EAAC,qBAAqB;YAACe,OAAO,EAAEjB,YAAa;YAAAG,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOH,CAAC,eAENjM,OAAA;MAAK4L,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACrB7L,OAAA,CAACF,UAAU;QACT8M,KAAK,EAAC,QAAQ;QACdxH,WAAW,EAAC;MAA4F;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzG,CAAC,eACRjM,OAAA;QAAK4L,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC7L,OAAA;UAAK4L,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7L,OAAA;YACE4L,SAAS,EAAC,4BAA4B;YACtCe,OAAO,EAAEhB,mBAAoB;YAC7B,cAAW,gBAAgB;YAAAE,QAAA,gBAE3B7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAEzM;YAAS;cAAAqM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjM,OAAA;YAAA6L,QAAA,GAAK,UAAQ,EAAChL,YAAY,CAACuG,MAAM,EAAC,UAAQ,EAACzG,SAAS,CAACyG,MAAM,EAAC,OAAK;UAAA;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CAAC,cAAU,eAAAjM,OAAA;QAAK4L,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtC5J,OAAO,gBACNjC,OAAA;UAAK4L,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7L,OAAA,CAACL,SAAS;YAACiM,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCjM,OAAA;YAAA6L,QAAA,EAAG;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,GACJ9J,KAAK,gBACPnC,OAAA;UAAK4L,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAE1J;QAAK;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,GAC1CpL,YAAY,CAACuG,MAAM,KAAK,CAAC,gBAC3BpH,OAAA;UAAK4L,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAE3EjM,OAAA,CAAAE,SAAA;UAAA2L,QAAA,GAEGZ,QAAQ,iBACPjL,OAAA,CAACjB,OAAO;YAEN0E,EAAE,EAAEwH,QAAQ,CAACxH,EAAG;YAChBE,GAAG,EAAEsH,QAAQ,CAACtH,GAAI;YAClBC,KAAK,EAAEqH,QAAQ,CAACrH,KAAM;YACtBG,IAAI,EAAEkH,QAAQ,CAAClH,IAAK;YACpBG,OAAO,EAAE+G,QAAQ,CAAC/G,OAAQ;YAC1BE,QAAQ,EAAE6G,QAAQ,CAAC7G,QAAS;YAC5BC,QAAQ,EAAE4G,QAAQ,CAAC5G,QAAS;YAC5BE,QAAQ,EAAE0G,QAAQ,CAAC1G,QAAS;YAC5BC,MAAM,EAAEyG,QAAQ,CAACzG,MAAO;YACxBI,UAAU,EAAEqG,QAAQ,CAACrG,UAAW;YAChCG,KAAK,EAAEkG,QAAQ,CAAClG;UAAM,GAXjBkG,QAAQ,CAACxH,EAAE,IAAI,CAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYtB,CACF,EAGAf,aAAa,CAAC1H,GAAG,CAAC,CAACL,GAAG,EAAEC,KAAK,kBAC5BpD,OAAA,CAACjB,OAAO;YAEN0E,EAAE,EAAEN,GAAG,CAACM,EAAG;YACXE,GAAG,EAAER,GAAG,CAACQ,GAAI;YACbC,KAAK,EAAET,GAAG,CAACS,KAAM;YACjBG,IAAI,EAAEZ,GAAG,CAACY,IAAK;YACfG,OAAO,EAAEf,GAAG,CAACe,OAAQ;YACrBE,QAAQ,EAAEjB,GAAG,CAACiB,QAAS;YACvBC,QAAQ,EAAElB,GAAG,CAACkB,QAAS;YACvBE,QAAQ,EAAEpB,GAAG,CAACoB,QAAS;YACvBC,MAAM,EAAErB,GAAG,CAACqB,MAAO;YACnBI,UAAU,EAAEzB,GAAG,CAACyB,UAAW;YAC3BG,KAAK,EAAE5B,GAAG,CAAC4B;UAAM,GAXZ5B,GAAG,CAACM,EAAE,IAAIL,KAAK;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYrB,CACF,CAAC;QAAA,eACF;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5J,mBAAmB,iBAClBrC,OAAA;MAAK4L,SAAS,EAAC,wBAAwB;MAACe,OAAO,EAAEhB;IAAoB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC5E,eAGDjM,OAAA;MAAK4L,SAAS,EAAE,yBAAyBvJ,mBAAmB,GAAG,MAAM,GAAG,EAAE,EAAG;MAAAwJ,QAAA,gBAC3E7L,OAAA;QAAK4L,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC7L,OAAA;UAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAEzM;UAAS;YAAAqM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDjM,OAAA;UAAQ4L,SAAS,EAAC,mBAAmB;UAACe,OAAO,EAAEhB,mBAAoB;UAAAE,QAAA,eACjE7L,OAAA,CAACf,eAAe;YAACiN,IAAI,EAAExM;UAAQ;YAAAoM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENjM,OAAA;QAAK4L,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7L,OAAA;UAAK4L,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7L,OAAA;YAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAE/M;YAAY;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAAa;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DjM,OAAA;YAAK4L,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAChC7L,OAAA;cAAK4L,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7L,OAAA;gBACEwJ,KAAK,EAAEjI,eAAgB;gBACvB4K,QAAQ,EAAE9C,mBAAoB;gBAC9BuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAE3B7L,OAAA;kBAAQwJ,KAAK,EAAC,EAAE;kBAAAqC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvC7C,yBAAyB,CAAC,CAAC,CAAC5F,GAAG,CAAC4I,QAAQ,iBACvCpM,OAAA;kBAAuBwJ,KAAK,EAAE4C,QAAS;kBAAAP,QAAA,EAAEO;gBAAQ,GAApCA,QAAQ;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACTjM,OAAA,CAACf,eAAe;gBAACiN,IAAI,EAAE1M,aAAc;gBAACoM,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7L,OAAA;YAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAEhN;YAAe;cAAA4M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAAS;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAmB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDjM,OAAA;cAAOsM,OAAO,EAAC,mBAAmB;cAAAT,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAmB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDjM,OAAA;cAAOsM,OAAO,EAAC,mBAAmB;cAAAT,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAmB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDjM,OAAA;cAAOsM,OAAO,EAAC,mBAAmB;cAAAT,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7L,OAAA;YAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAE7M;YAAY;cAAAyM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAAiB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAkB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CjM,OAAA;cAAOsM,OAAO,EAAC,kBAAkB;cAAAT,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAgB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CjM,OAAA;cAAOsM,OAAO,EAAC,gBAAgB;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAmB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDjM,OAAA;cAAOsM,OAAO,EAAC,mBAAmB;cAAAT,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAoB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDjM,OAAA;cAAOsM,OAAO,EAAC,oBAAoB;cAAAT,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAO6I,IAAI,EAAC,UAAU;cAACpF,EAAE,EAAC;YAAsB;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDjM,OAAA;cAAOsM,OAAO,EAAC,sBAAsB;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjM,OAAA;UAAK4L,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD7L,OAAA;YAAA6L,QAAA,gBAAI7L,OAAA,CAACf,eAAe;cAACiN,IAAI,EAAE5M;YAAa;cAAAwM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAAa;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE7DjM,OAAA;YAAK4L,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC7L,OAAA;cAAK4L,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAClC7L,OAAA;gBACE4L,SAAS,EAAC,wBAAwB;gBAClCY,KAAK,EAAE;kBACLC,IAAI,EAAE,GAAG/F,YAAY,CAAC,CAAC,CAAC,GAAG;kBAC3BgG,KAAK,EAAE,GAAGhG,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;gBAC7C;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENjM,OAAA;cACE6I,IAAI,EAAC,OAAO;cACZoB,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTV,KAAK,EAAE9C,YAAY,CAAC,CAAC,CAAE;cACvByF,QAAQ,EAAG7C,CAAC,IAAKiC,kBAAkB,CAACjC,CAAC,EAAE,CAAC,CAAE;cAC1CsC,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAEFjM,OAAA;cACE6I,IAAI,EAAC,OAAO;cACZoB,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTV,KAAK,EAAE9C,YAAY,CAAC,CAAC,CAAE;cACvByF,QAAQ,EAAG7C,CAAC,IAAKiC,kBAAkB,CAACjC,CAAC,EAAE,CAAC,CAAE;cAC1CsC,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjM,OAAA;YAAK4L,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7L,OAAA;cAAA6L,QAAA,EAAK;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACfjM,OAAA;cAAA6L,QAAA,EAAK;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENjM,OAAA;YAAK4L,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7L,OAAA;cACE6I,IAAI,EAAC,MAAM;cACXW,KAAK,EAAE,CAACY,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE6D,cAAc,CAAC,CAAE;cACrE8B,QAAQ,EAAG7C,CAAC,IAAK6B,uBAAuB,CAAC7B,CAAC,EAAE,CAAC,CAAE;cAC/CsC,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACFjM,OAAA;cAAK4L,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzCjM,OAAA;cACE6I,IAAI,EAAC,MAAM;cACXW,KAAK,EAAE,CAACY,KAAK,CAAC5D,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE6D,cAAc,CAAC,CAAE;cAC3E8B,QAAQ,EAAG7C,CAAC,IAAK6B,uBAAuB,CAAC7B,CAAC,EAAE,CAAC,CAAE;cAC/CsC,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjM,OAAA;YAAK4L,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC7L,OAAA;cAAQ4L,SAAS,EAAC,qBAAqB;cAACe,OAAO,EAAEnB,iBAAkB;cAAAK,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjM,OAAA;cAAQ4L,SAAS,EAAC,qBAAqB;cAACe,OAAO,EAAEjB,YAAa;cAAAG,QAAA,EAAC;YAE/D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7L,EAAA,CA58BID,QAAQ;EAAA,QAC4BtB,eAAe,EACtCC,WAAW,EACiCc,WAAW,EAkBpEC,YAAY;AAAA;AAAAgN,EAAA,GArBZ1M,QAAQ;AA88Bd,eAAeA,QAAQ;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}