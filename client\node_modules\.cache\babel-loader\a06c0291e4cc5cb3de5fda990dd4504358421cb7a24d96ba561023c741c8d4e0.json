{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\Admin\\\\JobsAdmin.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-unused-vars */\n/* eslint-disable no-useless-escape */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faPlus, faEdit, faTrash, faSearch, faExclamationCircle, faCheckCircle, faEye, faFire, faTimes, faBuilding, faImage, faSpinner, faChevronDown, faExclamationTriangle, faSync } from '@fortawesome/free-solid-svg-icons';\nimport '../../css/JobsAdmin.css';\nimport '../../css/JobFormModal.css';\nimport '../../css/shared-delete-dialog.css';\nimport ApiService from '../../services/apiService';\n// Import search-fix.css last to ensure it takes precedence\nimport '../../css/search-fix.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EXPERIENCE_OPTIONS = [\"No Experience Required\", \"Entry Level\", \"Mid Level\", \"Senior Level\", \"Manager\", \"Executive\"];\nconst JobsAdmin = () => {\n  _s();\n  const [jobs, setJobs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [companies, setCompanies] = useState([]);\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\n\n  // Delete confirmation dialog state\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [jobToDelete, setJobToDelete] = useState(null);\n\n  // New job form state\n  // Get today's date in YYYY-MM-DD format for the date inputs\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\n\n  // Format date for display in table\n  const formatDisplayDate = dateString => {\n    if (!dateString || dateString === '-') return '-';\n    try {\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (err) {\n      console.error(\"Error formatting display date:\", err);\n      return dateString;\n    }\n  };\n  const [newJobForm, setNewJobForm] = useState({\n    job_title: '',\n    year: new Date().getFullYear(),\n    start_date: getTodayFormatted(),\n    end_date: '',\n    send_cv_email: '',\n    main_topics: 'Government Jobs',\n    // Set a default valid value from the enum\n    sub_topics: [],\n    job_type: 'Full Time Jobs',\n    // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n    experience_level: 'Entry Level',\n    // New field for experience level\n    min_salary: '',\n    max_salary: '',\n    job_description: '',\n    job_post_image: null,\n    job_post_thumbnail: null,\n    company_name: '',\n    company_logo: null,\n    // UI-only fields (not in database)\n    status: 'Active',\n    hot: false\n  });\n\n  // Image preview states\n  const [jobImagePreview, setJobImagePreview] = useState(null);\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\n  const [logoPreview, setLogoPreview] = useState(null);\n  // State for managing sub-topics\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\n\n  // Predefined list of sub-topics for checkbox selection (fallback)\n  const defaultSubTopics = ['IT-software/DB/QA/WEB/GRAPHICS/GIS', 'IT-SOFTWARE/NETWORKS/SYSTEMS', 'ACCOUNTING/AUDITING/FINANCE', 'BANKING & FINANCE/INSURANCE', 'SALES/MARKETING/MERCHANDISING', 'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS', 'LOGISTICS', 'ENG-MECH/AUTO/ELE', 'MANUFACTURING', 'MEDIA/ADVERT/COMMUNICATION', 'SECURITY', 'EDUCATION', 'SUPERVISION', 'APPAREL/CLOTHING', 'TICKETING/AIRLINE', 'R&D/SCIENCE/RESEARCH', 'AGRICULTURE/ENVIRONMENT'];\n\n  // Load available sub-topics from localStorage\n  const loadSubTopicsFromStorage = () => {\n    try {\n      const savedSubTopics = localStorage.getItem('adminSubTopics');\n      if (savedSubTopics) {\n        const parsedSubTopics = JSON.parse(savedSubTopics);\n        // Filter only active sub-topics and extract just the names\n        const activeSubTopicNames = parsedSubTopics.filter(topic => topic.isActive).map(topic => topic.name);\n        setAvailableSubTopics(activeSubTopicNames);\n      } else {\n        // Use default sub-topics if no saved data\n        setAvailableSubTopics(defaultSubTopics);\n      }\n    } catch (err) {\n      console.error(\"Error loading sub-topics from localStorage:\", err);\n      // Fallback to default sub-topics\n      setAvailableSubTopics(defaultSubTopics);\n    }\n  };\n\n  // Initialize available sub-topics\n  useEffect(() => {\n    loadSubTopicsFromStorage();\n  }, []);\n\n  // Listen for sub-topics updates from SubTopicsAdmin\n  useEffect(() => {\n    const handleSubTopicsUpdate = event => {\n      console.log(\"Sub-topics updated, refreshing available options...\");\n      loadSubTopicsFromStorage();\n    };\n\n    // Add event listener for sub-topics updates\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n    };\n  }, []);\n\n  // Fetch jobs from backend\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        const response = await ApiService.jobs.getAll();\n\n        // Transform backend data to match the format used in frontend\n        // Adding default values for fields that don't exist in the database\n        const formattedJobs = response.data.map(job => {\n          // Determine the best date to use\n          let dateToUse;\n          if (job.created_at) {\n            // Prefer created_at if available\n            dateToUse = job.created_at;\n          } else if (job.start_date) {\n            // Otherwise use start_date\n            dateToUse = job.start_date;\n          } else if (job.posted_date) {\n            // Fall back to posted_date\n            dateToUse = job.posted_date;\n          } else {\n            // If no dates are available, use current date\n            dateToUse = new Date().toISOString();\n          }\n\n          // Compose salary display string\n          let salaryDisplay = '';\n          if (job.min_salary && job.max_salary) {\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n          } else if (job.min_salary) {\n            salaryDisplay = `${job.min_salary}`;\n          } else if (job.max_salary) {\n            salaryDisplay = `${job.max_salary}`;\n          } else {\n            salaryDisplay = '';\n          }\n          return {\n            id: job.job_id,\n            hot: job.hot || false,\n            // Use actual hot value from database\n            company: job.company_name,\n            position: job.job_title,\n            location: job.main_topics,\n            workTime: job.job_type,\n            experience_level: job.experience_level,\n            cv_email: job.send_cv_email,\n            salary: salaryDisplay,\n            status: 'Active',\n            // Default since it doesn't exist in DB\n            datePosted: dateToUse,\n            rawDatePosted: job.start_date || job.posted_date || job.created_at,\n            // Store raw date for debugging\n            views: job.view_count || 0,\n            // Use actual view count if available\n            end_date: job.end_date // Add end_date for auto-delete functionality\n          };\n        });\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching jobs:\", err);\n        setError(\"Failed to load jobs from server\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchJobs();\n  }, []);\n\n  // Auto-delete expired jobs (2 weeks after end date)\n  useEffect(() => {\n    const checkAndDeleteExpiredJobs = async () => {\n      try {\n        const currentDate = new Date();\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\n\n        // Get all jobs to check for expired ones\n        const response = await ApiService.jobs.getAll();\n        const allJobs = response.data;\n        const expiredJobs = allJobs.filter(job => {\n          if (!job.end_date) return false; // Skip jobs without end date\n\n          const endDate = new Date(job.end_date);\n          const timeDifference = currentDate.getTime() - endDate.getTime();\n\n          // Check if job is expired by more than 2 weeks\n          return timeDifference > twoWeeksInMs;\n        });\n\n        // Delete expired jobs\n        for (const expiredJob of expiredJobs) {\n          try {\n            await ApiService.jobs.delete(expiredJob.job_id);\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\n          } catch (deleteErr) {\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\n          }\n        }\n\n        // If any jobs were deleted, refresh the jobs list\n        if (expiredJobs.length > 0) {\n          // Refresh jobs list by fetching again\n          const updatedResponse = await ApiService.jobs.getAll();\n          const formattedJobs = updatedResponse.data.map(job => {\n            let dateToUse;\n            if (job.created_at) {\n              dateToUse = job.created_at;\n            } else if (job.start_date) {\n              dateToUse = job.start_date;\n            } else if (job.posted_date) {\n              dateToUse = job.posted_date;\n            } else {\n              dateToUse = new Date().toISOString();\n            }\n            let salaryDisplay = '';\n            if (job.min_salary && job.max_salary) {\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n            } else if (job.min_salary) {\n              salaryDisplay = `${job.min_salary}`;\n            } else if (job.max_salary) {\n              salaryDisplay = `${job.max_salary}`;\n            } else {\n              salaryDisplay = '';\n            }\n            return {\n              id: job.job_id,\n              hot: job.hot || false,\n              company: job.company_name,\n              position: job.job_title,\n              location: job.main_topics,\n              workTime: job.job_type,\n              experience_level: job.experience_level,\n              cv_email: job.send_cv_email,\n              salary: salaryDisplay,\n              status: 'Active',\n              datePosted: dateToUse,\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\n              views: job.view_count || 0,\n              end_date: job.end_date\n            };\n          });\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n          setJobs(sortedJobs);\n        }\n      } catch (err) {\n        console.error(\"Error checking for expired jobs:\", err);\n        // Don't set error state here to avoid disrupting normal operation\n      }\n    };\n\n    // Run the check immediately when component mounts\n    checkAndDeleteExpiredJobs();\n\n    // Set up interval to check every hour (3600000 ms)\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\n\n    // Cleanup interval on component unmount\n    return () => clearInterval(intervalId);\n  }, []);\n\n  // Fetch companies when modal is opened\n  useEffect(() => {\n    if (isModalOpen) {\n      console.log(\"Modal opened, fetching companies...\");\n      fetchCompanies();\n    }\n  }, [isModalOpen]);\n  const fetchCompanies = async () => {\n    try {\n      console.log(\"Fetching companies from API...\");\n      const response = await ApiService.companies.getAll();\n      const companiesData = response.data.data || [];\n      console.log(\"Companies fetched:\", companiesData);\n      setCompanies(companiesData);\n\n      // If we're editing a job and have a company name, select the matching company\n      if (selectedJob && newJobForm.company_name) {\n        const matchingCompany = companiesData.find(company => company.company_name === newJobForm.company_name);\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\n          setLogoPreview(matchingCompany.company_logo_url);\n        }\n      }\n    } catch (err) {\n      console.error('Error fetching companies:', err);\n      // Don't set error state here to avoid disrupting the job form\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const handleEditJob = async job => {\n    try {\n      setLoading(true);\n      // Fetch full job details from backend\n      const response = await ApiService.jobs.getById(job.id);\n      const fullJobDetails = response.data;\n      console.log(\"Job details from API:\", fullJobDetails);\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\n      setSelectedJob(job);\n      // Initialize form with job data - only using fields from the actual database schema\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\n      const formatDate = dateString => {\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\n\n        try {\n          // Parse the date and ensure it's valid\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) {\n            console.warn(\"Invalid date detected:\", dateString);\n            return getTodayFormatted(); // Default to today for invalid dates\n          }\n\n          // Format as YYYY-MM-DD for date input\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n          return `${year}-${month}-${day}`;\n        } catch (err) {\n          console.error(\"Error formatting date:\", err);\n          return getTodayFormatted();\n        }\n      };\n\n      // Set image previews\n      if (fullJobDetails.job_post_image) {\n        setJobImagePreview(fullJobDetails.job_post_image);\n      }\n      if (fullJobDetails.job_post_thumbnail) {\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\n      }\n\n      // Handle company logo preview\n      let companyLogoUrl = null;\n      if (fullJobDetails.company_logo) {\n        setLogoPreview(fullJobDetails.company_logo);\n        companyLogoUrl = fullJobDetails.company_logo;\n      } else if (fullJobDetails.company_logo_url) {\n        setLogoPreview(fullJobDetails.company_logo_url);\n        companyLogoUrl = fullJobDetails.company_logo_url;\n      } else {\n        // Fetch companies to find the logo\n        try {\n          const companiesResponse = await ApiService.companies.getAll();\n          const companiesData = companiesResponse.data.data || [];\n          const matchingCompany = companiesData.find(company => company.company_name === fullJobDetails.company_name);\n          if (matchingCompany && matchingCompany.company_logo_url) {\n            setLogoPreview(matchingCompany.company_logo_url);\n            companyLogoUrl = matchingCompany.company_logo_url;\n          }\n        } catch (err) {\n          console.error(\"Error finding company logo:\", err);\n        }\n      }\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\n      let expLevel = fullJobDetails.experience_level;\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\n        expLevel = \"No Experience Required\";\n      }\n      setNewJobForm({\n        job_title: fullJobDetails.job_title,\n        company_name: fullJobDetails.company_name,\n        year: fullJobDetails.year || new Date().getFullYear(),\n        start_date: formatDate(fullJobDetails.start_date),\n        end_date: formatDate(fullJobDetails.end_date),\n        job_type: fullJobDetails.job_type,\n        experience_level: expLevel,\n        min_salary: fullJobDetails.min_salary || '',\n        max_salary: fullJobDetails.max_salary || '',\n        send_cv_email: fullJobDetails.send_cv_email || '',\n        main_topics: fullJobDetails.main_topics,\n        sub_topics: (() => {\n          try {\n            // Try to parse sub_topics as JSON if it's a string\n            if (!fullJobDetails.sub_topics) return [];\n            if (typeof fullJobDetails.sub_topics === 'string') {\n              // Check if it starts with [ which would indicate a likely JSON array\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\n                return JSON.parse(fullJobDetails.sub_topics);\n              } else {\n                // If it's not a JSON array, treat it as a single item\n                return [fullJobDetails.sub_topics];\n              }\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\n              return fullJobDetails.sub_topics;\n            } else {\n              return [];\n            }\n          } catch (err) {\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\n          }\n        })(),\n        job_description: fullJobDetails.job_description || '',\n        job_post_image: fullJobDetails.job_post_image,\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\n        company_logo: fullJobDetails.company_logo,\n        company_logo_url: companyLogoUrl,\n        // Store the company logo URL\n        // Use default values for UI-only fields that don't exist in the database\n        status: 'Active',\n        hot: fullJobDetails.hot || false // Use actual hot value from database\n      });\n      setIsModalOpen(true);\n      setError(null);\n    } catch (err) {\n      console.error(\"Error fetching job details:\", err);\n      setError(`Failed to load job details for ${job.position}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteJob = async jobId => {\n    try {\n      setLoading(true);\n      await ApiService.jobs.delete(jobId);\n      setJobs(jobs.filter(job => job.id !== jobId));\n      setError(null);\n      setShowDeleteConfirm(false);\n      setJobToDelete(null);\n    } catch (err) {\n      console.error(\"Error deleting job:\", err);\n      setError(\"Failed to delete job\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to show delete confirmation\n  const confirmDelete = job => {\n    setJobToDelete(job);\n    setShowDeleteConfirm(true);\n  };\n\n  // Function to cancel delete\n  const cancelDelete = () => {\n    setShowDeleteConfirm(false);\n    setJobToDelete(null);\n  };\n  const handleToggleStatus = async jobId => {\n    try {\n      setLoading(true);\n      const jobToUpdate = jobs.find(job => job.id === jobId);\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\n\n      // Since we don't have a status column in the database,\n      // we'll just update the local state without sending to the backend\n      // In a real application, you would add the status column to the database\n\n      // Update local state only\n      setJobs(jobs.map(job => job.id === jobId ? {\n        ...job,\n        status: newStatus\n      } : job));\n      setError(null);\n    } catch (err) {\n      console.error(\"Error updating job status:\", err);\n      setError(\"Failed to update job status\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openNewJobModal = () => {\n    setIsModalOpen(true);\n    setSelectedJob(null);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setSelectedJob(null);\n    // Reset form\n    setNewJobForm({\n      job_title: '',\n      year: new Date().getFullYear(),\n      start_date: getTodayFormatted(),\n      end_date: '',\n      send_cv_email: '',\n      main_topics: 'Government Jobs',\n      // Ensure we always have a valid value\n      sub_topics: [],\n      job_type: 'Full Time Jobs',\n      // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n      experience_level: 'Entry Level',\n      min_salary: '',\n      max_salary: '',\n      job_description: '',\n      job_post_image: null,\n      job_post_thumbnail: null,\n      company_name: '',\n      company_logo: null,\n      // UI-only fields (not in database)\n      status: 'Active',\n      hot: false\n    });\n    // Reset previews\n    setJobImagePreview(null);\n    setThumbnailPreview(null);\n    setLogoPreview(null);\n    setShowCompanyDropdown(false);\n  };\n\n  // Basic client-side text sanitization\n  const sanitizeInputText = text => {\n    if (!text) return '';\n    // Modified to preserve Unicode characters in the job title\n    // Only remove control characters and potentially problematic chars\n    return text.replace(/[--]/g, '') // Remove control characters\n    .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\n    .trim();\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n\n    // Apply sanitization to text fields that might contain problematic characters\n    if (name === 'job_title' || name === 'job_description') {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\n      });\n    } else {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value\n      });\n    }\n  };\n  const handleImageUpload = (e, imageType) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (imageType === 'job_post_image') {\n          setJobImagePreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_image: file\n          });\n        } else if (imageType === 'job_post_thumbnail') {\n          setThumbnailPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_thumbnail: file\n          });\n        } else if (imageType === 'company_logo') {\n          setLogoPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            company_logo: file\n          });\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSelectCompany = company => {\n    console.log(\"Company selected:\", company);\n\n    // Validate company has a name\n    if (!company || !company.company_name) {\n      console.error(\"Invalid company selected\");\n      return;\n    }\n\n    // Set company name in the form\n    setNewJobForm({\n      ...newJobForm,\n      company_name: company.company_name,\n      company_logo: null,\n      // Reset the file input since we're using an existing logo\n      company_logo_url: company.company_logo_url // Store the logo URL\n    });\n\n    // Set logo preview\n    if (company.company_logo_url) {\n      setLogoPreview(company.company_logo_url);\n    } else {\n      setLogoPreview(null);\n    }\n\n    // Close the dropdown\n    setShowCompanyDropdown(false);\n  };\n\n  // Add function to remove logo\n  const handleRemoveLogo = () => {\n    setLogoPreview(null);\n    // If there was a logo file in the form, clear it\n    if (newJobForm.company_logo) {\n      setNewJobForm({\n        ...newJobForm,\n        company_logo: null\n      });\n    }\n  };\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n\n    // Debug the form state\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\n    console.log(\"Original form state:\", newJobForm);\n    console.log(\"Selected job:\", selectedJob);\n\n    // Client-side validation for required fields\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\n      setError(\"Job title is required\");\n      return;\n    }\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\n      setError(\"Company name is required\");\n      return;\n    }\n\n    // Validate email/URL field if provided\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\n      const value = newJobForm.send_cv_email.trim();\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\n        setError(\"Please enter a valid email address or website URL\");\n        return;\n      }\n    }\n\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\n    let experienceLevelToSend = newJobForm.experience_level;\n    if (!experienceLevelToSend) {\n      experienceLevelToSend = \"No Experience Required\";\n    }\n    try {\n      setLoading(true);\n\n      // Create a sanitized copy of the form data to send to the server\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\n      const sanitizedForm = {\n        ...newJobForm,\n        job_title: sanitizedTitle,\n        job_description: sanitizeInputText(newJobForm.job_description),\n        experience_level: experienceLevelToSend,\n        // For new jobs, set today's date as the start_date if not provided\n        start_date: selectedJob ? newJobForm.start_date : newJobForm.start_date || getTodayFormatted()\n      };\n      console.log(\"Sanitized form data:\", sanitizedForm);\n\n      // Prepare form data for API - create a completely fresh FormData object\n      const formData = new FormData();\n      // Add only fields that exist in the database schema, ensuring each field is only added once\n      const dbFields = ['job_title', 'company_name', 'year', 'start_date', 'end_date', 'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level', 'min_salary', 'max_salary', 'job_description', 'hot'];\n\n      // Add text fields one by one to avoid duplicates\n      dbFields.forEach(key => {\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\n          // Special handling for sub_topics\n          if (key === 'sub_topics') {\n            try {\n              if (Array.isArray(sanitizedForm[key])) {\n                // Ensure we're sending a clean array without unexpected characters\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\n                formData.append(key, JSON.stringify(cleanSubTopics));\n              } else {\n                // If it's a string or something else, convert to array\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\n              }\n            } catch (err) {\n              console.error(\"Error formatting sub_topics:\", err);\n              formData.append(key, '[]'); // Fallback to empty array\n            }\n          } else {\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\n            formData.append(key, sanitizedForm[key]);\n          }\n        }\n      });\n\n      // Add file fields if they exist\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\n        formData.append('job_post_image', sanitizedForm.job_post_image);\n      }\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\n      }\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\n        formData.append('company_logo', sanitizedForm.company_logo);\n      }\n\n      // If we have a logo preview from an existing company but no file, add the URL\n      if (logoPreview && !sanitizedForm.company_logo) {\n        formData.append('existing_company_logo_url', logoPreview);\n      }\n\n      // Add company_logo_url if it exists\n      if (sanitizedForm.company_logo_url) {\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\n      }\n\n      // Debug: Log what's being sent in the FormData\n      console.log(\"FormData contents:\");\n      for (let pair of formData.entries()) {\n        console.log(pair[0] + ': ' + pair[1]);\n      }\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\n\n      // Prepare form data for submission\n      if (selectedJob) {\n        // Update existing job\n        try {\n          await ApiService.jobs.update(selectedJob.id, formData);\n          // Successfully updated job\n        } catch (apiError) {\n          // Handle update error silently\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      } else {\n        // Create new job\n        try {\n          await ApiService.jobs.create(formData);\n          // Successfully created job\n        } catch (apiError) {\n          var _apiError$response, _apiError$response2, _apiError$response3;\n          // Log detailed error information\n          console.error(\"API Error Details:\", {\n            message: apiError.message,\n            status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n            statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n            data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data\n          });\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      }\n      setError(null);\n      // Close modal\n      closeModal();\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      console.error(\"Error saving job:\", err);\n      // Show more detailed error message if available\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredJobs = jobs.filter(job => {\n    const matchesSearch = job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase()) || job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesSearch;\n  });\n\n  // Add this useEffect to handle closing the dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const dropdownElement = document.querySelector('.company-select-dropdown');\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\n        setShowCompanyDropdown(false);\n      }\n    };\n    if (showCompanyDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [showCompanyDropdown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"jobs-admin-container\",\n    children: [showDeleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-dialog\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faExclamationTriangle,\n            className: \"delete-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Deletion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to delete this job posting?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: jobToDelete === null || jobToDelete === void 0 ? void 0 : jobToDelete.position\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 930,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-delete-btn\",\n            onClick: cancelDelete,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-delete-btn\",\n            onClick: () => handleDeleteJob(jobToDelete.id),\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 924,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"jobs-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"jobs-title\",\n        children: \"Job Listings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"refresh-button\",\n          onClick: () => {\n            setLoading(true);\n            const fetchJobs = async () => {\n              try {\n                const response = await ApiService.jobs.getAll();\n\n                // Transform backend data to match the format used in frontend\n                const formattedJobs = response.data.map(job => {\n                  // Determine the best date to use\n                  let dateToUse;\n                  if (job.created_at) {\n                    dateToUse = job.created_at;\n                  } else if (job.start_date) {\n                    dateToUse = job.start_date;\n                  } else if (job.posted_date) {\n                    dateToUse = job.posted_date;\n                  } else {\n                    dateToUse = new Date().toISOString();\n                  }\n\n                  // Compose salary display string\n                  let salaryDisplay = '';\n                  if (job.min_salary && job.max_salary) {\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n                  } else if (job.min_salary) {\n                    salaryDisplay = `${job.min_salary}`;\n                  } else if (job.max_salary) {\n                    salaryDisplay = `${job.max_salary}`;\n                  } else {\n                    salaryDisplay = '';\n                  }\n                  return {\n                    id: job.job_id,\n                    hot: job.hot || false,\n                    company: job.company_name,\n                    position: job.job_title,\n                    location: job.main_topics,\n                    workTime: job.job_type,\n                    experience_level: job.experience_level,\n                    cv_email: job.send_cv_email,\n                    salary: salaryDisplay,\n                    status: 'Active',\n                    datePosted: dateToUse,\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\n                    views: job.view_count || 0,\n                    end_date: job.end_date\n                  };\n                });\n\n                // Sort jobs by datePosted descending (newest first)\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n                setJobs(sortedJobs);\n                setError(null);\n              } catch (err) {\n                console.error(\"Error fetching jobs:\", err);\n                setError(\"Failed to load jobs from server\");\n              } finally {\n                setLoading(false);\n              }\n            };\n            fetchJobs();\n          },\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSync\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-job-button\",\n          onClick: openNewJobModal,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add New Job\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 953,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: faExclamationCircle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1031,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1030,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), \"          \", /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search by company or position...\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            className: \"search-input\",\n            style: {\n              paddingLeft: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 82\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1038,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1037,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faSpinner,\n          spin: true,\n          size: \"2x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading jobs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1056,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1054,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"jobs-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Experience Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email / Web URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Salary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Expire Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Posted Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Views\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1070,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.length > 0 ? filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [job.hot && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hot-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFire,\n                    style: {\n                      marginRight: '4px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 27\n                  }, this), \"URGENT\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-position\",\n                  title: job.position,\n                  children: job.position && job.position.length > 35 ? job.position.slice(0, 32) + '...' : job.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-company\",\n                  children: job.company\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.workTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"experience-level\",\n                  title: job.experience_level,\n                  children: job.experience_level || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cv-email\",\n                  title: job.cv_email,\n                  children: job.cv_email ? /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `mailto:${job.cv_email}`,\n                    className: \"email-link\",\n                    children: job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1101,\n                    columnNumber: 27\n                  }, this) : 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.salary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.end_date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.datePosted)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"views-container\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faEye,\n                    className: \"views-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1114,\n                    columnNumber: 25\n                  }, this), job.views]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1113,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button edit-button\",\n                    onClick: () => handleEditJob(job),\n                    title: \"Edit job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1126,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button delete-button\",\n                    onClick: () => confirmDelete(job),\n                    title: \"Delete job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1135,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1119,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"9\",\n                className: \"no-jobs-message\",\n                children: searchTerm ? \"No jobs match your search criteria\" : \"No jobs available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1142,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 11\n        }, this)\n      }, void 0, false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1052,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: e => {\n        // Close modal when clicking outside\n        if (e.target.className === 'modal-overlay') {\n          closeModal();\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: selectedJob ? 'Edit Job' : 'Add New Job'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: closeModal,\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faTimes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1169,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleFormSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_title\",\n                  children: [\"Job Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1176,\n                    columnNumber: 58\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"job_title\",\n                  name: \"job_title\",\n                  value: newJobForm.job_title,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter job title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [\"Company \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1188,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"company-selector\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-select-dropdown\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"company-select-button\",\n                      onClick: () => setShowCompanyDropdown(!showCompanyDropdown),\n                      children: [newJobForm.company_name ? newJobForm.company_name : 'Select a company', /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faChevronDown\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1197,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 25\n                    }, this), showCompanyDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"company-list-dropdown\",\n                      children: [companies && companies.length > 0 ? companies.map(company => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"company-item\",\n                        onClick: () => {\n                          handleSelectCompany(company);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"company-item-logo\",\n                          children: company.company_logo_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: company.company_logo_url,\n                            alt: company.company_name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1213,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                            icon: faBuilding\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1215,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1211,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: company.company_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1218,\n                          columnNumber: 35\n                        }, this)]\n                      }, company.company_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1204,\n                        columnNumber: 33\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-companies\",\n                        children: \"No companies available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1222,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"manual-company-entry\",\n                        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                          children: \"Or enter company name manually:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1225,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          placeholder: \"Enter company name\",\n                          value: newJobForm.company_name || '',\n                          onChange: e => {\n                            setNewJobForm({\n                              ...newJobForm,\n                              company_name: e.target.value\n                            });\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1226,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: \"apply-company-btn\",\n                          onClick: () => setShowCompanyDropdown(false),\n                          children: \"Apply\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1237,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1224,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1201,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 23\n                  }, this), logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"logo-preview-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: logoPreview,\n                      alt: \"Company logo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1252,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"remove-logo-button\",\n                      onClick: handleRemoveLogo,\n                      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faTimes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1258,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1253,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 25\n                  }, this), !logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      id: \"company_logo\",\n                      name: \"company_logo\",\n                      onChange: e => handleImageUpload(e, 'company_logo'),\n                      accept: \"image/*\",\n                      style: {\n                        display: 'none'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"file-upload-label\",\n                      onClick: () => document.getElementById('company_logo').click(),\n                      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faImage\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1279,\n                        columnNumber: 29\n                      }, this), \"Upload Company Logo\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1274,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1265,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"year\",\n                  children: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"year\",\n                  name: \"year\",\n                  value: newJobForm.year,\n                  onChange: handleFormChange,\n                  required: true,\n                  min: \"2000\",\n                  max: \"2100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 19\n              }, this), \"                  \", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_type\",\n                  children: \"Job Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1302,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"job_type\",\n                  name: \"job_type\",\n                  value: newJobForm.job_type,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time Jobs\",\n                    children: \"Full Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time Jobs\",\n                    children: \"Part Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1311,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Remote Jobs\",\n                    children: \"Remote Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1312,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Freelance\",\n                    children: \"Freelance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Temporary\",\n                    children: \"Temporary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1314,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1303,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1301,\n                columnNumber: 43\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"experience_level\",\n                  children: \"Experience Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"experience_level\",\n                  name: \"experience_level\",\n                  value: newJobForm.experience_level,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: EXPERIENCE_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"start_date\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"start_date\",\n                  name: \"start_date\",\n                  value: newJobForm.start_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"end_date\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"end_date\",\n                  name: \"end_date\",\n                  value: newJobForm.end_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"send_cv_email\",\n                  children: \"Email/WebURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"send_cv_email\",\n                  name: \"send_cv_email\",\n                  value: newJobForm.send_cv_email,\n                  onChange: handleFormChange,\n                  placeholder: \"Enter email or website URL for CV submissions (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1361,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"min_salary\",\n                  children: \"Minimum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"min_salary\",\n                  name: \"min_salary\",\n                  value: newJobForm.min_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Minimum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1372,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"max_salary\",\n                  children: \"Maximum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"max_salary\",\n                  name: \"max_salary\",\n                  value: newJobForm.max_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Maximum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group checkbox-group\",\n                style: {\n                  textAlign: 'left'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"hot\",\n                    checked: newJobForm.hot,\n                    onChange: handleFormChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkbox-text\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faFire,\n                      className: \"hot-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 25\n                    }, this), \"Mark as URGENT Job\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1401,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1400,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"main_topics\",\n                  children: \"Main Topic/Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"main_topics\",\n                  name: \"main_topics\",\n                  value: newJobForm.main_topics,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Government Jobs\",\n                    children: \"Government Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1426,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Private Jobs\",\n                    children: \"Private Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1427,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Foreign Jobs\",\n                    children: \"Foreign Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1428,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Internships\",\n                    children: \"Internships\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1429,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1417,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1416,\n              columnNumber: 17\n            }, this), \"                \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sub Topics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1435,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small-text\",\n                    children: [newJobForm.sub_topics.length, \" selected\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1436,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    style: {\n                      marginLeft: '16px',\n                      padding: '2px 8px',\n                      fontSize: '0.9em'\n                    },\n                    onClick: () => setNewJobForm(prev => ({\n                      ...prev,\n                      sub_topics: []\n                    })),\n                    children: \"Clear All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1437,\n                    columnNumber: 3\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1434,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-management-info\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    style: {\n                      color: '#6c757d',\n                      fontStyle: 'italic'\n                    },\n                    children: \"\\uD83D\\uDCA1 Tip: You can manage available sub-topics from the \\\"Sub Topics\\\" section in the sidebar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1446,\n                    columnNumber: 3\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-checkbox-container\",\n                  children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `subtopic-${index}`,\n                      checked: newJobForm.sub_topics.includes(topic),\n                      onChange: e => {\n                        const checked = e.target.checked;\n                        setNewJobForm(prev => {\n                          const set = new Set(prev.sub_topics);\n                          if (checked) {\n                            set.add(topic);\n                          } else {\n                            set.delete(topic);\n                          }\n                          return {\n                            ...prev,\n                            sub_topics: Array.from(set)\n                          };\n                        });\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1456,\n                      columnNumber: 1\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `subtopic-${index}`,\n                      children: topic\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1476,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1452,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1450,\n                  columnNumber: 1\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1433,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1432,\n              columnNumber: 39\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_description\",\n                  children: \"Job Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"job_description\",\n                  name: \"job_description\",\n                  rows: \"5\",\n                  value: newJobForm.job_description,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter detailed job description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1488,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"form-help-text\",\n                  children: \"Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1497,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1486,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_image\",\n                    onChange: e => handleImageUpload(e, 'job_post_image'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_image\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1515,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1516,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1514,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1506,\n                  columnNumber: 21\n                }, this), jobImagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: jobImagePreview,\n                    alt: \"Job Post\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_thumbnail\",\n                    onChange: e => handleImageUpload(e, 'job_post_thumbnail'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1528,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_thumbnail\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1536,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Thumbnail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1537,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1535,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1527,\n                  columnNumber: 21\n                }, this), thumbnailPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"thumbnail-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: thumbnailPreview,\n                    alt: \"Thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1542,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1525,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1503,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"cancel-button\",\n                onClick: closeModal,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1551,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-button\",\n                children: selectedJob ? 'Update Job' : 'Create Job'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1173,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1165,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1159,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 921,\n    columnNumber: 5\n  }, this);\n};\n_s(JobsAdmin, \"K5jflC6THa/hDQwnclixIfI/qg8=\");\n_c = JobsAdmin;\nexport default JobsAdmin;\nvar _c;\n$RefreshReg$(_c, \"JobsAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FontAwesomeIcon", "faPlus", "faEdit", "faTrash", "faSearch", "faExclamationCircle", "faCheckCircle", "faEye", "faFire", "faTimes", "faBuilding", "faImage", "faSpinner", "faChevronDown", "faExclamationTriangle", "faSync", "ApiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EXPERIENCE_OPTIONS", "JobsAdmin", "_s", "jobs", "setJobs", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "isModalOpen", "setIsModalOpen", "companies", "setCompanies", "showCompanyDropdown", "setShowCompanyDropdown", "showDeleteConfirm", "setShowDeleteConfirm", "jobToDelete", "setJobToDelete", "getTodayFormatted", "Date", "toISOString", "split", "formatDisplayDate", "dateString", "date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "err", "console", "newJobForm", "setNewJobForm", "job_title", "getFullYear", "start_date", "end_date", "send_cv_email", "main_topics", "sub_topics", "job_type", "experience_level", "min_salary", "max_salary", "job_description", "job_post_image", "job_post_thumbnail", "company_name", "company_logo", "status", "hot", "jobImagePreview", "setJobImagePreview", "thumbnailPreview", "setThumbnailPreview", "logoPreview", "setLogoPreview", "availableSubTopics", "setAvailableSubTopics", "defaultSubTopics", "loadSubTopicsFromStorage", "savedSubTopics", "localStorage", "getItem", "parsedSubTopics", "JSON", "parse", "activeSubTopicNames", "filter", "topic", "isActive", "map", "name", "handleSubTopicsUpdate", "event", "log", "window", "addEventListener", "removeEventListener", "fetchJobs", "response", "getAll", "formattedJobs", "data", "job", "dateToUse", "created_at", "posted_date", "salaryDisplay", "id", "job_id", "company", "position", "location", "workTime", "cv_email", "salary", "datePosted", "rawDatePosted", "views", "view_count", "sortedJobs", "sort", "a", "b", "checkAndDeleteExpiredJobs", "currentDate", "twoWeeksInMs", "allJobs", "expiredJobs", "endDate", "timeDifference", "<PERSON><PERSON><PERSON>", "delete", "deleteErr", "length", "updatedResponse", "intervalId", "setInterval", "clearInterval", "fetchCompanies", "companiesData", "matchingCompany", "find", "company_logo_url", "handleSearchChange", "e", "target", "value", "handleEditJob", "getById", "fullJobDetails", "formatDate", "warn", "String", "getMonth", "padStart", "getDate", "companyLogoUrl", "companiesResponse", "expLevel", "includes", "trim", "startsWith", "Array", "isArray", "handleDeleteJob", "jobId", "confirmDelete", "cancelDelete", "handleToggleStatus", "jobToUpdate", "newStatus", "openNewJobModal", "closeModal", "sanitizeInputText", "text", "replace", "handleFormChange", "type", "checked", "handleImageUpload", "imageType", "file", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSelectCompany", "handleRemoveLogo", "handleFormSubmit", "preventDefault", "emailRegex", "urlRegex", "test", "experienceLevelToSend", "sanitizedTitle", "sanitizedForm", "formData", "FormData", "dbFields", "for<PERSON>ach", "key", "undefined", "cleanSubTopics", "append", "stringify", "File", "pair", "entries", "get", "update", "apiError", "jobsResponse", "create", "_apiError$response", "_apiError$response2", "_apiError$response3", "message", "statusText", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "errorMessage", "filteredJobs", "matchesSearch", "toLowerCase", "handleClickOutside", "dropdownElement", "document", "querySelector", "contains", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "style", "paddingLeft", "spin", "size", "marginRight", "title", "slice", "href", "display", "colSpan", "onSubmit", "htmlFor", "color", "required", "src", "alt", "company_id", "accept", "getElementById", "click", "min", "max", "option", "step", "textAlign", "marginLeft", "padding", "fontSize", "prev", "fontStyle", "index", "set", "Set", "add", "from", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/Admin/JobsAdmin.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable no-unused-vars */\r\n/* eslint-disable no-useless-escape */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n  faPlus,\r\n  faEdit,\r\n  faTrash,\r\n  faSearch,\r\n  faExclamationCircle,\r\n  faCheckCircle,\r\n  faEye,\r\n  faFire,\r\n  faTimes,\r\n  faBuilding,\r\n  faImage,\r\n  faSpinner,\r\n  faChevronDown,\r\n  faExclamationTriangle,\r\n  faSync\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport '../../css/JobsAdmin.css';\r\nimport '../../css/JobFormModal.css';\r\nimport '../../css/shared-delete-dialog.css';\r\nimport ApiService from '../../services/apiService';\r\n// Import search-fix.css last to ensure it takes precedence\r\nimport '../../css/search-fix.css';\r\n\r\nconst EXPERIENCE_OPTIONS = [\r\n  \"No Experience Required\",\r\n  \"Entry Level\",\r\n  \"Mid Level\",\r\n  \"Senior Level\",\r\n  \"Manager\",\r\n  \"Executive\"\r\n];\r\n\r\nconst JobsAdmin = () => {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedJob, setSelectedJob] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\r\n  \r\n  // Delete confirmation dialog state\r\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n  const [jobToDelete, setJobToDelete] = useState(null);\r\n  \r\n  // New job form state\r\n  // Get today's date in YYYY-MM-DD format for the date inputs\r\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\r\n  \r\n  // Format date for display in table\r\n  const formatDisplayDate = (dateString) => {\r\n    if (!dateString || dateString === '-') return '-';\r\n    try {\r\n      const date = new Date(dateString);\r\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\r\n      return date.toLocaleDateString('en-US', { \r\n        year: 'numeric', \r\n        month: 'short', \r\n        day: 'numeric' \r\n      });\r\n    } catch (err) {\r\n      console.error(\"Error formatting display date:\", err);\r\n      return dateString;\r\n    }\r\n  };\r\n    const [newJobForm, setNewJobForm] = useState({\r\n    job_title: '',\r\n    year: new Date().getFullYear(),\r\n    start_date: getTodayFormatted(),\r\n    end_date: '',\r\n    send_cv_email: '',\r\n    main_topics: 'Government Jobs', // Set a default valid value from the enum\r\n    sub_topics: [],\r\n    job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n    experience_level: 'Entry Level', // New field for experience level\r\n    min_salary: '',\r\n    max_salary: '',\r\n    job_description: '',\r\n    job_post_image: null,\r\n    job_post_thumbnail: null,\r\n    company_name: '',\r\n    company_logo: null,\r\n    // UI-only fields (not in database)\r\n    status: 'Active',\r\n    hot: false\r\n  });\r\n  \r\n  // Image preview states\r\n  const [jobImagePreview, setJobImagePreview] = useState(null);\r\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\r\n  const [logoPreview, setLogoPreview] = useState(null);\r\n  // State for managing sub-topics\r\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\r\n\r\n  // Predefined list of sub-topics for checkbox selection (fallback)\r\n  const defaultSubTopics = [\r\n    'IT-software/DB/QA/WEB/GRAPHICS/GIS',\r\n    'IT-SOFTWARE/NETWORKS/SYSTEMS',\r\n    'ACCOUNTING/AUDITING/FINANCE',\r\n    'BANKING & FINANCE/INSURANCE',\r\n    'SALES/MARKETING/MERCHANDISING',\r\n    'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS',\r\n    'LOGISTICS',\r\n    'ENG-MECH/AUTO/ELE',\r\n    'MANUFACTURING',\r\n    'MEDIA/ADVERT/COMMUNICATION',\r\n    'SECURITY',\r\n    'EDUCATION',\r\n    'SUPERVISION',\r\n    'APPAREL/CLOTHING',\r\n    'TICKETING/AIRLINE',\r\n    'R&D/SCIENCE/RESEARCH',\r\n    'AGRICULTURE/ENVIRONMENT'\r\n  ];\r\n\r\n  // Load available sub-topics from localStorage\r\n  const loadSubTopicsFromStorage = () => {\r\n    try {\r\n      const savedSubTopics = localStorage.getItem('adminSubTopics');\r\n      if (savedSubTopics) {\r\n        const parsedSubTopics = JSON.parse(savedSubTopics);\r\n        // Filter only active sub-topics and extract just the names\r\n        const activeSubTopicNames = parsedSubTopics\r\n          .filter(topic => topic.isActive)\r\n          .map(topic => topic.name);\r\n        setAvailableSubTopics(activeSubTopicNames);\r\n      } else {\r\n        // Use default sub-topics if no saved data\r\n        setAvailableSubTopics(defaultSubTopics);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error loading sub-topics from localStorage:\", err);\r\n      // Fallback to default sub-topics\r\n      setAvailableSubTopics(defaultSubTopics);\r\n    }\r\n  };\r\n\r\n  // Initialize available sub-topics\r\n  useEffect(() => {\r\n    loadSubTopicsFromStorage();\r\n  }, []);\r\n\r\n  // Listen for sub-topics updates from SubTopicsAdmin\r\n  useEffect(() => {\r\n    const handleSubTopicsUpdate = (event) => {\r\n      console.log(\"Sub-topics updated, refreshing available options...\");\r\n      loadSubTopicsFromStorage();\r\n    };\r\n\r\n    // Add event listener for sub-topics updates\r\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Fetch jobs from backend\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await ApiService.jobs.getAll();\r\n        \r\n        // Transform backend data to match the format used in frontend\r\n        // Adding default values for fields that don't exist in the database\r\n        const formattedJobs = response.data.map(job => {\r\n          // Determine the best date to use\r\n          let dateToUse;\r\n          if (job.created_at) {\r\n            // Prefer created_at if available\r\n            dateToUse = job.created_at;\r\n          } else if (job.start_date) {\r\n            // Otherwise use start_date\r\n            dateToUse = job.start_date;\r\n          } else if (job.posted_date) {\r\n            // Fall back to posted_date\r\n            dateToUse = job.posted_date;\r\n          } else {\r\n            // If no dates are available, use current date\r\n            dateToUse = new Date().toISOString();\r\n          }\r\n\r\n          // Compose salary display string\r\n          let salaryDisplay = '';\r\n          if (job.min_salary && job.max_salary) {\r\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n          } else if (job.min_salary) {\r\n            salaryDisplay = `${job.min_salary}`;\r\n          } else if (job.max_salary) {\r\n            salaryDisplay = `${job.max_salary}`;\r\n          } else {\r\n            salaryDisplay = '';\r\n          }\r\n            return {\r\n            id: job.job_id,\r\n            hot: job.hot || false, // Use actual hot value from database\r\n            company: job.company_name,\r\n            position: job.job_title,\r\n            location: job.main_topics,\r\n            workTime: job.job_type,\r\n            experience_level: job.experience_level,\r\n            cv_email: job.send_cv_email,\r\n            salary: salaryDisplay,\r\n            status: 'Active', // Default since it doesn't exist in DB\r\n            datePosted: dateToUse,\r\n            rawDatePosted: job.start_date || job.posted_date || job.created_at, // Store raw date for debugging\r\n            views: job.view_count || 0, // Use actual view count if available\r\n            end_date: job.end_date // Add end_date for auto-delete functionality\r\n          };\r\n        });\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching jobs:\", err);\r\n        setError(\"Failed to load jobs from server\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchJobs();\r\n  }, []);\r\n\r\n  // Auto-delete expired jobs (2 weeks after end date)\r\n  useEffect(() => {\r\n    const checkAndDeleteExpiredJobs = async () => {\r\n      try {\r\n        const currentDate = new Date();\r\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\r\n        \r\n        // Get all jobs to check for expired ones\r\n        const response = await ApiService.jobs.getAll();\r\n        const allJobs = response.data;\r\n        \r\n        const expiredJobs = allJobs.filter(job => {\r\n          if (!job.end_date) return false; // Skip jobs without end date\r\n          \r\n          const endDate = new Date(job.end_date);\r\n          const timeDifference = currentDate.getTime() - endDate.getTime();\r\n          \r\n          // Check if job is expired by more than 2 weeks\r\n          return timeDifference > twoWeeksInMs;\r\n        });\r\n        \r\n        // Delete expired jobs\r\n        for (const expiredJob of expiredJobs) {\r\n          try {\r\n            await ApiService.jobs.delete(expiredJob.job_id);\r\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\r\n          } catch (deleteErr) {\r\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\r\n          }\r\n        }\r\n        \r\n        // If any jobs were deleted, refresh the jobs list\r\n        if (expiredJobs.length > 0) {\r\n          // Refresh jobs list by fetching again\r\n          const updatedResponse = await ApiService.jobs.getAll();\r\n          const formattedJobs = updatedResponse.data.map(job => {\r\n            let dateToUse;\r\n            if (job.created_at) {\r\n              dateToUse = job.created_at;\r\n            } else if (job.start_date) {\r\n              dateToUse = job.start_date;\r\n            } else if (job.posted_date) {\r\n              dateToUse = job.posted_date;\r\n            } else {\r\n              dateToUse = new Date().toISOString();\r\n            }\r\n\r\n            let salaryDisplay = '';\r\n            if (job.min_salary && job.max_salary) {\r\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n            } else if (job.min_salary) {\r\n              salaryDisplay = `${job.min_salary}`;\r\n            } else if (job.max_salary) {\r\n              salaryDisplay = `${job.max_salary}`;\r\n            } else {\r\n              salaryDisplay = '';\r\n            }\r\n            \r\n            return {\r\n              id: job.job_id,\r\n              hot: job.hot || false,\r\n              company: job.company_name,\r\n              position: job.job_title,\r\n              location: job.main_topics,\r\n              workTime: job.job_type,\r\n              experience_level: job.experience_level,\r\n              cv_email: job.send_cv_email,\r\n              salary: salaryDisplay,\r\n              status: 'Active',\r\n              datePosted: dateToUse,\r\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n              views: job.view_count || 0,\r\n              end_date: job.end_date\r\n            };\r\n          });\r\n          \r\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n          setJobs(sortedJobs);\r\n        }\r\n        \r\n      } catch (err) {\r\n        console.error(\"Error checking for expired jobs:\", err);\r\n        // Don't set error state here to avoid disrupting normal operation\r\n      }\r\n    };\r\n\r\n    // Run the check immediately when component mounts\r\n    checkAndDeleteExpiredJobs();\r\n    \r\n    // Set up interval to check every hour (3600000 ms)\r\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\r\n    \r\n    // Cleanup interval on component unmount\r\n    return () => clearInterval(intervalId);\r\n  }, []);\r\n\r\n  // Fetch companies when modal is opened\r\n  useEffect(() => {\r\n    if (isModalOpen) {\r\n      console.log(\"Modal opened, fetching companies...\");\r\n      fetchCompanies();\r\n    }\r\n  }, [isModalOpen]);\r\n\r\n  const fetchCompanies = async () => {\r\n    try {\r\n      console.log(\"Fetching companies from API...\");\r\n      const response = await ApiService.companies.getAll();\r\n      const companiesData = response.data.data || [];\r\n      console.log(\"Companies fetched:\", companiesData);\r\n      setCompanies(companiesData);\r\n      \r\n      // If we're editing a job and have a company name, select the matching company\r\n      if (selectedJob && newJobForm.company_name) {\r\n        const matchingCompany = companiesData.find(\r\n          company => company.company_name === newJobForm.company_name\r\n        );\r\n        \r\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\r\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\r\n          setLogoPreview(matchingCompany.company_logo_url);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching companies:', err);\r\n      // Don't set error state here to avoid disrupting the job form\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  const handleEditJob = async (job) => {\r\n    try {\r\n      setLoading(true);\r\n      // Fetch full job details from backend\r\n      const response = await ApiService.jobs.getById(job.id);\r\n      const fullJobDetails = response.data;\r\n      \r\n      console.log(\"Job details from API:\", fullJobDetails);\r\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\r\n      \r\n      setSelectedJob(job);\r\n      // Initialize form with job data - only using fields from the actual database schema\r\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\r\n      const formatDate = (dateString) => {\r\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\r\n        \r\n        try {\r\n          // Parse the date and ensure it's valid\r\n          const date = new Date(dateString);\r\n          if (isNaN(date.getTime())) {\r\n            console.warn(\"Invalid date detected:\", dateString);\r\n            return getTodayFormatted(); // Default to today for invalid dates\r\n          }\r\n          \r\n          // Format as YYYY-MM-DD for date input\r\n          const year = date.getFullYear();\r\n          const month = String(date.getMonth() + 1).padStart(2, '0');\r\n          const day = String(date.getDate()).padStart(2, '0');\r\n          return `${year}-${month}-${day}`;\r\n        } catch (err) {\r\n          console.error(\"Error formatting date:\", err);\r\n          return getTodayFormatted();\r\n        }\r\n      };\r\n      \r\n      // Set image previews\r\n      if (fullJobDetails.job_post_image) {\r\n        setJobImagePreview(fullJobDetails.job_post_image);\r\n      }\r\n      \r\n      if (fullJobDetails.job_post_thumbnail) {\r\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\r\n      }\r\n      \r\n      // Handle company logo preview\r\n      let companyLogoUrl = null;\r\n      if (fullJobDetails.company_logo) {\r\n        setLogoPreview(fullJobDetails.company_logo);\r\n        companyLogoUrl = fullJobDetails.company_logo;\r\n      } else if (fullJobDetails.company_logo_url) {\r\n        setLogoPreview(fullJobDetails.company_logo_url);\r\n        companyLogoUrl = fullJobDetails.company_logo_url;\r\n      } else {\r\n        // Fetch companies to find the logo\r\n        try {\r\n          const companiesResponse = await ApiService.companies.getAll();\r\n          const companiesData = companiesResponse.data.data || [];\r\n          \r\n          const matchingCompany = companiesData.find(\r\n            company => company.company_name === fullJobDetails.company_name\r\n          );\r\n          \r\n          if (matchingCompany && matchingCompany.company_logo_url) {\r\n            setLogoPreview(matchingCompany.company_logo_url);\r\n            companyLogoUrl = matchingCompany.company_logo_url;\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error finding company logo:\", err);\r\n        }\r\n      }\r\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\r\n      let expLevel = fullJobDetails.experience_level;\r\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\r\n        expLevel = \"No Experience Required\";\r\n      }\r\n      setNewJobForm({\r\n        job_title: fullJobDetails.job_title,\r\n        company_name: fullJobDetails.company_name,\r\n        year: fullJobDetails.year || new Date().getFullYear(),\r\n        start_date: formatDate(fullJobDetails.start_date),\r\n        end_date: formatDate(fullJobDetails.end_date),\r\n        job_type: fullJobDetails.job_type,\r\n        experience_level: expLevel,\r\n        min_salary: fullJobDetails.min_salary || '',\r\n        max_salary: fullJobDetails.max_salary || '',\r\n        send_cv_email: fullJobDetails.send_cv_email || '',\r\n        main_topics: fullJobDetails.main_topics,\r\n        sub_topics: (() => {\r\n          try {\r\n            // Try to parse sub_topics as JSON if it's a string\r\n            if (!fullJobDetails.sub_topics) return [];\r\n            \r\n            if (typeof fullJobDetails.sub_topics === 'string') {\r\n              // Check if it starts with [ which would indicate a likely JSON array\r\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\r\n                return JSON.parse(fullJobDetails.sub_topics);\r\n              } else {\r\n                // If it's not a JSON array, treat it as a single item\r\n                return [fullJobDetails.sub_topics];\r\n              }\r\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\r\n              return fullJobDetails.sub_topics;\r\n            } else {\r\n              return [];\r\n            }\r\n          } catch (err) {\r\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\r\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\r\n          }\r\n        })(),\r\n        job_description: fullJobDetails.job_description || '',\r\n        job_post_image: fullJobDetails.job_post_image,\r\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\r\n        company_logo: fullJobDetails.company_logo,\r\n        company_logo_url: companyLogoUrl, // Store the company logo URL\r\n        // Use default values for UI-only fields that don't exist in the database\r\n        status: 'Active',\r\n        hot: fullJobDetails.hot || false // Use actual hot value from database\r\n      });\r\n      \r\n      setIsModalOpen(true);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error fetching job details:\", err);\r\n      setError(`Failed to load job details for ${job.position}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteJob = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      await ApiService.jobs.delete(jobId);\r\n      setJobs(jobs.filter(job => job.id !== jobId));\r\n      setError(null);\r\n      setShowDeleteConfirm(false);\r\n      setJobToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting job:\", err);\r\n      setError(\"Failed to delete job\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to show delete confirmation\r\n  const confirmDelete = (job) => {\r\n    setJobToDelete(job);\r\n    setShowDeleteConfirm(true);\r\n  };\r\n\r\n  // Function to cancel delete\r\n  const cancelDelete = () => {\r\n    setShowDeleteConfirm(false);\r\n    setJobToDelete(null);\r\n  };\r\n\r\n  const handleToggleStatus = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      const jobToUpdate = jobs.find(job => job.id === jobId);\r\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\r\n      \r\n      // Since we don't have a status column in the database,\r\n      // we'll just update the local state without sending to the backend\r\n      // In a real application, you would add the status column to the database\r\n      \r\n      // Update local state only\r\n      setJobs(jobs.map(job => \r\n        job.id === jobId \r\n          ? {...job, status: newStatus} \r\n          : job\r\n      ));\r\n      \r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error updating job status:\", err);\r\n      setError(\"Failed to update job status\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n    const openNewJobModal = () => {\r\n    setIsModalOpen(true);\r\n    setSelectedJob(null);\r\n  };  \r\n    const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setSelectedJob(null);\r\n    // Reset form\r\n    setNewJobForm({\r\n      job_title: '',\r\n      year: new Date().getFullYear(),\r\n      start_date: getTodayFormatted(),\r\n      end_date: '',\r\n      send_cv_email: '',\r\n      main_topics: 'Government Jobs', // Ensure we always have a valid value\r\n      sub_topics: [],\r\n      job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n      experience_level: 'Entry Level',\r\n      min_salary: '',\r\n      max_salary: '',\r\n      job_description: '',\r\n      job_post_image: null,\r\n      job_post_thumbnail: null,\r\n      company_name: '',\r\n      company_logo: null,\r\n      // UI-only fields (not in database)\r\n      status: 'Active',\r\n      hot: false\r\n    });\r\n    // Reset previews\r\n    setJobImagePreview(null);\r\n    setThumbnailPreview(null);\r\n    setLogoPreview(null);\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Basic client-side text sanitization\r\n  const sanitizeInputText = (text) => {\r\n    if (!text) return '';\r\n    // Modified to preserve Unicode characters in the job title\r\n    // Only remove control characters and potentially problematic chars\r\n    return text\r\n      .replace(/[--]/g, '') // Remove control characters\r\n      .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\r\n      .trim();\r\n  };\r\n  \r\n  const handleFormChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    \r\n    // Apply sanitization to text fields that might contain problematic characters\r\n    if (name === 'job_title' || name === 'job_description') {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\r\n      });\r\n    } else {\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    });\r\n    }\r\n  };\r\n  \r\n  const handleImageUpload = (e, imageType) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        if (imageType === 'job_post_image') {\r\n          setJobImagePreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_image: file });\r\n        } else if (imageType === 'job_post_thumbnail') {\r\n          setThumbnailPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_thumbnail: file });\r\n        } else if (imageType === 'company_logo') {\r\n          setLogoPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, company_logo: file });\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSelectCompany = (company) => {\r\n    console.log(\"Company selected:\", company);\r\n    \r\n    // Validate company has a name\r\n    if (!company || !company.company_name) {\r\n      console.error(\"Invalid company selected\");\r\n      return;\r\n    }\r\n    \r\n    // Set company name in the form\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      company_name: company.company_name,\r\n      company_logo: null, // Reset the file input since we're using an existing logo\r\n      company_logo_url: company.company_logo_url // Store the logo URL\r\n    });\r\n    \r\n    // Set logo preview\r\n    if (company.company_logo_url) {\r\n      setLogoPreview(company.company_logo_url);\r\n    } else {\r\n      setLogoPreview(null);\r\n    }\r\n    \r\n    // Close the dropdown\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Add function to remove logo\r\n  const handleRemoveLogo = () => {\r\n    setLogoPreview(null);\r\n    // If there was a logo file in the form, clear it\r\n    if (newJobForm.company_logo) {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        company_logo: null\r\n      });\r\n    }\r\n  };\r\n  \r\n  const handleFormSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Debug the form state\r\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\r\n    console.log(\"Original form state:\", newJobForm);\r\n    console.log(\"Selected job:\", selectedJob);\r\n    \r\n    // Client-side validation for required fields\r\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\r\n      setError(\"Job title is required\");\r\n      return;\r\n    }\r\n    \r\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\r\n      setError(\"Company name is required\");\r\n      return;\r\n    }\r\n\r\n    // Validate email/URL field if provided\r\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\r\n      \r\n      const value = newJobForm.send_cv_email.trim();\r\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\r\n        setError(\"Please enter a valid email address or website URL\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\r\n    let experienceLevelToSend = newJobForm.experience_level;\r\n    if (!experienceLevelToSend) {\r\n      experienceLevelToSend = \"No Experience Required\";\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Create a sanitized copy of the form data to send to the server\r\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\r\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\r\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\r\n      \r\n      const sanitizedForm = {\r\n        ...newJobForm,\r\n        job_title: sanitizedTitle,\r\n        job_description: sanitizeInputText(newJobForm.job_description),\r\n        experience_level: experienceLevelToSend,\r\n        // For new jobs, set today's date as the start_date if not provided\r\n        start_date: selectedJob ? newJobForm.start_date : (newJobForm.start_date || getTodayFormatted())\r\n      };\r\n      \r\n      console.log(\"Sanitized form data:\", sanitizedForm);\r\n      \r\n      // Prepare form data for API - create a completely fresh FormData object\r\n      const formData = new FormData();\r\n        // Add only fields that exist in the database schema, ensuring each field is only added once\r\n      const dbFields = [\r\n        'job_title', 'company_name', 'year', 'start_date', 'end_date',\r\n        'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level',\r\n        'min_salary', 'max_salary', 'job_description', 'hot'\r\n      ];\r\n      \r\n      // Add text fields one by one to avoid duplicates\r\n      dbFields.forEach(key => {\r\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\r\n          // Special handling for sub_topics\r\n          if (key === 'sub_topics') {\r\n            try {\r\n              if (Array.isArray(sanitizedForm[key])) {\r\n                // Ensure we're sending a clean array without unexpected characters\r\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\r\n                formData.append(key, JSON.stringify(cleanSubTopics));\r\n              } else {\r\n                // If it's a string or something else, convert to array\r\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\r\n              }\r\n            } catch (err) {\r\n              console.error(\"Error formatting sub_topics:\", err);\r\n              formData.append(key, '[]'); // Fallback to empty array\r\n            }\r\n          } else {\r\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\r\n            formData.append(key, sanitizedForm[key]);\r\n          }\r\n        }\r\n      });\r\n      \r\n      // Add file fields if they exist\r\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\r\n        formData.append('job_post_image', sanitizedForm.job_post_image);\r\n      }\r\n      \r\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\r\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\r\n      }      \r\n      \r\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\r\n        formData.append('company_logo', sanitizedForm.company_logo);\r\n      }\r\n      \r\n      // If we have a logo preview from an existing company but no file, add the URL\r\n      if (logoPreview && !sanitizedForm.company_logo) {\r\n        formData.append('existing_company_logo_url', logoPreview);\r\n      }\r\n      \r\n      // Add company_logo_url if it exists\r\n      if (sanitizedForm.company_logo_url) {\r\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\r\n      }\r\n      \r\n      // Debug: Log what's being sent in the FormData\r\n      console.log(\"FormData contents:\");\r\n      for (let pair of formData.entries()) {\r\n        console.log(pair[0] + ': ' + pair[1]);\r\n      }\r\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\r\n      \r\n      // Prepare form data for submission\r\n      if (selectedJob) {\r\n        // Update existing job\r\n        try {\r\n          await ApiService.jobs.update(selectedJob.id, formData);\r\n          // Successfully updated job\r\n        } catch (apiError) {\r\n          // Handle update error silently\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      } else {\r\n        // Create new job\r\n        try {\r\n          await ApiService.jobs.create(formData);\r\n          // Successfully created job\r\n        } catch (apiError) {\r\n          // Log detailed error information\r\n          console.error(\"API Error Details:\", {\r\n            message: apiError.message,\r\n            status: apiError.response?.status,\r\n            statusText: apiError.response?.statusText,\r\n            data: apiError.response?.data\r\n          });\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      }\r\n      \r\n      setError(null);\r\n      // Close modal\r\n      closeModal();\r\n    } catch (err) {\r\n      console.error(\"Error saving job:\", err);\r\n      // Show more detailed error message if available\r\n      const errorMessage = err.response?.data?.message || \r\n                          err.response?.data?.error || \r\n                          (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const filteredJobs = jobs.filter(job => {\r\n    const matchesSearch = (job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase())) || \r\n                          (job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase()));\r\n    \r\n    return matchesSearch;\r\n  });\r\n\r\n  // Add this useEffect to handle closing the dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      const dropdownElement = document.querySelector('.company-select-dropdown');\r\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\r\n        setShowCompanyDropdown(false);\r\n      }\r\n    };\r\n\r\n    if (showCompanyDropdown) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n      return () => {\r\n        document.removeEventListener('mousedown', handleClickOutside);\r\n      };\r\n    }\r\n  }, [showCompanyDropdown]);\r\n\r\n  return (\r\n    <div className=\"jobs-admin-container\">\r\n      {/* Delete Confirmation Dialog */}\r\n      {showDeleteConfirm && (\r\n        <div className=\"delete-confirm-overlay\">\r\n          <div className=\"delete-confirm-dialog\">\r\n            <div className=\"delete-confirm-header\">\r\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"delete-icon\" />\r\n              <h3>Confirm Deletion</h3>\r\n            </div>\r\n            <div className=\"delete-confirm-content\">\r\n              <p>Are you sure you want to delete this job posting?</p>\r\n              <p><strong>{jobToDelete?.position}</strong></p>\r\n              <p>This action cannot be undone.</p>\r\n            </div>\r\n            <div className=\"delete-confirm-actions\">\r\n              <button \r\n                className=\"cancel-delete-btn\" \r\n                onClick={cancelDelete}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button \r\n                className=\"confirm-delete-btn\" \r\n                onClick={() => handleDeleteJob(jobToDelete.id)}\r\n              >\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"jobs-header\">\r\n        <h1 className=\"jobs-title\">Job Listings</h1>\r\n        <div className=\"job-header-actions\">\r\n          <button className=\"refresh-button\" onClick={() => {\r\n            setLoading(true);\r\n            const fetchJobs = async () => {\r\n              try {\r\n                const response = await ApiService.jobs.getAll();\r\n                \r\n                // Transform backend data to match the format used in frontend\r\n                const formattedJobs = response.data.map(job => {\r\n                  // Determine the best date to use\r\n                  let dateToUse;\r\n                  if (job.created_at) {\r\n                    dateToUse = job.created_at;\r\n                  } else if (job.start_date) {\r\n                    dateToUse = job.start_date;\r\n                  } else if (job.posted_date) {\r\n                    dateToUse = job.posted_date;\r\n                  } else {\r\n                    dateToUse = new Date().toISOString();\r\n                  }\r\n\r\n                  // Compose salary display string\r\n                  let salaryDisplay = '';\r\n                  if (job.min_salary && job.max_salary) {\r\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n                  } else if (job.min_salary) {\r\n                    salaryDisplay = `${job.min_salary}`;\r\n                  } else if (job.max_salary) {\r\n                    salaryDisplay = `${job.max_salary}`;\r\n                  } else {\r\n                    salaryDisplay = '';\r\n                  }\r\n                    return {\r\n                    id: job.job_id,\r\n                    hot: job.hot || false,\r\n                    company: job.company_name,\r\n                    position: job.job_title,\r\n                    location: job.main_topics,\r\n                    workTime: job.job_type,\r\n                    experience_level: job.experience_level,\r\n                    cv_email: job.send_cv_email,\r\n                    salary: salaryDisplay,\r\n                    status: 'Active',\r\n                    datePosted: dateToUse,\r\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n                    views: job.view_count || 0,\r\n                    end_date: job.end_date\r\n                  };\r\n                });\r\n                \r\n                // Sort jobs by datePosted descending (newest first)\r\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n                setJobs(sortedJobs);\r\n                setError(null);\r\n              } catch (err) {\r\n                console.error(\"Error fetching jobs:\", err);\r\n                setError(\"Failed to load jobs from server\");\r\n              } finally {\r\n                setLoading(false);\r\n              }\r\n            };\r\n\r\n            fetchJobs();\r\n          }} disabled={loading}>\r\n            <FontAwesomeIcon icon={faSync} />\r\n            <span>Refresh</span>\r\n          </button>\r\n          <button className=\"add-job-button\" onClick={openNewJobModal} disabled={loading}>\r\n            <FontAwesomeIcon icon={faPlus} />\r\n            <span>Add New Job</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"error-message\">\r\n          <FontAwesomeIcon icon={faExclamationCircle} />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"filters-container\">\r\n        <div className=\"search-container\">\r\n          <div className=\"search-input-wrapper\">\r\n            <FontAwesomeIcon icon={faSearch} className=\"search-icon\" />          <input\r\n            type=\"text\"\r\n            placeholder=\"Search by company or position...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n            className=\"search-input\"\r\n            style={{ paddingLeft: '40px' }}\r\n          />\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div className=\"table-container\">\r\n        {loading ? (\r\n          <div className=\"loading-container\">\r\n            <FontAwesomeIcon icon={faSpinner} spin size=\"2x\" />\r\n            <span>Loading jobs...</span>\r\n          </div>\r\n        ) : (\r\n          <>\r\n          <table className=\"jobs-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Job</th>\r\n                <th>Type</th>\r\n                <th>Experience Level</th>\r\n                <th>Email / Web URL</th>\r\n                <th>Salary</th>\r\n                <th>Expire Date</th>\r\n                <th>Posted Date</th>\r\n                <th>Views</th>\r\n                <th>Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {filteredJobs.length > 0 ? (\r\n                filteredJobs.map(job => (\r\n                  <tr key={job.id}>\r\n                    <td>\r\n                      {job.hot && (\r\n                        <span className=\"hot-badge\">\r\n                          <FontAwesomeIcon icon={faFire} style={{ marginRight: '4px' }} />\r\n                          URGENT\r\n                        </span>\r\n                      )}\r\n<div className=\"job-position\" title={job.position}>\r\n  {job.position && job.position.length > 35\r\n    ? job.position.slice(0, 32) + '...'\r\n    : job.position}\r\n</div>\r\n                      <div className=\"job-company\">{job.company}</div>\r\n                    </td>\r\n                    <td>{job.workTime}</td>\r\n                    <td>\r\n                      <div className=\"experience-level\" title={job.experience_level}>\r\n                        {job.experience_level || 'Not specified'}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div className=\"cv-email\" title={job.cv_email}>\r\n                        {job.cv_email ? (\r\n                          <a href={`mailto:${job.cv_email}`} className=\"email-link\">\r\n                            {job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email}\r\n                          </a>\r\n                        ) : (\r\n                          'Not provided'\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                    <td>{job.salary}</td>\r\n                    <td>{formatDisplayDate(job.end_date)}</td>\r\n                      <td>{formatDisplayDate(job.datePosted)}</td>\r\n                    <td>\r\n                      <div className=\"views-container\">\r\n                        <FontAwesomeIcon icon={faEye} className=\"views-icon\" />\r\n                        {job.views}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div style={{ display: 'flex' }}>\r\n                        <button \r\n                          className=\"action-button edit-button\" \r\n                          onClick={() => handleEditJob(job)}\r\n                          title=\"Edit job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faEdit} />\r\n                        </button>\r\n                        {/* Removed Inactive/Active toggle button */}\r\n                        <button \r\n                          className=\"action-button delete-button\" \r\n                          onClick={() => confirmDelete(job)}\r\n                          title=\"Delete job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faTrash} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td colSpan=\"9\" className=\"no-jobs-message\">\r\n                    {searchTerm ? \r\n                      \"No jobs match your search criteria\" : \r\n                      \"No jobs available\"\r\n                    }\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* New Job Modal */}\r\n      {isModalOpen && (\r\n        <div className=\"modal-overlay\" onClick={(e) => {\r\n          // Close modal when clicking outside\r\n          if (e.target.className === 'modal-overlay') {\r\n            closeModal();\r\n          }\r\n        }}>\r\n          <div className=\"job-modal\">\r\n            <div className=\"modal-header\">\r\n              <h2>{selectedJob ? 'Edit Job' : 'Add New Job'}</h2>\r\n              <button className=\"close-button\" onClick={closeModal}>\r\n                <FontAwesomeIcon icon={faTimes} />\r\n              </button>\r\n            </div>\r\n            <div className=\"modal-content\">\r\n              <form onSubmit={handleFormSubmit}>\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_title\">Job Title <span style={{ color: 'red' }}>*</span></label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"job_title\"\r\n                      name=\"job_title\"\r\n                      value={newJobForm.job_title}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter job title\"\r\n                    />\r\n                    </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Company <span style={{ color: 'red' }}>*</span></label>\r\n                    <div className=\"company-selector\">\r\n                      <div className=\"company-select-dropdown\">\r\n                        <button \r\n                          type=\"button\"\r\n                          className=\"company-select-button\"\r\n                          onClick={() => setShowCompanyDropdown(!showCompanyDropdown)}\r\n                        >\r\n                          {newJobForm.company_name ? newJobForm.company_name : 'Select a company'}\r\n                          <FontAwesomeIcon icon={faChevronDown} />\r\n                        </button>\r\n                        \r\n                        {showCompanyDropdown && (\r\n                          <div className=\"company-list-dropdown\">\r\n                            {companies && companies.length > 0 ? (\r\n                              companies.map(company => (\r\n                                <div \r\n                                  key={company.company_id}\r\n                                  className=\"company-item\"\r\n                                  onClick={() => {\r\n                                    handleSelectCompany(company);\r\n                                  }}\r\n                                >\r\n                                  <div className=\"company-item-logo\">\r\n                                    {company.company_logo_url ? (\r\n                                      <img src={company.company_logo_url} alt={company.company_name} />\r\n                                    ) : (\r\n                                      <FontAwesomeIcon icon={faBuilding} />\r\n                                    )}\r\n                                  </div>\r\n                                  <span>{company.company_name}</span>\r\n                                </div>\r\n                              ))\r\n                            ) : (\r\n                              <div className=\"no-companies\">No companies available</div>\r\n                            )}\r\n                            <div className=\"manual-company-entry\">\r\n                              <label>Or enter company name manually:</label>\r\n                              <input\r\n                                type=\"text\"\r\n                                placeholder=\"Enter company name\"\r\n                                value={newJobForm.company_name || ''}\r\n                                onChange={(e) => {\r\n                                  setNewJobForm({\r\n                                    ...newJobForm,\r\n                                    company_name: e.target.value\r\n                                  });\r\n                                }}\r\n                              />\r\n                              <button\r\n                                type=\"button\"\r\n                                className=\"apply-company-btn\"\r\n                                onClick={() => setShowCompanyDropdown(false)}\r\n                              >\r\n                                Apply\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      \r\n                      {/* Display logo preview if available */}\r\n                      {logoPreview && (\r\n                        <div className=\"logo-preview-container\">\r\n                          <img src={logoPreview} alt=\"Company logo\" />\r\n                          <button \r\n                            type=\"button\" \r\n                            className=\"remove-logo-button\"\r\n                            onClick={handleRemoveLogo}\r\n                          >\r\n                            <FontAwesomeIcon icon={faTimes} />\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                      \r\n                      {/* Allow uploading a new logo if no existing one is selected */}\r\n                      {!logoPreview && (\r\n                        <div>\r\n                          <input\r\n                            type=\"file\"\r\n                            id=\"company_logo\"\r\n                            name=\"company_logo\"\r\n                            onChange={(e) => handleImageUpload(e, 'company_logo')}\r\n                            accept=\"image/*\"\r\n                            style={{ display: 'none' }}\r\n                          />\r\n                          <button\r\n                            type=\"button\"\r\n                            className=\"file-upload-label\"\r\n                            onClick={() => document.getElementById('company_logo').click()}\r\n                          >\r\n                            <FontAwesomeIcon icon={faImage} />\r\n                            Upload Company Logo\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"year\">Year</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"year\"\r\n                      name=\"year\"\r\n                      value={newJobForm.year}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      min=\"2000\"\r\n                      max=\"2100\"\r\n                    />\r\n                  </div>                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_type\">Job Type</label>\r\n                    <select\r\n                      id=\"job_type\"\r\n                      name=\"job_type\"\r\n                      value={newJobForm.job_type}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Full Time Jobs\">Full Time Jobs</option>\r\n                      <option value=\"Part Time Jobs\">Part Time Jobs</option>\r\n                      <option value=\"Remote Jobs\">Remote Jobs</option>\r\n                      <option value=\"Freelance\">Freelance</option>\r\n                      <option value=\"Temporary\">Temporary</option>\r\n                    </select>\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"experience_level\">Experience Level</label>\r\n                    <select\r\n                      id=\"experience_level\"\r\n                      name=\"experience_level\"\r\n                      value={newJobForm.experience_level}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      {EXPERIENCE_OPTIONS.map(option => (\r\n                        <option key={option} value={option}>{option}</option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"start_date\">Start Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"start_date\"\r\n                      name=\"start_date\"\r\n                      value={newJobForm.start_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"end_date\">End Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"end_date\"\r\n                      name=\"end_date\"\r\n                      value={newJobForm.end_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"send_cv_email\">Email/WebURL</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"send_cv_email\"\r\n                      name=\"send_cv_email\"\r\n                      value={newJobForm.send_cv_email}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Enter email or website URL for CV submissions (Optional)\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"min_salary\">Minimum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"min_salary\"\r\n                      name=\"min_salary\"\r\n                      value={newJobForm.min_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Minimum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"max_salary\">Maximum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"max_salary\"\r\n                      name=\"max_salary\"\r\n                      value={newJobForm.max_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Maximum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Mark as URGENT Job - Moved to middle */}\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group checkbox-group\" style={{textAlign: 'left'}}>\r\n                    <label className=\"checkbox-container\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"hot\"\r\n                        checked={newJobForm.hot}\r\n                        onChange={handleFormChange}\r\n                      />\r\n                      <span className=\"checkbox-text\">\r\n                        <FontAwesomeIcon icon={faFire} className=\"hot-icon\" />\r\n                        Mark as URGENT Job\r\n                      </span>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"main_topics\">Main Topic/Category</label>\r\n                    <select\r\n                      id=\"main_topics\"\r\n                      name=\"main_topics\"\r\n                      value={newJobForm.main_topics}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Government Jobs\">Government Jobs</option>\r\n                      <option value=\"Private Jobs\">Private Jobs</option>\r\n                      <option value=\"Foreign Jobs\">Foreign Jobs</option>\r\n                      <option value=\"Internships\">Internships</option>\r\n                    </select>\r\n                  </div>\r\n                </div>                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n<label>\r\n  <span>Sub Topics</span>\r\n  <span className=\"small-text\">{newJobForm.sub_topics.length} selected</span>\r\n  <button\r\n    type=\"button\"\r\n    style={{ marginLeft: '16px', padding: '2px 8px', fontSize: '0.9em' }}\r\n    onClick={() => setNewJobForm(prev => ({ ...prev, sub_topics: [] }))}\r\n  >\r\n    Clear All\r\n  </button>\r\n</label>\r\n<div className=\"subtopics-management-info\">\r\n  <small style={{ color: '#6c757d', fontStyle: 'italic' }}>\r\n    💡 Tip: You can manage available sub-topics from the \"Sub Topics\" section in the sidebar\r\n  </small>\r\n</div>\r\n<div className=\"subtopics-checkbox-container\">\r\n  {availableSubTopics.map((topic, index) => (\r\n                        <div \r\n                          key={index} \r\n                          className={`subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`}\r\n                        >\r\n<input\r\n  type=\"checkbox\"\r\n  id={`subtopic-${index}`}\r\n  checked={newJobForm.sub_topics.includes(topic)}\r\n  onChange={e => {\r\n    const checked = e.target.checked;\r\n    setNewJobForm(prev => {\r\n      const set = new Set(prev.sub_topics);\r\n      if (checked) {\r\n        set.add(topic);\r\n      } else {\r\n        set.delete(topic);\r\n      }\r\n      return {\r\n        ...prev,\r\n        sub_topics: Array.from(set)\r\n      };\r\n    });\r\n  }}\r\n                          />\r\n                          <label htmlFor={`subtopic-${index}`}>\r\n                            {topic}\r\n                          </label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n                    <label htmlFor=\"job_description\">Job Description</label>\r\n                    <textarea\r\n                      id=\"job_description\"\r\n                      name=\"job_description\"\r\n                      rows=\"5\"\r\n                      value={newJobForm.job_description}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter detailed job description\"\r\n                    ></textarea>\r\n                    <small className=\"form-help-text\">\r\n                      Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Image</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_image\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_image')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_image\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Image</span>\r\n                      </label>\r\n                    </div>\r\n                    {jobImagePreview && (\r\n                      <div className=\"image-preview\">\r\n                        <img src={jobImagePreview} alt=\"Job Post\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Thumbnail</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_thumbnail\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_thumbnail')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_thumbnail\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Thumbnail</span>\r\n                      </label>\r\n                    </div>\r\n                    {thumbnailPreview && (\r\n                      <div className=\"thumbnail-preview\">\r\n                        <img src={thumbnailPreview} alt=\"Thumbnail\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n                <div className=\"modal-footer\">\r\n                  <button type=\"button\" className=\"cancel-button\" onClick={closeModal}>\r\n                    Cancel\r\n                  </button>\r\n                  <button type=\"submit\" className=\"submit-button\">\r\n                    {selectedJob ? 'Update Job' : 'Create Job'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobsAdmin;\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,mBAAmB,EACnBC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,SAAS,EACTC,aAAa,EACbC,qBAAqB,EACrBC,MAAM,QACD,mCAAmC;AAC1C,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AACnC,OAAO,oCAAoC;AAC3C,OAAOC,UAAU,MAAM,2BAA2B;AAClD;AACA,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,kBAAkB,GAAG,CACzB,wBAAwB,EACxB,aAAa,EACb,WAAW,EACX,cAAc,EACd,SAAS,EACT,WAAW,CACZ;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA;EACA,MAAM+C,iBAAiB,GAAGA,CAAA,KAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEtE;EACA,MAAMC,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,GAAG,EAAE,OAAO,GAAG;IACjD,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;MACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOH,UAAU,CAAC,CAAC;MAC9C,OAAOC,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;MACpD,OAAOR,UAAU;IACnB;EACF,CAAC;EACC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC;IAC7CgE,SAAS,EAAE,EAAE;IACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;IAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;IAC/BoB,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,iBAAiB;IAAE;IAChCC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,gBAAgB;IAAE;IAC5BC,gBAAgB,EAAE,aAAa;IAAE;IACjCC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClB;IACAC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACpD;EACA,MAAM,CAACwF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM0F,gBAAgB,GAAG,CACvB,oCAAoC,EACpC,8BAA8B,EAC9B,6BAA6B,EAC7B,6BAA6B,EAC7B,+BAA+B,EAC/B,8CAA8C,EAC9C,WAAW,EACX,mBAAmB,EACnB,eAAe,EACf,4BAA4B,EAC5B,UAAU,EACV,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,yBAAyB,CAC1B;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI;MACF,MAAMC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAC7D,IAAIF,cAAc,EAAE;QAClB,MAAMG,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAClD;QACA,MAAMM,mBAAmB,GAAGH,eAAe,CACxCI,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAC/BC,GAAG,CAACF,KAAK,IAAIA,KAAK,CAACG,IAAI,CAAC;QAC3Bd,qBAAqB,CAACS,mBAAmB,CAAC;MAC5C,CAAC,MAAM;QACL;QACAT,qBAAqB,CAACC,gBAAgB,CAAC;MACzC;IACF,CAAC,CAAC,OAAO9B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,6CAA6C,EAAE6B,GAAG,CAAC;MACjE;MACA6B,qBAAqB,CAACC,gBAAgB,CAAC;IACzC;EACF,CAAC;;EAED;EACAzF,SAAS,CAAC,MAAM;IACd0F,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1F,SAAS,CAAC,MAAM;IACd,MAAMuG,qBAAqB,GAAIC,KAAK,IAAK;MACvC5C,OAAO,CAAC6C,GAAG,CAAC,qDAAqD,CAAC;MAClEf,wBAAwB,CAAC,CAAC;IAC5B,CAAC;;IAED;IACAgB,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,qBAAqB,CAAC;;IAElE;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEL,qBAAqB,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvG,SAAS,CAAC,MAAM;IACd,MAAM6G,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFhF,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMiF,QAAQ,GAAG,MAAM5F,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;;QAE/C;QACA;QACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACZ,GAAG,CAACa,GAAG,IAAI;UAC7C;UACA,IAAIC,SAAS;UACb,IAAID,GAAG,CAACE,UAAU,EAAE;YAClB;YACAD,SAAS,GAAGD,GAAG,CAACE,UAAU;UAC5B,CAAC,MAAM,IAAIF,GAAG,CAACjD,UAAU,EAAE;YACzB;YACAkD,SAAS,GAAGD,GAAG,CAACjD,UAAU;UAC5B,CAAC,MAAM,IAAIiD,GAAG,CAACG,WAAW,EAAE;YAC1B;YACAF,SAAS,GAAGD,GAAG,CAACG,WAAW;UAC7B,CAAC,MAAM;YACL;YACAF,SAAS,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtC;;UAEA;UACA,IAAIsE,aAAa,GAAG,EAAE;UACtB,IAAIJ,GAAG,CAAC1C,UAAU,IAAI0C,GAAG,CAACzC,UAAU,EAAE;YACpC6C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,MAAM0C,GAAG,CAACzC,UAAU,EAAE;UACzD,CAAC,MAAM,IAAIyC,GAAG,CAAC1C,UAAU,EAAE;YACzB8C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,EAAE;UACrC,CAAC,MAAM,IAAI0C,GAAG,CAACzC,UAAU,EAAE;YACzB6C,aAAa,GAAG,GAAGJ,GAAG,CAACzC,UAAU,EAAE;UACrC,CAAC,MAAM;YACL6C,aAAa,GAAG,EAAE;UACpB;UACE,OAAO;YACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;YACdxC,GAAG,EAAEkC,GAAG,CAAClC,GAAG,IAAI,KAAK;YAAE;YACvByC,OAAO,EAAEP,GAAG,CAACrC,YAAY;YACzB6C,QAAQ,EAAER,GAAG,CAACnD,SAAS;YACvB4D,QAAQ,EAAET,GAAG,CAAC9C,WAAW;YACzBwD,QAAQ,EAAEV,GAAG,CAAC5C,QAAQ;YACtBC,gBAAgB,EAAE2C,GAAG,CAAC3C,gBAAgB;YACtCsD,QAAQ,EAAEX,GAAG,CAAC/C,aAAa;YAC3B2D,MAAM,EAAER,aAAa;YACrBvC,MAAM,EAAE,QAAQ;YAAE;YAClBgD,UAAU,EAAEZ,SAAS;YACrBa,aAAa,EAAEd,GAAG,CAACjD,UAAU,IAAIiD,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;YAAE;YACpEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;YAAE;YAC5BhE,QAAQ,EAAEgD,GAAG,CAAChD,QAAQ,CAAC;UACzB,CAAC;QACH,CAAC,CAAC;;QAEF;QACA,MAAMiE,UAAU,GAAG,CAAC,GAAGnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvF,IAAI,CAACuF,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIhF,IAAI,CAACsF,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGpG,OAAO,CAACwG,UAAU,CAAC;QACnBpG,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;QAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgF,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7G,SAAS,CAAC,MAAM;IACd,MAAMuI,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,IAAI;QACF,MAAMC,WAAW,GAAG,IAAIzF,IAAI,CAAC,CAAC;QAC9B,MAAM0F,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;QAE/C;QACA,MAAM3B,QAAQ,GAAG,MAAM5F,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;QAC/C,MAAM2B,OAAO,GAAG5B,QAAQ,CAACG,IAAI;QAE7B,MAAM0B,WAAW,GAAGD,OAAO,CAACxC,MAAM,CAACgB,GAAG,IAAI;UACxC,IAAI,CAACA,GAAG,CAAChD,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;UAEjC,MAAM0E,OAAO,GAAG,IAAI7F,IAAI,CAACmE,GAAG,CAAChD,QAAQ,CAAC;UACtC,MAAM2E,cAAc,GAAGL,WAAW,CAAClF,OAAO,CAAC,CAAC,GAAGsF,OAAO,CAACtF,OAAO,CAAC,CAAC;;UAEhE;UACA,OAAOuF,cAAc,GAAGJ,YAAY;QACtC,CAAC,CAAC;;QAEF;QACA,KAAK,MAAMK,UAAU,IAAIH,WAAW,EAAE;UACpC,IAAI;YACF,MAAMzH,UAAU,CAACQ,IAAI,CAACqH,MAAM,CAACD,UAAU,CAACtB,MAAM,CAAC;YAC/C5D,OAAO,CAAC6C,GAAG,CAAC,6BAA6BqC,UAAU,CAAC/E,SAAS,SAAS+E,UAAU,CAACtB,MAAM,GAAG,CAAC;UAC7F,CAAC,CAAC,OAAOwB,SAAS,EAAE;YAClBpF,OAAO,CAAC9B,KAAK,CAAC,6BAA6BgH,UAAU,CAACtB,MAAM,GAAG,EAAEwB,SAAS,CAAC;UAC7E;QACF;;QAEA;QACA,IAAIL,WAAW,CAACM,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMC,eAAe,GAAG,MAAMhI,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;UACtD,MAAMC,aAAa,GAAGkC,eAAe,CAACjC,IAAI,CAACZ,GAAG,CAACa,GAAG,IAAI;YACpD,IAAIC,SAAS;YACb,IAAID,GAAG,CAACE,UAAU,EAAE;cAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;YAC5B,CAAC,MAAM,IAAIF,GAAG,CAACjD,UAAU,EAAE;cACzBkD,SAAS,GAAGD,GAAG,CAACjD,UAAU;YAC5B,CAAC,MAAM,IAAIiD,GAAG,CAACG,WAAW,EAAE;cAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;YAC7B,CAAC,MAAM;cACLF,SAAS,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtC;YAEA,IAAIsE,aAAa,GAAG,EAAE;YACtB,IAAIJ,GAAG,CAAC1C,UAAU,IAAI0C,GAAG,CAACzC,UAAU,EAAE;cACpC6C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,MAAM0C,GAAG,CAACzC,UAAU,EAAE;YACzD,CAAC,MAAM,IAAIyC,GAAG,CAAC1C,UAAU,EAAE;cACzB8C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,EAAE;YACrC,CAAC,MAAM,IAAI0C,GAAG,CAACzC,UAAU,EAAE;cACzB6C,aAAa,GAAG,GAAGJ,GAAG,CAACzC,UAAU,EAAE;YACrC,CAAC,MAAM;cACL6C,aAAa,GAAG,EAAE;YACpB;YAEA,OAAO;cACLC,EAAE,EAAEL,GAAG,CAACM,MAAM;cACdxC,GAAG,EAAEkC,GAAG,CAAClC,GAAG,IAAI,KAAK;cACrByC,OAAO,EAAEP,GAAG,CAACrC,YAAY;cACzB6C,QAAQ,EAAER,GAAG,CAACnD,SAAS;cACvB4D,QAAQ,EAAET,GAAG,CAAC9C,WAAW;cACzBwD,QAAQ,EAAEV,GAAG,CAAC5C,QAAQ;cACtBC,gBAAgB,EAAE2C,GAAG,CAAC3C,gBAAgB;cACtCsD,QAAQ,EAAEX,GAAG,CAAC/C,aAAa;cAC3B2D,MAAM,EAAER,aAAa;cACrBvC,MAAM,EAAE,QAAQ;cAChBgD,UAAU,EAAEZ,SAAS;cACrBa,aAAa,EAAEd,GAAG,CAACjD,UAAU,IAAIiD,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;cAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;cAC1BhE,QAAQ,EAAEgD,GAAG,CAAChD;YAChB,CAAC;UACH,CAAC,CAAC;UAEF,MAAMiE,UAAU,GAAG,CAAC,GAAGnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvF,IAAI,CAACuF,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIhF,IAAI,CAACsF,CAAC,CAACN,UAAU,CAAC,CAAC;UACrGpG,OAAO,CAACwG,UAAU,CAAC;QACrB;MAEF,CAAC,CAAC,OAAOxE,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;QACtD;MACF;IACF,CAAC;;IAED;IACA4E,yBAAyB,CAAC,CAAC;;IAE3B;IACA,MAAMY,UAAU,GAAGC,WAAW,CAACb,yBAAyB,EAAE,OAAO,CAAC;;IAElE;IACA,OAAO,MAAMc,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnJ,SAAS,CAAC,MAAM;IACd,IAAIoC,WAAW,EAAE;MACfwB,OAAO,CAAC6C,GAAG,CAAC,qCAAqC,CAAC;MAClD6C,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClH,WAAW,CAAC,CAAC;EAEjB,MAAMkH,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF1F,OAAO,CAAC6C,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMK,QAAQ,GAAG,MAAM5F,UAAU,CAACoB,SAAS,CAACyE,MAAM,CAAC,CAAC;MACpD,MAAMwC,aAAa,GAAGzC,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE;MAC9CrD,OAAO,CAAC6C,GAAG,CAAC,oBAAoB,EAAE8C,aAAa,CAAC;MAChDhH,YAAY,CAACgH,aAAa,CAAC;;MAE3B;MACA,IAAIrH,WAAW,IAAI2B,UAAU,CAACgB,YAAY,EAAE;QAC1C,MAAM2E,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxChC,OAAO,IAAIA,OAAO,CAAC5C,YAAY,KAAKhB,UAAU,CAACgB,YACjD,CAAC;QAED,IAAI2E,eAAe,IAAIA,eAAe,CAACE,gBAAgB,IAAI,CAACrE,WAAW,EAAE;UACvEzB,OAAO,CAAC6C,GAAG,CAAC,4CAA4C,EAAE+C,eAAe,CAAC3E,YAAY,CAAC;UACvFS,cAAc,CAACkE,eAAe,CAACE,gBAAgB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAO/F,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAE6B,GAAG,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMgG,kBAAkB,GAAIC,CAAC,IAAK;IAChC3H,aAAa,CAAC2H,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAO7C,GAAG,IAAK;IACnC,IAAI;MACFrF,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMiF,QAAQ,GAAG,MAAM5F,UAAU,CAACQ,IAAI,CAACsI,OAAO,CAAC9C,GAAG,CAACK,EAAE,CAAC;MACtD,MAAM0C,cAAc,GAAGnD,QAAQ,CAACG,IAAI;MAEpCrD,OAAO,CAAC6C,GAAG,CAAC,uBAAuB,EAAEwD,cAAc,CAAC;MACpDrG,OAAO,CAAC6C,GAAG,CAAC,qBAAqB,EAAEwD,cAAc,CAAClG,SAAS,CAAC;MAE5D5B,cAAc,CAAC+E,GAAG,CAAC;MACnB;MACA;MACA,MAAMgD,UAAU,GAAI/G,UAAU,IAAK;QACjC,IAAI,CAACA,UAAU,EAAE,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;;QAE7C,IAAI;UACF;UACA,MAAMM,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;UACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;YACzBM,OAAO,CAACuG,IAAI,CAAC,wBAAwB,EAAEhH,UAAU,CAAC;YAClD,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC9B;;UAEA;UACA,MAAMU,IAAI,GAAGJ,IAAI,CAACY,WAAW,CAAC,CAAC;UAC/B,MAAMP,KAAK,GAAG2G,MAAM,CAAChH,IAAI,CAACiH,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAC1D,MAAM5G,GAAG,GAAG0G,MAAM,CAAChH,IAAI,CAACmH,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACnD,OAAO,GAAG9G,IAAI,IAAIC,KAAK,IAAIC,GAAG,EAAE;QAClC,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,wBAAwB,EAAE6B,GAAG,CAAC;UAC5C,OAAOb,iBAAiB,CAAC,CAAC;QAC5B;MACF,CAAC;;MAED;MACA,IAAImH,cAAc,CAACtF,cAAc,EAAE;QACjCO,kBAAkB,CAAC+E,cAAc,CAACtF,cAAc,CAAC;MACnD;MAEA,IAAIsF,cAAc,CAACrF,kBAAkB,EAAE;QACrCQ,mBAAmB,CAAC6E,cAAc,CAACrF,kBAAkB,CAAC;MACxD;;MAEA;MACA,IAAI4F,cAAc,GAAG,IAAI;MACzB,IAAIP,cAAc,CAACnF,YAAY,EAAE;QAC/BQ,cAAc,CAAC2E,cAAc,CAACnF,YAAY,CAAC;QAC3C0F,cAAc,GAAGP,cAAc,CAACnF,YAAY;MAC9C,CAAC,MAAM,IAAImF,cAAc,CAACP,gBAAgB,EAAE;QAC1CpE,cAAc,CAAC2E,cAAc,CAACP,gBAAgB,CAAC;QAC/Cc,cAAc,GAAGP,cAAc,CAACP,gBAAgB;MAClD,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAMe,iBAAiB,GAAG,MAAMvJ,UAAU,CAACoB,SAAS,CAACyE,MAAM,CAAC,CAAC;UAC7D,MAAMwC,aAAa,GAAGkB,iBAAiB,CAACxD,IAAI,CAACA,IAAI,IAAI,EAAE;UAEvD,MAAMuC,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxChC,OAAO,IAAIA,OAAO,CAAC5C,YAAY,KAAKoF,cAAc,CAACpF,YACrD,CAAC;UAED,IAAI2E,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;YACvDpE,cAAc,CAACkE,eAAe,CAACE,gBAAgB,CAAC;YAChDc,cAAc,GAAGhB,eAAe,CAACE,gBAAgB;UACnD;QACF,CAAC,CAAC,OAAO/F,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;QACnD;MACF;MACA;MACA,IAAI+G,QAAQ,GAAGT,cAAc,CAAC1F,gBAAgB;MAC9C,IAAI,CAAChD,kBAAkB,CAACoJ,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC1CA,QAAQ,GAAG,wBAAwB;MACrC;MACA5G,aAAa,CAAC;QACZC,SAAS,EAAEkG,cAAc,CAAClG,SAAS;QACnCc,YAAY,EAAEoF,cAAc,CAACpF,YAAY;QACzCrB,IAAI,EAAEyG,cAAc,CAACzG,IAAI,IAAI,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACrDC,UAAU,EAAEiG,UAAU,CAACD,cAAc,CAAChG,UAAU,CAAC;QACjDC,QAAQ,EAAEgG,UAAU,CAACD,cAAc,CAAC/F,QAAQ,CAAC;QAC7CI,QAAQ,EAAE2F,cAAc,CAAC3F,QAAQ;QACjCC,gBAAgB,EAAEmG,QAAQ;QAC1BlG,UAAU,EAAEyF,cAAc,CAACzF,UAAU,IAAI,EAAE;QAC3CC,UAAU,EAAEwF,cAAc,CAACxF,UAAU,IAAI,EAAE;QAC3CN,aAAa,EAAE8F,cAAc,CAAC9F,aAAa,IAAI,EAAE;QACjDC,WAAW,EAAE6F,cAAc,CAAC7F,WAAW;QACvCC,UAAU,EAAE,CAAC,MAAM;UACjB,IAAI;YACF;YACA,IAAI,CAAC4F,cAAc,CAAC5F,UAAU,EAAE,OAAO,EAAE;YAEzC,IAAI,OAAO4F,cAAc,CAAC5F,UAAU,KAAK,QAAQ,EAAE;cACjD;cACA,IAAI4F,cAAc,CAAC5F,UAAU,CAACuG,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACpD,OAAO9E,IAAI,CAACC,KAAK,CAACiE,cAAc,CAAC5F,UAAU,CAAC;cAC9C,CAAC,MAAM;gBACL;gBACA,OAAO,CAAC4F,cAAc,CAAC5F,UAAU,CAAC;cACpC;YACF,CAAC,MAAM,IAAIyG,KAAK,CAACC,OAAO,CAACd,cAAc,CAAC5F,UAAU,CAAC,EAAE;cACnD,OAAO4F,cAAc,CAAC5F,UAAU;YAClC,CAAC,MAAM;cACL,OAAO,EAAE;YACX;UACF,CAAC,CAAC,OAAOV,GAAG,EAAE;YACZC,OAAO,CAACuG,IAAI,CAAC,iDAAiD,EAAExG,GAAG,CAAC;YACpE,OAAOsG,cAAc,CAAC5F,UAAU,GAAG,CAAC4F,cAAc,CAAC5F,UAAU,CAAC,GAAG,EAAE;UACrE;QACF,CAAC,EAAE,CAAC;QACJK,eAAe,EAAEuF,cAAc,CAACvF,eAAe,IAAI,EAAE;QACrDC,cAAc,EAAEsF,cAAc,CAACtF,cAAc;QAC7CC,kBAAkB,EAAEqF,cAAc,CAACrF,kBAAkB;QACrDE,YAAY,EAAEmF,cAAc,CAACnF,YAAY;QACzC4E,gBAAgB,EAAEc,cAAc;QAAE;QAClC;QACAzF,MAAM,EAAE,QAAQ;QAChBC,GAAG,EAAEiF,cAAc,CAACjF,GAAG,IAAI,KAAK,CAAC;MACnC,CAAC,CAAC;MAEF3C,cAAc,CAAC,IAAI,CAAC;MACpBN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;MACjD5B,QAAQ,CAAC,kCAAkCmF,GAAG,CAACQ,QAAQ,EAAE,CAAC;IAC5D,CAAC,SAAS;MACR7F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmJ,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI;MACFpJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMX,UAAU,CAACQ,IAAI,CAACqH,MAAM,CAACkC,KAAK,CAAC;MACnCtJ,OAAO,CAACD,IAAI,CAACwE,MAAM,CAACgB,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK0D,KAAK,CAAC,CAAC;MAC7ClJ,QAAQ,CAAC,IAAI,CAAC;MACdY,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,qBAAqB,EAAE6B,GAAG,CAAC;MACzC5B,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqJ,aAAa,GAAIhE,GAAG,IAAK;IAC7BrE,cAAc,CAACqE,GAAG,CAAC;IACnBvE,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMwI,YAAY,GAAGA,CAAA,KAAM;IACzBxI,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuI,kBAAkB,GAAG,MAAOH,KAAK,IAAK;IAC1C,IAAI;MACFpJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwJ,WAAW,GAAG3J,IAAI,CAAC+H,IAAI,CAACvC,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK0D,KAAK,CAAC;MACtD,MAAMK,SAAS,GAAGD,WAAW,CAACtG,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;;MAEzE;MACA;MACA;;MAEA;MACApD,OAAO,CAACD,IAAI,CAAC2E,GAAG,CAACa,GAAG,IAClBA,GAAG,CAACK,EAAE,KAAK0D,KAAK,GACZ;QAAC,GAAG/D,GAAG;QAAEnC,MAAM,EAAEuG;MAAS,CAAC,GAC3BpE,GACN,CAAC,CAAC;MAEFnF,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,4BAA4B,EAAE6B,GAAG,CAAC;MAChD5B,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACC,MAAM0J,eAAe,GAAGA,CAAA,KAAM;IAC9BlJ,cAAc,CAAC,IAAI,CAAC;IACpBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACC,MAAMqJ,UAAU,GAAGA,CAAA,KAAM;IACzBnJ,cAAc,CAAC,KAAK,CAAC;IACrBF,cAAc,CAAC,IAAI,CAAC;IACpB;IACA2B,aAAa,CAAC;MACZC,SAAS,EAAE,EAAE;MACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;MAC/BoB,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,iBAAiB;MAAE;MAChCC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,gBAAgB;MAAE;MAC5BC,gBAAgB,EAAE,aAAa;MAC/BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,IAAI;MACpBC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE;IACP,CAAC,CAAC;IACF;IACAE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,mBAAmB,CAAC,IAAI,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;IACpB7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMgJ,iBAAiB,GAAIC,IAAI,IAAK;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA;IACA,OAAOA,IAAI,CACRC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAAA,CAC5Bf,IAAI,CAAC,CAAC;EACX,CAAC;EAED,MAAMgB,gBAAgB,GAAIhC,CAAC,IAAK;IAC9B,MAAM;MAAEtD,IAAI;MAAEwD,KAAK;MAAE+B,IAAI;MAAEC;IAAQ,CAAC,GAAGlC,CAAC,CAACC,MAAM;;IAE/C;IACA,IAAIvD,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,iBAAiB,EAAE;MACtDxC,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAACyC,IAAI,GAAGuF,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGhC,KAAK,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACPhG,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAACyC,IAAI,GAAGuF,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGhC;MAC1C,CAAC,CAAC;IACF;EACF,CAAC;EAED,MAAMiC,iBAAiB,GAAGA,CAACnC,CAAC,EAAEoC,SAAS,KAAK;IAC1C,MAAMC,IAAI,GAAGrC,CAAC,CAACC,MAAM,CAACqC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpB,IAAIL,SAAS,KAAK,gBAAgB,EAAE;UAClC9G,kBAAkB,CAACiH,MAAM,CAACG,MAAM,CAAC;UACjCxI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEc,cAAc,EAAEsH;UAAK,CAAC,CAAC;QACxD,CAAC,MAAM,IAAID,SAAS,KAAK,oBAAoB,EAAE;UAC7C5G,mBAAmB,CAAC+G,MAAM,CAACG,MAAM,CAAC;UAClCxI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEe,kBAAkB,EAAEqH;UAAK,CAAC,CAAC;QAC5D,CAAC,MAAM,IAAID,SAAS,KAAK,cAAc,EAAE;UACvC1G,cAAc,CAAC6G,MAAM,CAACG,MAAM,CAAC;UAC7BxI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEiB,YAAY,EAAEmH;UAAK,CAAC,CAAC;QACtD;MACF,CAAC;MACDE,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAI/E,OAAO,IAAK;IACvC7D,OAAO,CAAC6C,GAAG,CAAC,mBAAmB,EAAEgB,OAAO,CAAC;;IAEzC;IACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC5C,YAAY,EAAE;MACrCjB,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;;IAEA;IACAgC,aAAa,CAAC;MACZ,GAAGD,UAAU;MACbgB,YAAY,EAAE4C,OAAO,CAAC5C,YAAY;MAClCC,YAAY,EAAE,IAAI;MAAE;MACpB4E,gBAAgB,EAAEjC,OAAO,CAACiC,gBAAgB,CAAC;IAC7C,CAAC,CAAC;;IAEF;IACA,IAAIjC,OAAO,CAACiC,gBAAgB,EAAE;MAC5BpE,cAAc,CAACmC,OAAO,CAACiC,gBAAgB,CAAC;IAC1C,CAAC,MAAM;MACLpE,cAAc,CAAC,IAAI,CAAC;IACtB;;IAEA;IACA7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMgK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnH,cAAc,CAAC,IAAI,CAAC;IACpB;IACA,IAAIzB,UAAU,CAACiB,YAAY,EAAE;MAC3BhB,aAAa,CAAC;QACZ,GAAGD,UAAU;QACbiB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM4H,gBAAgB,GAAG,MAAO9C,CAAC,IAAK;IACpCA,CAAC,CAAC+C,cAAc,CAAC,CAAC;;IAElB;IACA/I,OAAO,CAAC6C,GAAG,CAAC,sCAAsC,CAAC;IACnD7C,OAAO,CAAC6C,GAAG,CAAC,sBAAsB,EAAE5C,UAAU,CAAC;IAC/CD,OAAO,CAAC6C,GAAG,CAAC,eAAe,EAAEvE,WAAW,CAAC;;IAEzC;IACA,IAAI,CAAC2B,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACE,SAAS,CAAC6G,IAAI,CAAC,CAAC,EAAE;MACzD7I,QAAQ,CAAC,uBAAuB,CAAC;MACjC;IACF;IAEA,IAAI,CAAC8B,UAAU,CAACgB,YAAY,IAAI,CAAChB,UAAU,CAACgB,YAAY,CAAC+F,IAAI,CAAC,CAAC,EAAE;MAC/D7I,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;;IAEA;IACA,IAAI8B,UAAU,CAACM,aAAa,IAAIN,UAAU,CAACM,aAAa,CAACyG,IAAI,CAAC,CAAC,EAAE;MAC/D,MAAMgC,UAAU,GAAG,4BAA4B;MAC/C,MAAMC,QAAQ,GAAG,+DAA+D;MAEhF,MAAM/C,KAAK,GAAGjG,UAAU,CAACM,aAAa,CAACyG,IAAI,CAAC,CAAC;MAC7C,IAAI,CAACgC,UAAU,CAACE,IAAI,CAAChD,KAAK,CAAC,IAAI,CAAC+C,QAAQ,CAACC,IAAI,CAAChD,KAAK,CAAC,EAAE;QACpD/H,QAAQ,CAAC,mDAAmD,CAAC;QAC7D;MACF;IACF;;IAEA;IACA,IAAIgL,qBAAqB,GAAGlJ,UAAU,CAACU,gBAAgB;IACvD,IAAI,CAACwI,qBAAqB,EAAE;MAC1BA,qBAAqB,GAAG,wBAAwB;IAClD;IAEA,IAAI;MACFlL,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA+B,OAAO,CAAC6C,GAAG,CAAC,gCAAgC,EAAE5C,UAAU,CAACE,SAAS,CAAC;MACnE,MAAMiJ,cAAc,GAAGvB,iBAAiB,CAAC5H,UAAU,CAACE,SAAS,CAAC;MAC9DH,OAAO,CAAC6C,GAAG,CAAC,+BAA+B,EAAEuG,cAAc,CAAC;MAE5D,MAAMC,aAAa,GAAG;QACpB,GAAGpJ,UAAU;QACbE,SAAS,EAAEiJ,cAAc;QACzBtI,eAAe,EAAE+G,iBAAiB,CAAC5H,UAAU,CAACa,eAAe,CAAC;QAC9DH,gBAAgB,EAAEwI,qBAAqB;QACvC;QACA9I,UAAU,EAAE/B,WAAW,GAAG2B,UAAU,CAACI,UAAU,GAAIJ,UAAU,CAACI,UAAU,IAAInB,iBAAiB,CAAC;MAChG,CAAC;MAEDc,OAAO,CAAC6C,GAAG,CAAC,sBAAsB,EAAEwG,aAAa,CAAC;;MAElD;MACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC7B;MACF,MAAMC,QAAQ,GAAG,CACf,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAC7D,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAC5E,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,KAAK,CACrD;;MAED;MACAA,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAI;QACtB,IAAIL,aAAa,CAACK,GAAG,CAAC,KAAKC,SAAS,IAAIN,aAAa,CAACK,GAAG,CAAC,KAAK,IAAI,EAAE;UACnE;UACA,IAAIA,GAAG,KAAK,YAAY,EAAE;YACxB,IAAI;cACF,IAAIxC,KAAK,CAACC,OAAO,CAACkC,aAAa,CAACK,GAAG,CAAC,CAAC,EAAE;gBACrC;gBACA,MAAME,cAAc,GAAGP,aAAa,CAACK,GAAG,CAAC,CAACjH,GAAG,CAACF,KAAK,IAAIiE,MAAM,CAACjE,KAAK,CAAC,CAACyE,IAAI,CAAC,CAAC,CAAC;gBAC5EsC,QAAQ,CAACO,MAAM,CAACH,GAAG,EAAEvH,IAAI,CAAC2H,SAAS,CAACF,cAAc,CAAC,CAAC;cACtD,CAAC,MAAM;gBACL;gBACAN,QAAQ,CAACO,MAAM,CAACH,GAAG,EAAEvH,IAAI,CAAC2H,SAAS,CAAC,CAACtD,MAAM,CAAC6C,aAAa,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;cACpE;YACF,CAAC,CAAC,OAAO3J,GAAG,EAAE;cACZC,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,EAAE6B,GAAG,CAAC;cAClDuJ,QAAQ,CAACO,MAAM,CAACH,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9B;UACF,CAAC,MAAM;YACL1J,OAAO,CAAC6C,GAAG,CAAC,sBAAsB6G,GAAG,MAAML,aAAa,CAACK,GAAG,CAAC,EAAE,CAAC;YAChEJ,QAAQ,CAACO,MAAM,CAACH,GAAG,EAAEL,aAAa,CAACK,GAAG,CAAC,CAAC;UAC1C;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIL,aAAa,CAACtI,cAAc,IAAIsI,aAAa,CAACtI,cAAc,YAAYgJ,IAAI,EAAE;QAChFT,QAAQ,CAACO,MAAM,CAAC,gBAAgB,EAAER,aAAa,CAACtI,cAAc,CAAC;MACjE;MAEA,IAAIsI,aAAa,CAACrI,kBAAkB,IAAIqI,aAAa,CAACrI,kBAAkB,YAAY+I,IAAI,EAAE;QACxFT,QAAQ,CAACO,MAAM,CAAC,oBAAoB,EAAER,aAAa,CAACrI,kBAAkB,CAAC;MACzE;MAEA,IAAIqI,aAAa,CAACnI,YAAY,IAAImI,aAAa,CAACnI,YAAY,YAAY6I,IAAI,EAAE;QAC5ET,QAAQ,CAACO,MAAM,CAAC,cAAc,EAAER,aAAa,CAACnI,YAAY,CAAC;MAC7D;;MAEA;MACA,IAAIO,WAAW,IAAI,CAAC4H,aAAa,CAACnI,YAAY,EAAE;QAC9CoI,QAAQ,CAACO,MAAM,CAAC,2BAA2B,EAAEpI,WAAW,CAAC;MAC3D;;MAEA;MACA,IAAI4H,aAAa,CAACvD,gBAAgB,EAAE;QAClCwD,QAAQ,CAACO,MAAM,CAAC,kBAAkB,EAAER,aAAa,CAACvD,gBAAgB,CAAC;MACrE;;MAEA;MACA9F,OAAO,CAAC6C,GAAG,CAAC,oBAAoB,CAAC;MACjC,KAAK,IAAImH,IAAI,IAAIV,QAAQ,CAACW,OAAO,CAAC,CAAC,EAAE;QACnCjK,OAAO,CAAC6C,GAAG,CAACmH,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC;MACAhK,OAAO,CAAC6C,GAAG,CAAC,4BAA4B,EAAEyG,QAAQ,CAACY,GAAG,CAAC,WAAW,CAAC,CAAC;;MAEpE;MACA,IAAI5L,WAAW,EAAE;QACf;QACA,IAAI;UACF,MAAMhB,UAAU,CAACQ,IAAI,CAACqM,MAAM,CAAC7L,WAAW,CAACqF,EAAE,EAAE2F,QAAQ,CAAC;UACtD;QACF,CAAC,CAAC,OAAOc,QAAQ,EAAE;UACjB;UACA,MAAMA,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAM/M,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGiH,YAAY,CAAChH,IAAI,CAACZ,GAAG,CAACa,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACdxC,GAAG,EAAEkC,GAAG,CAAClC,GAAG,IAAI,KAAK;UAAE;UACvByC,OAAO,EAAEP,GAAG,CAACrC,YAAY;UACzB6C,QAAQ,EAAER,GAAG,CAACnD,SAAS;UACvB4D,QAAQ,EAAET,GAAG,CAAC9C,WAAW;UACzBwD,QAAQ,EAAEV,GAAG,CAAC5C,QAAQ;UACtBC,gBAAgB,EAAE2C,GAAG,CAAC3C,gBAAgB;UACtCsD,QAAQ,EAAEX,GAAG,CAAC/C,aAAa;UAC3B2D,MAAM,EAAGZ,GAAG,CAAC1C,UAAU,IAAI0C,GAAG,CAACzC,UAAU,GACrC,GAAGyC,GAAG,CAAC1C,UAAU,MAAM0C,GAAG,CAACzC,UAAU,EAAE,GACvCyC,GAAG,CAAC1C,UAAU,GACZ,GAAG0C,GAAG,CAAC1C,UAAU,EAAE,GACnB0C,GAAG,CAACzC,UAAU,GACZ,GAAGyC,GAAG,CAACzC,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClBgD,UAAU,EAAEb,GAAG,CAACjD,UAAU,GAAG,IAAIlB,IAAI,CAACmE,GAAG,CAACjD,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvEiE,GAAG,CAACG,WAAW,GAAG,IAAItE,IAAI,CAACmE,GAAG,CAACG,WAAW,CAAC,CAACrE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvFgF,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BhE,QAAQ,EAAEgD,GAAG,CAAChD,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMiE,UAAU,GAAG,CAAC,GAAGnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvF,IAAI,CAACuF,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIhF,IAAI,CAACsF,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGpG,OAAO,CAACwG,UAAU,CAAC;MACrB,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAMjH,UAAU,CAACQ,IAAI,CAACwM,MAAM,CAAChB,QAAQ,CAAC;UACtC;QACF,CAAC,CAAC,OAAOc,QAAQ,EAAE;UAAA,IAAAG,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;UACjB;UACAzK,OAAO,CAAC9B,KAAK,CAAC,oBAAoB,EAAE;YAClCwM,OAAO,EAAEN,QAAQ,CAACM,OAAO;YACzBvJ,MAAM,GAAAoJ,kBAAA,GAAEH,QAAQ,CAAClH,QAAQ,cAAAqH,kBAAA,uBAAjBA,kBAAA,CAAmBpJ,MAAM;YACjCwJ,UAAU,GAAAH,mBAAA,GAAEJ,QAAQ,CAAClH,QAAQ,cAAAsH,mBAAA,uBAAjBA,mBAAA,CAAmBG,UAAU;YACzCtH,IAAI,GAAAoH,mBAAA,GAAEL,QAAQ,CAAClH,QAAQ,cAAAuH,mBAAA,uBAAjBA,mBAAA,CAAmBpH;UAC3B,CAAC,CAAC;UACF,MAAM+G,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAM/M,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGiH,YAAY,CAAChH,IAAI,CAACZ,GAAG,CAACa,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACdxC,GAAG,EAAEkC,GAAG,CAAClC,GAAG,IAAI,KAAK;UAAE;UACvByC,OAAO,EAAEP,GAAG,CAACrC,YAAY;UACzB6C,QAAQ,EAAER,GAAG,CAACnD,SAAS;UACvB4D,QAAQ,EAAET,GAAG,CAAC9C,WAAW;UACzBwD,QAAQ,EAAEV,GAAG,CAAC5C,QAAQ;UACtBC,gBAAgB,EAAE2C,GAAG,CAAC3C,gBAAgB;UACtCsD,QAAQ,EAAEX,GAAG,CAAC/C,aAAa;UAC3B2D,MAAM,EAAGZ,GAAG,CAAC1C,UAAU,IAAI0C,GAAG,CAACzC,UAAU,GACrC,GAAGyC,GAAG,CAAC1C,UAAU,MAAM0C,GAAG,CAACzC,UAAU,EAAE,GACvCyC,GAAG,CAAC1C,UAAU,GACZ,GAAG0C,GAAG,CAAC1C,UAAU,EAAE,GACnB0C,GAAG,CAACzC,UAAU,GACZ,GAAGyC,GAAG,CAACzC,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClBgD,UAAU,EAAEb,GAAG,CAACjD,UAAU,GAAG,IAAIlB,IAAI,CAACmE,GAAG,CAACjD,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvEiE,GAAG,CAACG,WAAW,GAAG,IAAItE,IAAI,CAACmE,GAAG,CAACG,WAAW,CAAC,CAACrE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvFgF,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BhE,QAAQ,EAAEgD,GAAG,CAAChD,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMiE,UAAU,GAAG,CAAC,GAAGnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvF,IAAI,CAACuF,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIhF,IAAI,CAACsF,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGpG,OAAO,CAACwG,UAAU,CAAC;MACrB;MAEApG,QAAQ,CAAC,IAAI,CAAC;MACd;MACAyJ,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAO7H,GAAG,EAAE;MAAA,IAAA6K,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ/K,OAAO,CAAC9B,KAAK,CAAC,mBAAmB,EAAE6B,GAAG,CAAC;MACvC;MACA,MAAMiL,YAAY,GAAG,EAAAJ,aAAA,GAAA7K,GAAG,CAACmD,QAAQ,cAAA0H,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcvH,IAAI,cAAAwH,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,OAAAI,cAAA,GAC5B/K,GAAG,CAACmD,QAAQ,cAAA4H,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczH,IAAI,cAAA0H,mBAAA,uBAAlBA,mBAAA,CAAoB7M,KAAK,MACxBI,WAAW,GAAG,sBAAsB,GAAG,sBAAsB,CAAC;MACnFH,QAAQ,CAAC6M,YAAY,CAAC;IACxB,CAAC,SAAS;MACR/M,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgN,YAAY,GAAGnN,IAAI,CAACwE,MAAM,CAACgB,GAAG,IAAI;IACtC,MAAM4H,aAAa,GAAI5H,GAAG,CAACO,OAAO,IAAIP,GAAG,CAACO,OAAO,CAACsH,WAAW,CAAC,CAAC,CAACpE,QAAQ,CAAC3I,UAAU,CAAC+M,WAAW,CAAC,CAAC,CAAC,IAC3E7H,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAACqH,WAAW,CAAC,CAAC,CAACpE,QAAQ,CAAC3I,UAAU,CAAC+M,WAAW,CAAC,CAAC,CAAE;IAErG,OAAOD,aAAa;EACtB,CAAC,CAAC;;EAEF;EACA9O,SAAS,CAAC,MAAM;IACd,MAAMgP,kBAAkB,GAAIxI,KAAK,IAAK;MACpC,MAAMyI,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;MAC1E,IAAIF,eAAe,IAAI,CAACA,eAAe,CAACG,QAAQ,CAAC5I,KAAK,CAACqD,MAAM,CAAC,EAAE;QAC9DpH,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAED,IAAID,mBAAmB,EAAE;MACvB0M,QAAQ,CAACvI,gBAAgB,CAAC,WAAW,EAAEqI,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXE,QAAQ,CAACtI,mBAAmB,CAAC,WAAW,EAAEoI,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACxM,mBAAmB,CAAC,CAAC;EAEzB,oBACEpB,OAAA;IAAKiO,SAAS,EAAC,sBAAsB;IAAAC,QAAA,GAElC5M,iBAAiB,iBAChBtB,OAAA;MAAKiO,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrClO,OAAA;QAAKiO,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClO,OAAA;UAAKiO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpClO,OAAA,CAAClB,eAAe;YAACqP,IAAI,EAAEvO,qBAAsB;YAACqO,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxEvO,OAAA;YAAAkO,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNvO,OAAA;UAAKiO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrClO,OAAA;YAAAkO,QAAA,EAAG;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDvO,OAAA;YAAAkO,QAAA,eAAGlO,OAAA;cAAAkO,QAAA,EAAS1M,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8E;YAAQ;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CvO,OAAA;YAAAkO,QAAA,EAAG;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNvO,OAAA;UAAKiO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrClO,OAAA;YACEiO,SAAS,EAAC,mBAAmB;YAC7BO,OAAO,EAAEzE,YAAa;YAAAmE,QAAA,EACvB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvO,OAAA;YACEiO,SAAS,EAAC,oBAAoB;YAC9BO,OAAO,EAAEA,CAAA,KAAM5E,eAAe,CAACpI,WAAW,CAAC2E,EAAE,CAAE;YAAA+H,QAAA,EAChD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDvO,OAAA;MAAKiO,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlO,OAAA;QAAIiO,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CvO,OAAA;QAAKiO,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjClO,OAAA;UAAQiO,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAEA,CAAA,KAAM;YAChD/N,UAAU,CAAC,IAAI,CAAC;YAChB,MAAMgF,SAAS,GAAG,MAAAA,CAAA,KAAY;cAC5B,IAAI;gBACF,MAAMC,QAAQ,GAAG,MAAM5F,UAAU,CAACQ,IAAI,CAACqF,MAAM,CAAC,CAAC;;gBAE/C;gBACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACZ,GAAG,CAACa,GAAG,IAAI;kBAC7C;kBACA,IAAIC,SAAS;kBACb,IAAID,GAAG,CAACE,UAAU,EAAE;oBAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;kBAC5B,CAAC,MAAM,IAAIF,GAAG,CAACjD,UAAU,EAAE;oBACzBkD,SAAS,GAAGD,GAAG,CAACjD,UAAU;kBAC5B,CAAC,MAAM,IAAIiD,GAAG,CAACG,WAAW,EAAE;oBAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;kBAC7B,CAAC,MAAM;oBACLF,SAAS,GAAG,IAAIpE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACtC;;kBAEA;kBACA,IAAIsE,aAAa,GAAG,EAAE;kBACtB,IAAIJ,GAAG,CAAC1C,UAAU,IAAI0C,GAAG,CAACzC,UAAU,EAAE;oBACpC6C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,MAAM0C,GAAG,CAACzC,UAAU,EAAE;kBACzD,CAAC,MAAM,IAAIyC,GAAG,CAAC1C,UAAU,EAAE;oBACzB8C,aAAa,GAAG,GAAGJ,GAAG,CAAC1C,UAAU,EAAE;kBACrC,CAAC,MAAM,IAAI0C,GAAG,CAACzC,UAAU,EAAE;oBACzB6C,aAAa,GAAG,GAAGJ,GAAG,CAACzC,UAAU,EAAE;kBACrC,CAAC,MAAM;oBACL6C,aAAa,GAAG,EAAE;kBACpB;kBACE,OAAO;oBACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;oBACdxC,GAAG,EAAEkC,GAAG,CAAClC,GAAG,IAAI,KAAK;oBACrByC,OAAO,EAAEP,GAAG,CAACrC,YAAY;oBACzB6C,QAAQ,EAAER,GAAG,CAACnD,SAAS;oBACvB4D,QAAQ,EAAET,GAAG,CAAC9C,WAAW;oBACzBwD,QAAQ,EAAEV,GAAG,CAAC5C,QAAQ;oBACtBC,gBAAgB,EAAE2C,GAAG,CAAC3C,gBAAgB;oBACtCsD,QAAQ,EAAEX,GAAG,CAAC/C,aAAa;oBAC3B2D,MAAM,EAAER,aAAa;oBACrBvC,MAAM,EAAE,QAAQ;oBAChBgD,UAAU,EAAEZ,SAAS;oBACrBa,aAAa,EAAEd,GAAG,CAACjD,UAAU,IAAIiD,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;oBAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;oBAC1BhE,QAAQ,EAAEgD,GAAG,CAAChD;kBAChB,CAAC;gBACH,CAAC,CAAC;;gBAEF;gBACA,MAAMiE,UAAU,GAAG,CAAC,GAAGnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvF,IAAI,CAACuF,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIhF,IAAI,CAACsF,CAAC,CAACN,UAAU,CAAC,CAAC;gBACrGpG,OAAO,CAACwG,UAAU,CAAC;gBACnBpG,QAAQ,CAAC,IAAI,CAAC;cAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;gBACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;gBAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;cAC7C,CAAC,SAAS;gBACRF,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC;YAEDgF,SAAS,CAAC,CAAC;UACb,CAAE;UAACgJ,QAAQ,EAAEjO,OAAQ;UAAA0N,QAAA,gBACnBlO,OAAA,CAAClB,eAAe;YAACqP,IAAI,EAAEtO;UAAO;YAAAuO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCvO,OAAA;YAAAkO,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACTvO,OAAA;UAAQiO,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAErE,eAAgB;UAACsE,QAAQ,EAAEjO,OAAQ;UAAA0N,QAAA,gBAC7ElO,OAAA,CAAClB,eAAe;YAACqP,IAAI,EAAEpP;UAAO;YAAAqP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCvO,OAAA;YAAAkO,QAAA,EAAM;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7N,KAAK,iBACJV,OAAA;MAAKiO,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BlO,OAAA,CAAClB,eAAe;QAACqP,IAAI,EAAEhP;MAAoB;QAAAiP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CvO,OAAA;QAAAkO,QAAA,EAAOxN;MAAK;QAAA0N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAEDvO,OAAA;MAAKiO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChClO,OAAA;QAAKiO,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlO,OAAA;UAAKiO,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnClO,OAAA,CAAClB,eAAe;YAACqP,IAAI,EAAEjP,QAAS;YAAC+O,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAAU,eAAAvO,OAAA;YACrEyK,IAAI,EAAC,MAAM;YACXiE,WAAW,EAAC,kCAAkC;YAC9ChG,KAAK,EAAE9H,UAAW;YAClB+N,QAAQ,EAAEpG,kBAAmB;YAC7B0F,SAAS,EAAC,cAAc;YACxBW,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAENvO,OAAA;MAAKiO,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7B1N,OAAO,gBACNR,OAAA;QAAKiO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClO,OAAA,CAAClB,eAAe;UAACqP,IAAI,EAAEzO,SAAU;UAACoP,IAAI;UAACC,IAAI,EAAC;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDvO,OAAA;UAAAkO,QAAA,EAAM;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAENvO,OAAA,CAAAE,SAAA;QAAAgO,QAAA,eACAlO,OAAA;UAAOiO,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC3BlO,OAAA;YAAAkO,QAAA,eACElO,OAAA;cAAAkO,QAAA,gBACElO,OAAA;gBAAAkO,QAAA,EAAI;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZvO,OAAA;gBAAAkO,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbvO,OAAA;gBAAAkO,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBvO,OAAA;gBAAAkO,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBvO,OAAA;gBAAAkO,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfvO,OAAA;gBAAAkO,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBvO,OAAA;gBAAAkO,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBvO,OAAA;gBAAAkO,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdvO,OAAA;gBAAAkO,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvO,OAAA;YAAAkO,QAAA,EACGT,YAAY,CAAC5F,MAAM,GAAG,CAAC,GACtB4F,YAAY,CAACxI,GAAG,CAACa,GAAG,iBAClB9F,OAAA;cAAAkO,QAAA,gBACElO,OAAA;gBAAAkO,QAAA,GACGpI,GAAG,CAAClC,GAAG,iBACN5D,OAAA;kBAAMiO,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACzBlO,OAAA,CAAClB,eAAe;oBAACqP,IAAI,EAAE7O,MAAO;oBAACsP,KAAK,EAAE;sBAAEI,WAAW,EAAE;oBAAM;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAElE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP,eACvBvO,OAAA;kBAAKiO,SAAS,EAAC,cAAc;kBAACgB,KAAK,EAAEnJ,GAAG,CAACQ,QAAS;kBAAA4H,QAAA,EAC/CpI,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAACuB,MAAM,GAAG,EAAE,GACrC/B,GAAG,CAACQ,QAAQ,CAAC4I,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GACjCpJ,GAAG,CAACQ;gBAAQ;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACgBvO,OAAA;kBAAKiO,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEpI,GAAG,CAACO;gBAAO;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACLvO,OAAA;gBAAAkO,QAAA,EAAKpI,GAAG,CAACU;cAAQ;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBvO,OAAA;gBAAAkO,QAAA,eACElO,OAAA;kBAAKiO,SAAS,EAAC,kBAAkB;kBAACgB,KAAK,EAAEnJ,GAAG,CAAC3C,gBAAiB;kBAAA+K,QAAA,EAC3DpI,GAAG,CAAC3C,gBAAgB,IAAI;gBAAe;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLvO,OAAA;gBAAAkO,QAAA,eACElO,OAAA;kBAAKiO,SAAS,EAAC,UAAU;kBAACgB,KAAK,EAAEnJ,GAAG,CAACW,QAAS;kBAAAyH,QAAA,EAC3CpI,GAAG,CAACW,QAAQ,gBACXzG,OAAA;oBAAGmP,IAAI,EAAE,UAAUrJ,GAAG,CAACW,QAAQ,EAAG;oBAACwH,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtDpI,GAAG,CAACW,QAAQ,CAACoB,MAAM,GAAG,EAAE,GAAG/B,GAAG,CAACW,QAAQ,CAACyI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGpJ,GAAG,CAACW;kBAAQ;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,GAEJ;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLvO,OAAA;gBAAAkO,QAAA,EAAKpI,GAAG,CAACY;cAAM;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBvO,OAAA;gBAAAkO,QAAA,EAAKpM,iBAAiB,CAACgE,GAAG,CAAChD,QAAQ;cAAC;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxCvO,OAAA;gBAAAkO,QAAA,EAAKpM,iBAAiB,CAACgE,GAAG,CAACa,UAAU;cAAC;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CvO,OAAA;gBAAAkO,QAAA,eACElO,OAAA;kBAAKiO,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BlO,OAAA,CAAClB,eAAe;oBAACqP,IAAI,EAAE9O,KAAM;oBAAC4O,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtDzI,GAAG,CAACe,KAAK;gBAAA;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLvO,OAAA;gBAAAkO,QAAA,eACElO,OAAA;kBAAK4O,KAAK,EAAE;oBAAEQ,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,gBAC9BlO,OAAA;oBACEiO,SAAS,EAAC,2BAA2B;oBACrCO,OAAO,EAAEA,CAAA,KAAM7F,aAAa,CAAC7C,GAAG,CAAE;oBAClCmJ,KAAK,EAAC,UAAU;oBAChBR,QAAQ,EAAEjO,OAAQ;oBAAA0N,QAAA,eAElBlO,OAAA,CAAClB,eAAe;sBAACqP,IAAI,EAAEnP;oBAAO;sBAAAoP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eAETvO,OAAA;oBACEiO,SAAS,EAAC,6BAA6B;oBACvCO,OAAO,EAAEA,CAAA,KAAM1E,aAAa,CAAChE,GAAG,CAAE;oBAClCmJ,KAAK,EAAC,YAAY;oBAClBR,QAAQ,EAAEjO,OAAQ;oBAAA0N,QAAA,eAElBlO,OAAA,CAAClB,eAAe;sBAACqP,IAAI,EAAElP;oBAAQ;sBAAAmP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7DEzI,GAAG,CAACK,EAAE;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8DX,CACL,CAAC,gBAEFvO,OAAA;cAAAkO,QAAA,eACElO,OAAA;gBAAIqP,OAAO,EAAC,GAAG;gBAACpB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EACxCtN,UAAU,GACT,oCAAoC,GACpC;cAAmB;gBAAAwN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACN;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvN,WAAW,iBACVhB,OAAA;MAAKiO,SAAS,EAAC,eAAe;MAACO,OAAO,EAAGhG,CAAC,IAAK;QAC7C;QACA,IAAIA,CAAC,CAACC,MAAM,CAACwF,SAAS,KAAK,eAAe,EAAE;UAC1C7D,UAAU,CAAC,CAAC;QACd;MACF,CAAE;MAAA8D,QAAA,eACAlO,OAAA;QAAKiO,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlO,OAAA;UAAKiO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlO,OAAA;YAAAkO,QAAA,EAAKpN,WAAW,GAAG,UAAU,GAAG;UAAa;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDvO,OAAA;YAAQiO,SAAS,EAAC,cAAc;YAACO,OAAO,EAAEpE,UAAW;YAAA8D,QAAA,eACnDlO,OAAA,CAAClB,eAAe;cAACqP,IAAI,EAAE5O;YAAQ;cAAA6O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNvO,OAAA;UAAKiO,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BlO,OAAA;YAAMsP,QAAQ,EAAEhE,gBAAiB;YAAA4C,QAAA,gBAC/BlO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,WAAW;kBAAArB,QAAA,GAAC,YAAU,eAAAlO,OAAA;oBAAM4O,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpFvO,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACXtE,EAAE,EAAC,WAAW;kBACdjB,IAAI,EAAC,WAAW;kBAChBwD,KAAK,EAAEjG,UAAU,CAACE,SAAU;kBAC5BgM,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBACRf,WAAW,EAAC;gBAAiB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACRvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAAkO,QAAA,GAAO,UAAQ,eAAAlO,OAAA;oBAAM4O,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9DvO,OAAA;kBAAKiO,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlO,OAAA;oBAAKiO,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtClO,OAAA;sBACEyK,IAAI,EAAC,QAAQ;sBACbwD,SAAS,EAAC,uBAAuB;sBACjCO,OAAO,EAAEA,CAAA,KAAMnN,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;sBAAA8M,QAAA,GAE3DzL,UAAU,CAACgB,YAAY,GAAGhB,UAAU,CAACgB,YAAY,GAAG,kBAAkB,eACvEzD,OAAA,CAAClB,eAAe;wBAACqP,IAAI,EAAExO;sBAAc;wBAAAyO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,EAERnN,mBAAmB,iBAClBpB,OAAA;sBAAKiO,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnChN,SAAS,IAAIA,SAAS,CAAC2G,MAAM,GAAG,CAAC,GAChC3G,SAAS,CAAC+D,GAAG,CAACoB,OAAO,iBACnBrG,OAAA;wBAEEiO,SAAS,EAAC,cAAc;wBACxBO,OAAO,EAAEA,CAAA,KAAM;0BACbpD,mBAAmB,CAAC/E,OAAO,CAAC;wBAC9B,CAAE;wBAAA6H,QAAA,gBAEFlO,OAAA;0BAAKiO,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAC/B7H,OAAO,CAACiC,gBAAgB,gBACvBtI,OAAA;4BAAK0P,GAAG,EAAErJ,OAAO,CAACiC,gBAAiB;4BAACqH,GAAG,EAAEtJ,OAAO,CAAC5C;0BAAa;4BAAA2K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAEjEvO,OAAA,CAAClB,eAAe;4BAACqP,IAAI,EAAE3O;0BAAW;4BAAA4O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACrC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNvO,OAAA;0BAAAkO,QAAA,EAAO7H,OAAO,CAAC5C;wBAAY;0BAAA2K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAb9BlI,OAAO,CAACuJ,UAAU;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAcpB,CACN,CAAC,gBAEFvO,OAAA;wBAAKiO,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAC1D,eACDvO,OAAA;wBAAKiO,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,gBACnClO,OAAA;0BAAAkO,QAAA,EAAO;wBAA+B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC9CvO,OAAA;0BACEyK,IAAI,EAAC,MAAM;0BACXiE,WAAW,EAAC,oBAAoB;0BAChChG,KAAK,EAAEjG,UAAU,CAACgB,YAAY,IAAI,EAAG;0BACrCkL,QAAQ,EAAGnG,CAAC,IAAK;4BACf9F,aAAa,CAAC;8BACZ,GAAGD,UAAU;8BACbgB,YAAY,EAAE+E,CAAC,CAACC,MAAM,CAACC;4BACzB,CAAC,CAAC;0BACJ;wBAAE;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFvO,OAAA;0BACEyK,IAAI,EAAC,QAAQ;0BACbwD,SAAS,EAAC,mBAAmB;0BAC7BO,OAAO,EAAEA,CAAA,KAAMnN,sBAAsB,CAAC,KAAK,CAAE;0BAAA6M,QAAA,EAC9C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAGLtK,WAAW,iBACVjE,OAAA;oBAAKiO,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrClO,OAAA;sBAAK0P,GAAG,EAAEzL,WAAY;sBAAC0L,GAAG,EAAC;oBAAc;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5CvO,OAAA;sBACEyK,IAAI,EAAC,QAAQ;sBACbwD,SAAS,EAAC,oBAAoB;sBAC9BO,OAAO,EAAEnD,gBAAiB;sBAAA6C,QAAA,eAE1BlO,OAAA,CAAClB,eAAe;wBAACqP,IAAI,EAAE5O;sBAAQ;wBAAA6O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGA,CAACtK,WAAW,iBACXjE,OAAA;oBAAAkO,QAAA,gBACElO,OAAA;sBACEyK,IAAI,EAAC,MAAM;sBACXtE,EAAE,EAAC,cAAc;sBACjBjB,IAAI,EAAC,cAAc;sBACnByJ,QAAQ,EAAGnG,CAAC,IAAKmC,iBAAiB,CAACnC,CAAC,EAAE,cAAc,CAAE;sBACtDqH,MAAM,EAAC,SAAS;sBAChBjB,KAAK,EAAE;wBAAEQ,OAAO,EAAE;sBAAO;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACFvO,OAAA;sBACEyK,IAAI,EAAC,QAAQ;sBACbwD,SAAS,EAAC,mBAAmB;sBAC7BO,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAACgC,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAAC,CAAE;sBAAA7B,QAAA,gBAE/DlO,OAAA,CAAClB,eAAe;wBAACqP,IAAI,EAAE1O;sBAAQ;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,uBAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,MAAM;kBAAArB,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCvO,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACbtE,EAAE,EAAC,MAAM;kBACTjB,IAAI,EAAC,MAAM;kBACXwD,KAAK,EAAEjG,UAAU,CAACL,IAAK;kBACvBuM,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBACRO,GAAG,EAAC,MAAM;kBACVC,GAAG,EAAC;gBAAM;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,sBAAkB,eAAAvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACjDlO,OAAA;kBAAOuP,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CvO,OAAA;kBACEmG,EAAE,EAAC,UAAU;kBACbjB,IAAI,EAAC,UAAU;kBACfwD,KAAK,EAAEjG,UAAU,CAACS,QAAS;kBAC3ByL,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBAAAvB,QAAA,gBAERlO,OAAA;oBAAQ0I,KAAK,EAAC,gBAAgB;oBAAAwF,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtDvO,OAAA;oBAAQ0I,KAAK,EAAC,gBAAgB;oBAAAwF,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtDvO,OAAA;oBAAQ0I,KAAK,EAAC,aAAa;oBAAAwF,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDvO,OAAA;oBAAQ0I,KAAK,EAAC,WAAW;oBAAAwF,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CvO,OAAA;oBAAQ0I,KAAK,EAAC,WAAW;oBAAAwF,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,kBAAkB;kBAAArB,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DvO,OAAA;kBACEmG,EAAE,EAAC,kBAAkB;kBACrBjB,IAAI,EAAC,kBAAkB;kBACvBwD,KAAK,EAAEjG,UAAU,CAACU,gBAAiB;kBACnCwL,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBAAAvB,QAAA,EAEP/N,kBAAkB,CAAC8E,GAAG,CAACiL,MAAM,iBAC5BlQ,OAAA;oBAAqB0I,KAAK,EAAEwH,MAAO;oBAAAhC,QAAA,EAAEgC;kBAAM,GAA9BA,MAAM;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CvO,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACXtE,EAAE,EAAC,YAAY;kBACfjB,IAAI,EAAC,YAAY;kBACjBwD,KAAK,EAAEjG,UAAU,CAACI,UAAW;kBAC7B8L,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CvO,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACXtE,EAAE,EAAC,UAAU;kBACbjB,IAAI,EAAC,UAAU;kBACfwD,KAAK,EAAEjG,UAAU,CAACK,QAAS;kBAC3B6L,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,eAAe;kBAAArB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDvO,OAAA;kBACEyK,IAAI,EAAC,MAAM;kBACXtE,EAAE,EAAC,eAAe;kBAClBjB,IAAI,EAAC,eAAe;kBACpBwD,KAAK,EAAEjG,UAAU,CAACM,aAAc;kBAChC4L,QAAQ,EAAEnE,gBAAiB;kBAC3BkE,WAAW,EAAC;gBAA0D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDvO,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACbtE,EAAE,EAAC,YAAY;kBACfjB,IAAI,EAAC,YAAY;kBACjBwD,KAAK,EAAEjG,UAAU,CAACW,UAAW;kBAC7BuL,QAAQ,EAAEnE,gBAAiB;kBAC3BkE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDvO,OAAA;kBACEyK,IAAI,EAAC,QAAQ;kBACbtE,EAAE,EAAC,YAAY;kBACfjB,IAAI,EAAC,YAAY;kBACjBwD,KAAK,EAAEjG,UAAU,CAACY,UAAW;kBAC7BsL,QAAQ,EAAEnE,gBAAiB;kBAC3BkE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBlO,OAAA;gBAAKiO,SAAS,EAAC,2BAA2B;gBAACW,KAAK,EAAE;kBAACwB,SAAS,EAAE;gBAAM,CAAE;gBAAAlC,QAAA,eACpElO,OAAA;kBAAOiO,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACnClO,OAAA;oBACEyK,IAAI,EAAC,UAAU;oBACfvF,IAAI,EAAC,KAAK;oBACVwF,OAAO,EAAEjI,UAAU,CAACmB,GAAI;oBACxB+K,QAAQ,EAAEnE;kBAAiB;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACFvO,OAAA;oBAAMiO,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC7BlO,OAAA,CAAClB,eAAe;sBAACqP,IAAI,EAAE7O,MAAO;sBAAC2O,SAAS,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,sBAExD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAOuP,OAAO,EAAC,aAAa;kBAAArB,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDvO,OAAA;kBACEmG,EAAE,EAAC,aAAa;kBAChBjB,IAAI,EAAC,aAAa;kBAClBwD,KAAK,EAAEjG,UAAU,CAACO,WAAY;kBAC9B2L,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBAAAvB,QAAA,gBAERlO,OAAA;oBAAQ0I,KAAK,EAAC,iBAAiB;oBAAAwF,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxDvO,OAAA;oBAAQ0I,KAAK,EAAC,cAAc;oBAAAwF,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDvO,OAAA;oBAAQ0I,KAAK,EAAC,cAAc;oBAAAwF,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDvO,OAAA;oBAAQ0I,KAAK,EAAC,aAAa;oBAAAwF,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,oBAAgB,eAAAvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC7ClO,OAAA;gBAAKiO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACxDlO,OAAA;kBAAAkO,QAAA,gBACElO,OAAA;oBAAAkO,QAAA,EAAM;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvBvO,OAAA;oBAAMiO,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAEzL,UAAU,CAACQ,UAAU,CAAC4E,MAAM,EAAC,WAAS;kBAAA;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3EvO,OAAA;oBACEyK,IAAI,EAAC,QAAQ;oBACbmE,KAAK,EAAE;sBAAEyB,UAAU,EAAE,MAAM;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,QAAQ,EAAE;oBAAQ,CAAE;oBACrE/B,OAAO,EAAEA,CAAA,KAAM9L,aAAa,CAAC8N,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEvN,UAAU,EAAE;oBAAG,CAAC,CAAC,CAAE;oBAAAiL,QAAA,EACrE;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACRvO,OAAA;kBAAKiO,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxClO,OAAA;oBAAO4O,KAAK,EAAE;sBAAEY,KAAK,EAAE,SAAS;sBAAEiB,SAAS,EAAE;oBAAS,CAAE;oBAAAvC,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNvO,OAAA;kBAAKiO,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1C/J,kBAAkB,CAACc,GAAG,CAAC,CAACF,KAAK,EAAE2L,KAAK,kBACf1Q,OAAA;oBAEEiO,SAAS,EAAE,qBAAqBxL,UAAU,CAACQ,UAAU,CAACsG,QAAQ,CAACxE,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAAmJ,QAAA,gBAEpHlO,OAAA;sBACEyK,IAAI,EAAC,UAAU;sBACftE,EAAE,EAAE,YAAYuK,KAAK,EAAG;sBACxBhG,OAAO,EAAEjI,UAAU,CAACQ,UAAU,CAACsG,QAAQ,CAACxE,KAAK,CAAE;sBAC/C4J,QAAQ,EAAEnG,CAAC,IAAI;wBACb,MAAMkC,OAAO,GAAGlC,CAAC,CAACC,MAAM,CAACiC,OAAO;wBAChChI,aAAa,CAAC8N,IAAI,IAAI;0BACpB,MAAMG,GAAG,GAAG,IAAIC,GAAG,CAACJ,IAAI,CAACvN,UAAU,CAAC;0BACpC,IAAIyH,OAAO,EAAE;4BACXiG,GAAG,CAACE,GAAG,CAAC9L,KAAK,CAAC;0BAChB,CAAC,MAAM;4BACL4L,GAAG,CAAChJ,MAAM,CAAC5C,KAAK,CAAC;0BACnB;0BACA,OAAO;4BACL,GAAGyL,IAAI;4BACPvN,UAAU,EAAEyG,KAAK,CAACoH,IAAI,CAACH,GAAG;0BAC5B,CAAC;wBACH,CAAC,CAAC;sBACJ;oBAAE;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACuB,CAAC,eACFvO,OAAA;sBAAOuP,OAAO,EAAE,YAAYmB,KAAK,EAAG;sBAAAxC,QAAA,EACjCnJ;oBAAK;sBAAAqJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA,GAzBHmC,KAAK;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0BP,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBlO,OAAA;gBAAKiO,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClO,OAAA;kBAAOuP,OAAO,EAAC,iBAAiB;kBAAArB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDvO,OAAA;kBACEmG,EAAE,EAAC,iBAAiB;kBACpBjB,IAAI,EAAC,iBAAiB;kBACtB6L,IAAI,EAAC,GAAG;kBACRrI,KAAK,EAAEjG,UAAU,CAACa,eAAgB;kBAClCqL,QAAQ,EAAEnE,gBAAiB;kBAC3BiF,QAAQ;kBACRf,WAAW,EAAC;gBAAgC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACZvO,OAAA;kBAAOiO,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENvO,OAAA;cAAKiO,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAAkO,QAAA,EAAO;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BvO,OAAA;kBAAKiO,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpClO,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACXtE,EAAE,EAAC,gBAAgB;oBACnBwI,QAAQ,EAAGnG,CAAC,IAAKmC,iBAAiB,CAACnC,CAAC,EAAE,gBAAgB,CAAE;oBACxDqH,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFvO,OAAA;oBAAOuP,OAAO,EAAC,gBAAgB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC3DlO,OAAA,CAAClB,eAAe;sBAACqP,IAAI,EAAE1O;oBAAQ;sBAAA2O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCvO,OAAA;sBAAAkO,QAAA,EAAM;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACL1K,eAAe,iBACd7D,OAAA;kBAAKiO,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BlO,OAAA;oBAAK0P,GAAG,EAAE7L,eAAgB;oBAAC8L,GAAG,EAAC;kBAAU;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNvO,OAAA;gBAAKiO,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlO,OAAA;kBAAAkO,QAAA,EAAO;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCvO,OAAA;kBAAKiO,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpClO,OAAA;oBACEyK,IAAI,EAAC,MAAM;oBACXtE,EAAE,EAAC,oBAAoB;oBACvBwI,QAAQ,EAAGnG,CAAC,IAAKmC,iBAAiB,CAACnC,CAAC,EAAE,oBAAoB,CAAE;oBAC5DqH,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFvO,OAAA;oBAAOuP,OAAO,EAAC,oBAAoB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/DlO,OAAA,CAAClB,eAAe;sBAACqP,IAAI,EAAE1O;oBAAQ;sBAAA2O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCvO,OAAA;sBAAAkO,QAAA,EAAM;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLxK,gBAAgB,iBACf/D,OAAA;kBAAKiO,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChClO,OAAA;oBAAK0P,GAAG,EAAE3L,gBAAiB;oBAAC4L,GAAG,EAAC;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAINvO,OAAA;cAAKiO,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlO,OAAA;gBAAQyK,IAAI,EAAC,QAAQ;gBAACwD,SAAS,EAAC,eAAe;gBAACO,OAAO,EAAEpE,UAAW;gBAAA8D,QAAA,EAAC;cAErE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvO,OAAA;gBAAQyK,IAAI,EAAC,QAAQ;gBAACwD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5CpN,WAAW,GAAG,YAAY,GAAG;cAAY;gBAAAsN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClO,EAAA,CAt/CID,SAAS;AAAA4Q,EAAA,GAAT5Q,SAAS;AAw/Cf,eAAeA,SAAS;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}