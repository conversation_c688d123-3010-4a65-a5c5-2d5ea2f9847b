/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable no-useless-escape */
import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faEdit,
  faTrash,
  faSearch,
  faExclamationCircle,
  faCheckCircle,
  faEye,
  faFire,
  faTimes,
  faBuilding,
  faImage,
  faSpinner,
  faChevronDown,
  faExclamationTriangle,
  faSync
} from '@fortawesome/free-solid-svg-icons';
import '../../css/JobsAdmin.css';
import '../../css/JobFormModal.css';
import '../../css/shared-delete-dialog.css';
import ApiService from '../../services/apiService';
import { getActiveSubTopicNames, getDefaultSubTopics } from '../../utils/subTopicsUtils';
// Import search-fix.css last to ensure it takes precedence
import '../../css/search-fix.css';

const EXPERIENCE_OPTIONS = [
  "No Experience Required",
  "Entry Level",
  "Mid Level",
  "Senior Level",
  "Manager",
  "Executive"
];

const JobsAdmin = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedJob, setSelectedJob] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);
  
  // Delete confirmation dialog state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [jobToDelete, setJobToDelete] = useState(null);
  
  // New job form state
  // Get today's date in YYYY-MM-DD format for the date inputs
  const getTodayFormatted = () => new Date().toISOString().split('T')[0];
  
  // Format date for display in table
  const formatDisplayDate = (dateString) => {
    if (!dateString || dateString === '-') return '-';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    } catch (err) {
      console.error("Error formatting display date:", err);
      return dateString;
    }
  };
    const [newJobForm, setNewJobForm] = useState({
    job_title: '',
    year: new Date().getFullYear(),
    start_date: getTodayFormatted(),
    end_date: '',
    send_cv_email: '',
    main_topics: 'Government Jobs', // Set a default valid value from the enum
    sub_topics: [],
    job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'
    experience_level: 'Entry Level', // New field for experience level
    min_salary: '',
    max_salary: '',
    job_description: '',
    job_post_image: null,
    job_post_thumbnail: null,
    company_name: '',
    company_logo: null,
    // UI-only fields (not in database)
    status: 'Active',
    hot: false
  });
  
  // Image preview states
  const [jobImagePreview, setJobImagePreview] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  // State for managing sub-topics
  const [availableSubTopics, setAvailableSubTopics] = useState([]);

  // Load available sub-topics from localStorage
  const loadSubTopicsFromStorage = () => {
    try {
      const activeSubTopicNames = getActiveSubTopicNames();
      if (activeSubTopicNames.length > 0) {
        setAvailableSubTopics(activeSubTopicNames);
      } else {
        // Use default sub-topics if no saved data
        const defaultSubTopics = getDefaultSubTopics();
        setAvailableSubTopics(defaultSubTopics);
      }
    } catch (err) {
      console.error("Error loading sub-topics from localStorage:", err);
      // Fallback to default sub-topics
      const defaultSubTopics = getDefaultSubTopics();
      setAvailableSubTopics(defaultSubTopics);
    }
  };

  // Initialize available sub-topics
  useEffect(() => {
    loadSubTopicsFromStorage();
  }, []);

  // Listen for sub-topics updates from SubTopicsAdmin
  useEffect(() => {
    const handleSubTopicsUpdate = (event) => {
      console.log("Sub-topics updated, refreshing available options...");
      loadSubTopicsFromStorage();
    };

    // Add event listener for sub-topics updates
    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);
    };
  }, []);

  // Fetch jobs from backend
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        const response = await ApiService.jobs.getAll();
        
        // Transform backend data to match the format used in frontend
        // Adding default values for fields that don't exist in the database
        const formattedJobs = response.data.map(job => {
          // Determine the best date to use
          let dateToUse;
          if (job.created_at) {
            // Prefer created_at if available
            dateToUse = job.created_at;
          } else if (job.start_date) {
            // Otherwise use start_date
            dateToUse = job.start_date;
          } else if (job.posted_date) {
            // Fall back to posted_date
            dateToUse = job.posted_date;
          } else {
            // If no dates are available, use current date
            dateToUse = new Date().toISOString();
          }

          // Compose salary display string
          let salaryDisplay = '';
          if (job.min_salary && job.max_salary) {
            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;
          } else if (job.min_salary) {
            salaryDisplay = `${job.min_salary}`;
          } else if (job.max_salary) {
            salaryDisplay = `${job.max_salary}`;
          } else {
            salaryDisplay = '';
          }
            return {
            id: job.job_id,
            hot: job.hot || false, // Use actual hot value from database
            company: job.company_name,
            position: job.job_title,
            location: job.main_topics,
            workTime: job.job_type,
            experience_level: job.experience_level,
            cv_email: job.send_cv_email,
            salary: salaryDisplay,
            status: 'Active', // Default since it doesn't exist in DB
            datePosted: dateToUse,
            rawDatePosted: job.start_date || job.posted_date || job.created_at, // Store raw date for debugging
            views: job.view_count || 0, // Use actual view count if available
            end_date: job.end_date // Add end_date for auto-delete functionality
          };
        });
        
        // Sort jobs by datePosted descending (newest first)
        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));
        setJobs(sortedJobs);
        setError(null);
      } catch (err) {
        console.error("Error fetching jobs:", err);
        setError("Failed to load jobs from server");
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  // Auto-delete expired jobs (2 weeks after end date)
  useEffect(() => {
    const checkAndDeleteExpiredJobs = async () => {
      try {
        const currentDate = new Date();
        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds
        
        // Get all jobs to check for expired ones
        const response = await ApiService.jobs.getAll();
        const allJobs = response.data;
        
        const expiredJobs = allJobs.filter(job => {
          if (!job.end_date) return false; // Skip jobs without end date
          
          const endDate = new Date(job.end_date);
          const timeDifference = currentDate.getTime() - endDate.getTime();
          
          // Check if job is expired by more than 2 weeks
          return timeDifference > twoWeeksInMs;
        });
        
        // Delete expired jobs
        for (const expiredJob of expiredJobs) {
          try {
            await ApiService.jobs.delete(expiredJob.job_id);
            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);
          } catch (deleteErr) {
            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);
          }
        }
        
        // If any jobs were deleted, refresh the jobs list
        if (expiredJobs.length > 0) {
          // Refresh jobs list by fetching again
          const updatedResponse = await ApiService.jobs.getAll();
          const formattedJobs = updatedResponse.data.map(job => {
            let dateToUse;
            if (job.created_at) {
              dateToUse = job.created_at;
            } else if (job.start_date) {
              dateToUse = job.start_date;
            } else if (job.posted_date) {
              dateToUse = job.posted_date;
            } else {
              dateToUse = new Date().toISOString();
            }

            let salaryDisplay = '';
            if (job.min_salary && job.max_salary) {
              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;
            } else if (job.min_salary) {
              salaryDisplay = `${job.min_salary}`;
            } else if (job.max_salary) {
              salaryDisplay = `${job.max_salary}`;
            } else {
              salaryDisplay = '';
            }
            
            return {
              id: job.job_id,
              hot: job.hot || false,
              company: job.company_name,
              position: job.job_title,
              location: job.main_topics,
              workTime: job.job_type,
              experience_level: job.experience_level,
              cv_email: job.send_cv_email,
              salary: salaryDisplay,
              status: 'Active',
              datePosted: dateToUse,
              rawDatePosted: job.start_date || job.posted_date || job.created_at,
              views: job.view_count || 0,
              end_date: job.end_date
            };
          });
          
          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));
          setJobs(sortedJobs);
        }
        
      } catch (err) {
        console.error("Error checking for expired jobs:", err);
        // Don't set error state here to avoid disrupting normal operation
      }
    };

    // Run the check immediately when component mounts
    checkAndDeleteExpiredJobs();
    
    // Set up interval to check every hour (3600000 ms)
    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);
    
    // Cleanup interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  // Fetch companies when modal is opened
  useEffect(() => {
    if (isModalOpen) {
      console.log("Modal opened, fetching companies...");
      fetchCompanies();
    }
  }, [isModalOpen]);

  const fetchCompanies = async () => {
    try {
      console.log("Fetching companies from API...");
      const response = await ApiService.companies.getAll();
      const companiesData = response.data.data || [];
      console.log("Companies fetched:", companiesData);
      setCompanies(companiesData);
      
      // If we're editing a job and have a company name, select the matching company
      if (selectedJob && newJobForm.company_name) {
        const matchingCompany = companiesData.find(
          company => company.company_name === newJobForm.company_name
        );
        
        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {
          console.log("Setting logo preview for matching company:", matchingCompany.company_name);
          setLogoPreview(matchingCompany.company_logo_url);
        }
      }
    } catch (err) {
      console.error('Error fetching companies:', err);
      // Don't set error state here to avoid disrupting the job form
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleEditJob = async (job) => {
    try {
      setLoading(true);
      // Fetch full job details from backend
      const response = await ApiService.jobs.getById(job.id);
      const fullJobDetails = response.data;
      
      console.log("Job details from API:", fullJobDetails);
      console.log("Job title from API:", fullJobDetails.job_title);
      
      setSelectedJob(job);
      // Initialize form with job data - only using fields from the actual database schema
      // Format dates for the form (YYYY-MM-DD format required by date inputs)
      const formatDate = (dateString) => {
        if (!dateString) return getTodayFormatted(); // Default to today if no date provided
        
        try {
          // Parse the date and ensure it's valid
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            console.warn("Invalid date detected:", dateString);
            return getTodayFormatted(); // Default to today for invalid dates
          }
          
          // Format as YYYY-MM-DD for date input
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        } catch (err) {
          console.error("Error formatting date:", err);
          return getTodayFormatted();
        }
      };
      
      // Set image previews
      if (fullJobDetails.job_post_image) {
        setJobImagePreview(fullJobDetails.job_post_image);
      }
      
      if (fullJobDetails.job_post_thumbnail) {
        setThumbnailPreview(fullJobDetails.job_post_thumbnail);
      }
      
      // Handle company logo preview
      let companyLogoUrl = null;
      if (fullJobDetails.company_logo) {
        setLogoPreview(fullJobDetails.company_logo);
        companyLogoUrl = fullJobDetails.company_logo;
      } else if (fullJobDetails.company_logo_url) {
        setLogoPreview(fullJobDetails.company_logo_url);
        companyLogoUrl = fullJobDetails.company_logo_url;
      } else {
        // Fetch companies to find the logo
        try {
          const companiesResponse = await ApiService.companies.getAll();
          const companiesData = companiesResponse.data.data || [];
          
          const matchingCompany = companiesData.find(
            company => company.company_name === fullJobDetails.company_name
          );
          
          if (matchingCompany && matchingCompany.company_logo_url) {
            setLogoPreview(matchingCompany.company_logo_url);
            companyLogoUrl = matchingCompany.company_logo_url;
          }
        } catch (err) {
          console.error("Error finding company logo:", err);
        }
      }
      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to "No Experience Required"
      let expLevel = fullJobDetails.experience_level;
      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {
        expLevel = "No Experience Required";
      }
      setNewJobForm({
        job_title: fullJobDetails.job_title,
        company_name: fullJobDetails.company_name,
        year: fullJobDetails.year || new Date().getFullYear(),
        start_date: formatDate(fullJobDetails.start_date),
        end_date: formatDate(fullJobDetails.end_date),
        job_type: fullJobDetails.job_type,
        experience_level: expLevel,
        min_salary: fullJobDetails.min_salary || '',
        max_salary: fullJobDetails.max_salary || '',
        send_cv_email: fullJobDetails.send_cv_email || '',
        main_topics: fullJobDetails.main_topics,
        sub_topics: (() => {
          try {
            // Try to parse sub_topics as JSON if it's a string
            if (!fullJobDetails.sub_topics) return [];
            
            if (typeof fullJobDetails.sub_topics === 'string') {
              // Check if it starts with [ which would indicate a likely JSON array
              if (fullJobDetails.sub_topics.trim().startsWith('[')) {
                return JSON.parse(fullJobDetails.sub_topics);
              } else {
                // If it's not a JSON array, treat it as a single item
                return [fullJobDetails.sub_topics];
              }
            } else if (Array.isArray(fullJobDetails.sub_topics)) {
              return fullJobDetails.sub_topics;
            } else {
              return [];
            }
          } catch (err) {
            console.warn("Error parsing sub_topics, using as single item:", err);
            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];
          }
        })(),
        job_description: fullJobDetails.job_description || '',
        job_post_image: fullJobDetails.job_post_image,
        job_post_thumbnail: fullJobDetails.job_post_thumbnail,
        company_logo: fullJobDetails.company_logo,
        company_logo_url: companyLogoUrl, // Store the company logo URL
        // Use default values for UI-only fields that don't exist in the database
        status: 'Active',
        hot: fullJobDetails.hot || false // Use actual hot value from database
      });
      
      setIsModalOpen(true);
      setError(null);
    } catch (err) {
      console.error("Error fetching job details:", err);
      setError(`Failed to load job details for ${job.position}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteJob = async (jobId) => {
    try {
      setLoading(true);
      await ApiService.jobs.delete(jobId);
      setJobs(jobs.filter(job => job.id !== jobId));
      setError(null);
      setShowDeleteConfirm(false);
      setJobToDelete(null);
    } catch (err) {
      console.error("Error deleting job:", err);
      setError("Failed to delete job");
    } finally {
      setLoading(false);
    }
  };

  // Function to show delete confirmation
  const confirmDelete = (job) => {
    setJobToDelete(job);
    setShowDeleteConfirm(true);
  };

  // Function to cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setJobToDelete(null);
  };

  const handleToggleStatus = async (jobId) => {
    try {
      setLoading(true);
      const jobToUpdate = jobs.find(job => job.id === jobId);
      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';
      
      // Since we don't have a status column in the database,
      // we'll just update the local state without sending to the backend
      // In a real application, you would add the status column to the database
      
      // Update local state only
      setJobs(jobs.map(job => 
        job.id === jobId 
          ? {...job, status: newStatus} 
          : job
      ));
      
      setError(null);
    } catch (err) {
      console.error("Error updating job status:", err);
      setError("Failed to update job status");
    } finally {
      setLoading(false);
    }
  };
    const openNewJobModal = () => {
    setIsModalOpen(true);
    setSelectedJob(null);
  };  
    const closeModal = () => {
    setIsModalOpen(false);
    setSelectedJob(null);
    // Reset form
    setNewJobForm({
      job_title: '',
      year: new Date().getFullYear(),
      start_date: getTodayFormatted(),
      end_date: '',
      send_cv_email: '',
      main_topics: 'Government Jobs', // Ensure we always have a valid value
      sub_topics: [],
      job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'
      experience_level: 'Entry Level',
      min_salary: '',
      max_salary: '',
      job_description: '',
      job_post_image: null,
      job_post_thumbnail: null,
      company_name: '',
      company_logo: null,
      // UI-only fields (not in database)
      status: 'Active',
      hot: false
    });
    // Reset previews
    setJobImagePreview(null);
    setThumbnailPreview(null);
    setLogoPreview(null);
    setShowCompanyDropdown(false);
  };
  
  // Basic client-side text sanitization
  const sanitizeInputText = (text) => {
    if (!text) return '';
    // Modified to preserve Unicode characters in the job title
    // Only remove control characters and potentially problematic chars
    return text
      .replace(/[--]/g, '') // Remove control characters
      .replace(/[&<>"'`=\/]/g, '') // Remove potentially harmful characters
      .trim();
  };
  
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Apply sanitization to text fields that might contain problematic characters
    if (name === 'job_title' || name === 'job_description') {
      setNewJobForm({
        ...newJobForm,
        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience
      });
    } else {
    setNewJobForm({
      ...newJobForm,
      [name]: type === 'checkbox' ? checked : value
    });
    }
  };
  
  const handleImageUpload = (e, imageType) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        if (imageType === 'job_post_image') {
          setJobImagePreview(reader.result);
          setNewJobForm({ ...newJobForm, job_post_image: file });
        } else if (imageType === 'job_post_thumbnail') {
          setThumbnailPreview(reader.result);
          setNewJobForm({ ...newJobForm, job_post_thumbnail: file });
        } else if (imageType === 'company_logo') {
          setLogoPreview(reader.result);
          setNewJobForm({ ...newJobForm, company_logo: file });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSelectCompany = (company) => {
    console.log("Company selected:", company);
    
    // Validate company has a name
    if (!company || !company.company_name) {
      console.error("Invalid company selected");
      return;
    }
    
    // Set company name in the form
    setNewJobForm({
      ...newJobForm,
      company_name: company.company_name,
      company_logo: null, // Reset the file input since we're using an existing logo
      company_logo_url: company.company_logo_url // Store the logo URL
    });
    
    // Set logo preview
    if (company.company_logo_url) {
      setLogoPreview(company.company_logo_url);
    } else {
      setLogoPreview(null);
    }
    
    // Close the dropdown
    setShowCompanyDropdown(false);
  };
  
  // Add function to remove logo
  const handleRemoveLogo = () => {
    setLogoPreview(null);
    // If there was a logo file in the form, clear it
    if (newJobForm.company_logo) {
      setNewJobForm({
        ...newJobForm,
        company_logo: null
      });
    }
  };
  
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    
    // Debug the form state
    console.log("=== DEBUG: FORM SUBMISSION START ===");
    console.log("Original form state:", newJobForm);
    console.log("Selected job:", selectedJob);
    
    // Client-side validation for required fields
    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {
      setError("Job title is required");
      return;
    }
    
    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {
      setError("Company name is required");
      return;
    }

    // Validate email/URL field if provided
    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+(\/.*)?$/;
      
      const value = newJobForm.send_cv_email.trim();
      if (!emailRegex.test(value) && !urlRegex.test(value)) {
        setError("Please enter a valid email address or website URL");
        return;
      }
    }

    // Fix: If experience_level is empty, set to "No Experience Required"
    let experienceLevelToSend = newJobForm.experience_level;
    if (!experienceLevelToSend) {
      experienceLevelToSend = "No Experience Required";
    }
    
    try {
      setLoading(true);
      
      // Create a sanitized copy of the form data to send to the server
      console.log("Job title before sanitization:", newJobForm.job_title);
      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);
      console.log("Job title after sanitization:", sanitizedTitle);
      
      const sanitizedForm = {
        ...newJobForm,
        job_title: sanitizedTitle,
        job_description: sanitizeInputText(newJobForm.job_description),
        experience_level: experienceLevelToSend,
        // For new jobs, set today's date as the start_date if not provided
        start_date: selectedJob ? newJobForm.start_date : (newJobForm.start_date || getTodayFormatted())
      };
      
      console.log("Sanitized form data:", sanitizedForm);
      
      // Prepare form data for API - create a completely fresh FormData object
      const formData = new FormData();
        // Add only fields that exist in the database schema, ensuring each field is only added once
      const dbFields = [
        'job_title', 'company_name', 'year', 'start_date', 'end_date',
        'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level',
        'min_salary', 'max_salary', 'job_description', 'hot'
      ];
      
      // Add text fields one by one to avoid duplicates
      dbFields.forEach(key => {
        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {
          // Special handling for sub_topics
          if (key === 'sub_topics') {
            try {
              if (Array.isArray(sanitizedForm[key])) {
                // Ensure we're sending a clean array without unexpected characters
                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());
                formData.append(key, JSON.stringify(cleanSubTopics));
              } else {
                // If it's a string or something else, convert to array
                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));
              }
            } catch (err) {
              console.error("Error formatting sub_topics:", err);
              formData.append(key, '[]'); // Fallback to empty array
            }
          } else {
            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);
            formData.append(key, sanitizedForm[key]);
          }
        }
      });
      
      // Add file fields if they exist
      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {
        formData.append('job_post_image', sanitizedForm.job_post_image);
      }
      
      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {
        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);
      }      
      
      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {
        formData.append('company_logo', sanitizedForm.company_logo);
      }
      
      // If we have a logo preview from an existing company but no file, add the URL
      if (logoPreview && !sanitizedForm.company_logo) {
        formData.append('existing_company_logo_url', logoPreview);
      }
      
      // Add company_logo_url if it exists
      if (sanitizedForm.company_logo_url) {
        formData.append('company_logo_url', sanitizedForm.company_logo_url);
      }
      
      // Debug: Log what's being sent in the FormData
      console.log("FormData contents:");
      for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
      }
      console.log("Job title being submitted:", formData.get('job_title'));
      
      // Prepare form data for submission
      if (selectedJob) {
        // Update existing job
        try {
          await ApiService.jobs.update(selectedJob.id, formData);
          // Successfully updated job
        } catch (apiError) {
          // Handle update error silently
          throw apiError; // Re-throw to be caught by the outer try/catch
        }
        
        // Refresh jobs list
        const jobsResponse = await ApiService.jobs.getAll();
        const formattedJobs = jobsResponse.data.map(job => ({
          id: job.job_id,
          hot: job.hot || false, // Use actual hot value from database
          company: job.company_name,
          position: job.job_title,
          location: job.main_topics,
          workTime: job.job_type,
          experience_level: job.experience_level,
          cv_email: job.send_cv_email,
          salary: (job.min_salary && job.max_salary)
            ? `${job.min_salary} - ${job.max_salary}`
            : job.min_salary
              ? `${job.min_salary}`
              : job.max_salary
                ? `${job.max_salary}`
                : '',
          status: 'Active', // Default since it doesn't exist in DB
          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : 
                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',
          views: job.view_count || 0, // Use actual view count if available
          end_date: job.end_date // Ensure end_date is included
        }));
        
        // Sort jobs by datePosted descending (newest first)
        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));
        setJobs(sortedJobs);
      } else {
        // Create new job
        try {
          await ApiService.jobs.create(formData);
          // Successfully created job
        } catch (apiError) {
          // Log detailed error information
          console.error("API Error Details:", {
            message: apiError.message,
            status: apiError.response?.status,
            statusText: apiError.response?.statusText,
            data: apiError.response?.data
          });
          throw apiError; // Re-throw to be caught by the outer try/catch
        }
        
        // Refresh jobs list
        const jobsResponse = await ApiService.jobs.getAll();
        const formattedJobs = jobsResponse.data.map(job => ({
          id: job.job_id,
          hot: job.hot || false, // Use actual hot value from database
          company: job.company_name,
          position: job.job_title,
          location: job.main_topics,
          workTime: job.job_type,
          experience_level: job.experience_level,
          cv_email: job.send_cv_email,
          salary: (job.min_salary && job.max_salary)
            ? `${job.min_salary} - ${job.max_salary}`
            : job.min_salary
              ? `${job.min_salary}`
              : job.max_salary
                ? `${job.max_salary}`
                : '',
          status: 'Active', // Default since it doesn't exist in DB
          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : 
                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',
          views: job.view_count || 0, // Use actual view count if available
          end_date: job.end_date // Ensure end_date is included
        }));
        
        // Sort jobs by datePosted descending (newest first)
        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));
        setJobs(sortedJobs);
      }
      
      setError(null);
      // Close modal
      closeModal();
    } catch (err) {
      console.error("Error saving job:", err);
      // Show more detailed error message if available
      const errorMessage = err.response?.data?.message || 
                          err.response?.data?.error || 
                          (selectedJob ? "Failed to update job" : "Failed to create job");
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = (job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase())) || 
                          (job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesSearch;
  });

  // Add this useEffect to handle closing the dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdownElement = document.querySelector('.company-select-dropdown');
      if (dropdownElement && !dropdownElement.contains(event.target)) {
        setShowCompanyDropdown(false);
      }
    };

    if (showCompanyDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showCompanyDropdown]);

  return (
    <div className="jobs-admin-container">
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="delete-confirm-overlay">
          <div className="delete-confirm-dialog">
            <div className="delete-confirm-header">
              <FontAwesomeIcon icon={faExclamationTriangle} className="delete-icon" />
              <h3>Confirm Deletion</h3>
            </div>
            <div className="delete-confirm-content">
              <p>Are you sure you want to delete this job posting?</p>
              <p><strong>{jobToDelete?.position}</strong></p>
              <p>This action cannot be undone.</p>
            </div>
            <div className="delete-confirm-actions">
              <button 
                className="cancel-delete-btn" 
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button 
                className="confirm-delete-btn" 
                onClick={() => handleDeleteJob(jobToDelete.id)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className="jobs-header">
        <h1 className="jobs-title">Job Listings</h1>
        <div className="job-header-actions">
          <button className="refresh-button" onClick={() => {
            setLoading(true);
            const fetchJobs = async () => {
              try {
                const response = await ApiService.jobs.getAll();
                
                // Transform backend data to match the format used in frontend
                const formattedJobs = response.data.map(job => {
                  // Determine the best date to use
                  let dateToUse;
                  if (job.created_at) {
                    dateToUse = job.created_at;
                  } else if (job.start_date) {
                    dateToUse = job.start_date;
                  } else if (job.posted_date) {
                    dateToUse = job.posted_date;
                  } else {
                    dateToUse = new Date().toISOString();
                  }

                  // Compose salary display string
                  let salaryDisplay = '';
                  if (job.min_salary && job.max_salary) {
                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;
                  } else if (job.min_salary) {
                    salaryDisplay = `${job.min_salary}`;
                  } else if (job.max_salary) {
                    salaryDisplay = `${job.max_salary}`;
                  } else {
                    salaryDisplay = '';
                  }
                    return {
                    id: job.job_id,
                    hot: job.hot || false,
                    company: job.company_name,
                    position: job.job_title,
                    location: job.main_topics,
                    workTime: job.job_type,
                    experience_level: job.experience_level,
                    cv_email: job.send_cv_email,
                    salary: salaryDisplay,
                    status: 'Active',
                    datePosted: dateToUse,
                    rawDatePosted: job.start_date || job.posted_date || job.created_at,
                    views: job.view_count || 0,
                    end_date: job.end_date
                  };
                });
                
                // Sort jobs by datePosted descending (newest first)
                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));
                setJobs(sortedJobs);
                setError(null);
              } catch (err) {
                console.error("Error fetching jobs:", err);
                setError("Failed to load jobs from server");
              } finally {
                setLoading(false);
              }
            };

            fetchJobs();
          }} disabled={loading}>
            <FontAwesomeIcon icon={faSync} />
            <span>Refresh</span>
          </button>
          <button className="add-job-button" onClick={openNewJobModal} disabled={loading}>
            <FontAwesomeIcon icon={faPlus} />
            <span>Add New Job</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <FontAwesomeIcon icon={faExclamationCircle} />
          <span>{error}</span>
        </div>
      )}

      <div className="filters-container">
        <div className="search-container">
          <div className="search-input-wrapper">
            <FontAwesomeIcon icon={faSearch} className="search-icon" />          <input
            type="text"
            placeholder="Search by company or position..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
            style={{ paddingLeft: '40px' }}
          />
          </div>
        </div>

      </div>

      <div className="table-container">
        {loading ? (
          <div className="loading-container">
            <FontAwesomeIcon icon={faSpinner} spin size="2x" />
            <span>Loading jobs...</span>
          </div>
        ) : (
          <>
          <table className="jobs-table">
            <thead>
              <tr>
                <th>Job</th>
                <th>Type</th>
                <th>Experience Level</th>
                <th>Email / Web URL</th>
                <th>Salary</th>
                <th>Expire Date</th>
                <th>Posted Date</th>
                <th>Views</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredJobs.length > 0 ? (
                filteredJobs.map(job => (
                  <tr key={job.id}>
                    <td>
                      {job.hot && (
                        <span className="hot-badge">
                          <FontAwesomeIcon icon={faFire} style={{ marginRight: '4px' }} />
                          URGENT
                        </span>
                      )}
<div className="job-position" title={job.position}>
  {job.position && job.position.length > 35
    ? job.position.slice(0, 32) + '...'
    : job.position}
</div>
                      <div className="job-company">{job.company}</div>
                    </td>
                    <td>{job.workTime}</td>
                    <td>
                      <div className="experience-level" title={job.experience_level}>
                        {job.experience_level || 'Not specified'}
                      </div>
                    </td>
                    <td>
                      <div className="cv-email" title={job.cv_email}>
                        {job.cv_email ? (
                          <a href={`mailto:${job.cv_email}`} className="email-link">
                            {job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </div>
                    </td>
                    <td>{job.salary}</td>
                    <td>{formatDisplayDate(job.end_date)}</td>
                      <td>{formatDisplayDate(job.datePosted)}</td>
                    <td>
                      <div className="views-container">
                        <FontAwesomeIcon icon={faEye} className="views-icon" />
                        {job.views}
                      </div>
                    </td>
                    <td>
                      <div style={{ display: 'flex' }}>
                        <button 
                          className="action-button edit-button" 
                          onClick={() => handleEditJob(job)}
                          title="Edit job"
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </button>
                        {/* Removed Inactive/Active toggle button */}
                        <button 
                          className="action-button delete-button" 
                          onClick={() => confirmDelete(job)}
                          title="Delete job"
                          disabled={loading}
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="9" className="no-jobs-message">
                    {searchTerm ? 
                      "No jobs match your search criteria" : 
                      "No jobs available"
                    }
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          </>
        )}
      </div>

      {/* New Job Modal */}
      {isModalOpen && (
        <div className="modal-overlay" onClick={(e) => {
          // Close modal when clicking outside
          if (e.target.className === 'modal-overlay') {
            closeModal();
          }
        }}>
          <div className="job-modal">
            <div className="modal-header">
              <h2>{selectedJob ? 'Edit Job' : 'Add New Job'}</h2>
              <button className="close-button" onClick={closeModal}>
                <FontAwesomeIcon icon={faTimes} />
              </button>
            </div>
            <div className="modal-content">
              <form onSubmit={handleFormSubmit}>
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="job_title">Job Title <span style={{ color: 'red' }}>*</span></label>
                    <input
                      type="text"
                      id="job_title"
                      name="job_title"
                      value={newJobForm.job_title}
                      onChange={handleFormChange}
                      required
                      placeholder="Enter job title"
                    />
                    </div>
                  <div className="form-group">
                    <label>Company <span style={{ color: 'red' }}>*</span></label>
                    <div className="company-selector">
                      <div className="company-select-dropdown">
                        <button 
                          type="button"
                          className="company-select-button"
                          onClick={() => setShowCompanyDropdown(!showCompanyDropdown)}
                        >
                          {newJobForm.company_name ? newJobForm.company_name : 'Select a company'}
                          <FontAwesomeIcon icon={faChevronDown} />
                        </button>
                        
                        {showCompanyDropdown && (
                          <div className="company-list-dropdown">
                            {companies && companies.length > 0 ? (
                              companies.map(company => (
                                <div 
                                  key={company.company_id}
                                  className="company-item"
                                  onClick={() => {
                                    handleSelectCompany(company);
                                  }}
                                >
                                  <div className="company-item-logo">
                                    {company.company_logo_url ? (
                                      <img src={company.company_logo_url} alt={company.company_name} />
                                    ) : (
                                      <FontAwesomeIcon icon={faBuilding} />
                                    )}
                                  </div>
                                  <span>{company.company_name}</span>
                                </div>
                              ))
                            ) : (
                              <div className="no-companies">No companies available</div>
                            )}
                            <div className="manual-company-entry">
                              <label>Or enter company name manually:</label>
                              <input
                                type="text"
                                placeholder="Enter company name"
                                value={newJobForm.company_name || ''}
                                onChange={(e) => {
                                  setNewJobForm({
                                    ...newJobForm,
                                    company_name: e.target.value
                                  });
                                }}
                              />
                              <button
                                type="button"
                                className="apply-company-btn"
                                onClick={() => setShowCompanyDropdown(false)}
                              >
                                Apply
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Display logo preview if available */}
                      {logoPreview && (
                        <div className="logo-preview-container">
                          <img src={logoPreview} alt="Company logo" />
                          <button 
                            type="button" 
                            className="remove-logo-button"
                            onClick={handleRemoveLogo}
                          >
                            <FontAwesomeIcon icon={faTimes} />
                          </button>
                        </div>
                      )}
                      
                      {/* Allow uploading a new logo if no existing one is selected */}
                      {!logoPreview && (
                        <div>
                          <input
                            type="file"
                            id="company_logo"
                            name="company_logo"
                            onChange={(e) => handleImageUpload(e, 'company_logo')}
                            accept="image/*"
                            style={{ display: 'none' }}
                          />
                          <button
                            type="button"
                            className="file-upload-label"
                            onClick={() => document.getElementById('company_logo').click()}
                          >
                            <FontAwesomeIcon icon={faImage} />
                            Upload Company Logo
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="year">Year</label>
                    <input
                      type="number"
                      id="year"
                      name="year"
                      value={newJobForm.year}
                      onChange={handleFormChange}
                      required
                      min="2000"
                      max="2100"
                    />
                  </div>                  <div className="form-group">
                    <label htmlFor="job_type">Job Type</label>
                    <select
                      id="job_type"
                      name="job_type"
                      value={newJobForm.job_type}
                      onChange={handleFormChange}
                      required
                    >
                      <option value="Full Time Jobs">Full Time Jobs</option>
                      <option value="Part Time Jobs">Part Time Jobs</option>
                      <option value="Remote Jobs">Remote Jobs</option>
                      <option value="Freelance">Freelance</option>
                      <option value="Temporary">Temporary</option>
                    </select>
                  </div>
                  <div className="form-group">
                    <label htmlFor="experience_level">Experience Level</label>
                    <select
                      id="experience_level"
                      name="experience_level"
                      value={newJobForm.experience_level}
                      onChange={handleFormChange}
                      required
                    >
                      {EXPERIENCE_OPTIONS.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="start_date">Start Date</label>
                    <input
                      type="date"
                      id="start_date"
                      name="start_date"
                      value={newJobForm.start_date}
                      onChange={handleFormChange}
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="end_date">End Date</label>
                    <input
                      type="date"
                      id="end_date"
                      name="end_date"
                      value={newJobForm.end_date}
                      onChange={handleFormChange}
                      required
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="send_cv_email">Email/WebURL</label>
                    <input
                      type="text"
                      id="send_cv_email"
                      name="send_cv_email"
                      value={newJobForm.send_cv_email}
                      onChange={handleFormChange}
                      placeholder="Enter email or website URL for CV submissions (Optional)"
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="min_salary">Minimum Salary</label>
                    <input
                      type="number"
                      id="min_salary"
                      name="min_salary"
                      value={newJobForm.min_salary}
                      onChange={handleFormChange}
                      placeholder="Minimum salary"
                      min="0"
                      step="any"
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="max_salary">Maximum Salary</label>
                    <input
                      type="number"
                      id="max_salary"
                      name="max_salary"
                      value={newJobForm.max_salary}
                      onChange={handleFormChange}
                      placeholder="Maximum salary"
                      min="0"
                      step="any"
                    />
                  </div>
                </div>

                {/* Mark as URGENT Job - Moved to middle */}
                <div className="form-row">
                  <div className="form-group checkbox-group" style={{textAlign: 'left'}}>
                    <label className="checkbox-container">
                      <input
                        type="checkbox"
                        name="hot"
                        checked={newJobForm.hot}
                        onChange={handleFormChange}
                      />
                      <span className="checkbox-text">
                        <FontAwesomeIcon icon={faFire} className="hot-icon" />
                        Mark as URGENT Job
                      </span>
                    </label>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="main_topics">Main Topic/Category</label>
                    <select
                      id="main_topics"
                      name="main_topics"
                      value={newJobForm.main_topics}
                      onChange={handleFormChange}
                      required
                    >
                      <option value="Government Jobs">Government Jobs</option>
                      <option value="Private Jobs">Private Jobs</option>
                      <option value="Foreign Jobs">Foreign Jobs</option>
                      <option value="Internships">Internships</option>
                    </select>
                  </div>
                </div>                <div className="form-row">
                  <div className="form-group full-width">
<label>
  <span>Sub Topics</span>
  <span className="small-text">{newJobForm.sub_topics.length} selected</span>
  <button
    type="button"
    style={{ marginLeft: '16px', padding: '2px 8px', fontSize: '0.9em' }}
    onClick={() => setNewJobForm(prev => ({ ...prev, sub_topics: [] }))}
  >
    Clear All
  </button>
  <button
    type="button"
    style={{ marginLeft: '8px', padding: '2px 8px', fontSize: '0.9em', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '3px' }}
    onClick={loadSubTopicsFromStorage}
    title="Refresh sub-topics list"
  >
    <FontAwesomeIcon icon={faSync} style={{ marginRight: '4px' }} />
    Refresh
  </button>
</label>
<div className="subtopics-management-info">
  <small style={{ color: '#6c757d', fontStyle: 'italic' }}>
    💡 Tip: You can manage available sub-topics from the "Sub Topics" section in the sidebar.
    Click "Refresh" if you don't see newly added sub-topics.
  </small>
</div>
<div className="subtopics-checkbox-container">
  {availableSubTopics.map((topic, index) => (
                        <div 
                          key={index} 
                          className={`subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`}
                        >
<input
  type="checkbox"
  id={`subtopic-${index}`}
  checked={newJobForm.sub_topics.includes(topic)}
  onChange={e => {
    const checked = e.target.checked;
    setNewJobForm(prev => {
      const set = new Set(prev.sub_topics);
      if (checked) {
        set.add(topic);
      } else {
        set.delete(topic);
      }
      return {
        ...prev,
        sub_topics: Array.from(set)
      };
    });
  }}
                          />
                          <label htmlFor={`subtopic-${index}`}>
                            {topic}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group full-width">
                    <label htmlFor="job_description">Job Description</label>
                    <textarea
                      id="job_description"
                      name="job_description"
                      rows="5"
                      value={newJobForm.job_description}
                      onChange={handleFormChange}
                      required
                      placeholder="Enter detailed job description"
                    ></textarea>
                    <small className="form-help-text">
                      Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.
                    </small>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Job Post Image</label>
                    <div className="file-upload-container">
                      <input
                        type="file"
                        id="job_post_image"
                        onChange={(e) => handleImageUpload(e, 'job_post_image')}
                        accept="image/*"
                        className="file-input"
                      />
                      <label htmlFor="job_post_image" className="file-upload-label">
                        <FontAwesomeIcon icon={faImage} />
                        <span>Choose Image</span>
                      </label>
                    </div>
                    {jobImagePreview && (
                      <div className="image-preview">
                        <img src={jobImagePreview} alt="Job Post" />
                      </div>
                    )}
                  </div>
                  <div className="form-group">
                    <label>Job Post Thumbnail</label>
                    <div className="file-upload-container">
                      <input
                        type="file"
                        id="job_post_thumbnail"
                        onChange={(e) => handleImageUpload(e, 'job_post_thumbnail')}
                        accept="image/*"
                        className="file-input"
                      />
                      <label htmlFor="job_post_thumbnail" className="file-upload-label">
                        <FontAwesomeIcon icon={faImage} />
                        <span>Choose Thumbnail</span>
                      </label>
                    </div>
                    {thumbnailPreview && (
                      <div className="thumbnail-preview">
                        <img src={thumbnailPreview} alt="Thumbnail" />
                      </div>
                    )}
                  </div>
                </div>



                <div className="modal-footer">
                  <button type="button" className="cancel-button" onClick={closeModal}>
                    Cancel
                  </button>
                  <button type="submit" className="submit-button">
                    {selectedJob ? 'Update Job' : 'Create Job'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JobsAdmin;
