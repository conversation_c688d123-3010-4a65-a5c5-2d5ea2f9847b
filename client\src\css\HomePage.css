/* HomePage.css */
.homepage-container {
  background-color: #ffffff;
  font-family: 'Inter', sans-serif;
  width: 100%;
  max-width: 100%;
  padding: 0;
  overflow-x: hidden;
  box-sizing: border-box;
  position: relative; /* Ensure proper stacking context */
}

.content-spacing {
  height: 80px; /* Space for the fixed navbar */
  width: 100%;
}

/* Full width sections for SearchArea and Banner */
.homepage-search-area,
.homepage-top-banner,
.homepage-newsletter,
.homepage-banner {
  width: 100%;
  display: block;
}

/* SearchArea wrapper - no special handling needed */
.homepage-search-area,
.homepage-top-banner,
.homepage-newsletter,
.homepage-banner {
  width: 100%;
  display: block;
}

/* Adjust homepage container to ensure proper flow */
.homepage-container {
  position: relative;
}

/* Add horizontal padding to HomePage content (excluding SearchArea) */
.homepage-container > *:not(.homepage-search-area) {
  padding-left: 200px;
  padding-right: 200px;
  box-sizing: border-box;
}

.home-content-wrapper {
  max-width: 96%;
  margin: 0 auto 0 2%;
  padding: 0 15px;
  /* Remove the added padding from this wrapper since parent handles it */
  padding-left: 0;
  padding-right: 0;
  margin: 0;
  max-width: none;
}

/* Header styles */
.home-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #eaeaea;
}

.main-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.header-tabs {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

.tab {
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
  color: #666;

}

.tab.active {
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #7743DB;
  background: none;
  border-radius: 0;
}

/* All Jobs tab special active style */
.tab.all-active {
  background-color: #00C2FF !important;
  color: #fff !important;
  border-radius: 25px;
  border-bottom: 2px solid #00C2FF !important;
  font-weight: 700;
}

/* Government Jobs tab special active style */
.tab.gov-active {
  background-color: #FECA16 !important;
  color: #222 !important;
  border-radius: 25px;
  border-bottom: 2px solid #FECA16 !important;
  font-weight: 700;
}

/* Private Jobs tab special active style */
.tab.private-active {
  background-color: #9777FA !important;
  color: #fff !important;
  border-radius: 25px;
  border-bottom: 2px solid #9777FA !important;
  font-weight: 700;
}

/* Foreign Jobs tab special active style */
.tab.foreign-active {
  background-color: #FF2D55 !important;
  color: #fff !important;
  border-radius: 25px;
  border-bottom: 2px solid #FF2D55 !important;
  font-weight: 700;
}

/* Internships tab special active style */
.tab.internships-active {
  background-color: #29DCB2 !important;
  color: #fff !important;
  border-radius: 25px;
  border-bottom: 2px solid #29DCB2 !important;
  font-weight: 700;
}

/* Main 3-column layout */
.home-layout {
  display: grid;
  grid-template-columns: 220px 1fr 370px; /* Left, Middle, Right columns - Made right column wider, left smaller */
  gap: 20px;
  margin-bottom: 40px;
  margin-left: 0;
}

/* Left column - Urgent Jobs */
.left-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.urgent-job-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.urgent-job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.urgent-job-image {
  width: 100%;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
  position: relative;
  background-color: #f5f5f5;
}

.urgent-job-image img {
  object-fit: contain;
  object-position: center;
  display: block;
  margin: 0 auto;
  transition: transform 0.3s ease;
}

.urgent-job-card:hover .urgent-job-image img {
  transform: scale(1.05);
}

.hot-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #ff6b6b;
  color: #fff;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
}

.job-company-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.company-logo {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 10px;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-details {
  display: flex;
  flex-direction: column;
}

.company-details h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.verified-badge {
  font-size: 12px;
  color: #7743DB;
  font-weight: 500;
}

.job-position {
  font-size: 16px;
  font-weight: 700;
  margin: 8px 0;
  color: #333;
  line-height: 1.4;
}

.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #666;
}

.job-meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.job-footer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.job-salary {
  font-weight: 700;
  color: #7743DB;
  font-size: 16px;
}

.job-posted {
  font-size: 12px;
  color: #888;
}

.job-stats {
  font-size: 12px;
  color: #888;
}

/* Recruiting box */
.recruiting-box {
  background-color: #eaf2ff;
  padding: 12px;
  border-radius: 12px;
  text-align: center;
  width: 250px;
  height: 280px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recruiting-box h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 6px;
  color: #333;
  line-height: 1.2;
}

.recruiting-box p {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.3;
  flex-grow: 1;
}

.recruiting-image {
  width: 100%;
  height: 140px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 10px;
}

/* Ensure recruiting box image fills properly on mobile */
@media (max-width: 768px) {
  .recruiting-image {
    height: 150px;
    object-fit: cover;
    margin-bottom: 12px;
  }
  
  .post-job-btn {
    padding: 10px 12px;
    font-size: 14px;
    margin-top: auto;
  }
}

.post-job-btn {
  background-color: #7743DB;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 13px;
  margin-top: auto;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.post-job-btn:hover {
  background-color: #6234c7;
}

/* Middle column - Regular Jobs */
.middle-column {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.job-listing-card {
  display: flex;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  position: relative;
  transition: all 0.3s ease;
  height: 200px;
  align-items: stretch;
  width: 100%;
}

.job-listing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.job-image {
  width: 160px;
  min-width: 160px;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.job-image img {
  width: auto;
  height: auto;
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  object-position: center;
  display: block;
  margin: 0 auto;
}

.job-details {
  padding: 12px 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* .job-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 0.5px;
  color: #333;
} */

.job-description p {
  font-size: 14px;
  color: #666;
  margin: 0 0 0.5px;
  line-height: 1.35;
}

.job-meta-row {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 13px;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.job-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.apply-btn, .details-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
}

.apply-btn {
  background-color: #7743DB;
  color: white;
  border: none;
}

.details-btn {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.salary-and-time {
  margin-left: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.job-salary-inline {
  font-weight: 700;
  color: #7743DB;
  font-size: 16px;
  margin-bottom: 2px;
}

.time-posted {
  font-size: 12px;
  color: #888;
}

/* Remove the old salary tag positioning */
.job-salary-tag {
  display: none;
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-top: 20px;
}

.page-arrow, .page-number {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 5px;
  cursor: pointer;
}

.page-number.active {
  background-color: #7743DB;
  color: white;
  border-color: #7743DB;
}

.page-info {
  margin-left: 10px;
  font-size: 13px;
  color: #666;
}

/* Right column - Categories */
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-box {
  display: flex;
  background-color: #fff;
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.search-input {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  color: #aaa;
}

.search-input input {
  width: 100%;
  border-radius: 6px;
  font-size: 14px;
}

.filter-btn {
  width: 40px;
  height: 40px;
  background-color: #7743DB;
  color: white;
  border: none;
  border-radius: 6px;
  margin-left: 10px;
  cursor: pointer;
}

.categories-box, .featured-employers {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.categories-box h3, .featured-employers h3 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 15px;
  color: #333;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  color: white;
  height: 80px;
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.category-item.blue { border-left: 3px solid #4285f4; }
.category-item.green { border-left: 3px solid #34a853; }
.category-item.orange { border-left: 3px solid #fbbc05; }
.category-item.purple { border-left: 3px solid #7743DB; }
.category-item.red { border-left: 3px solid #ea4335; }

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.category-count {
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 10px;
  border-radius: 20px;
  color: white;
}

.employer-logos {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.employer-logos img {
  width: 100%;
  height: 60px;
  object-fit: contain;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 5px;
}

/* Featured section */
.featured-section {
  margin: 40px 0;
}

/* FeaturedSection component background in HomePage */
.home-content-wrapper .featured-container {
  background-color: #f5f5f5; /* Ash color */
  padding: 30px;
  border-radius: 8px;
  margin: 20px 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  margin-right: 15px;
  color: #333;
}

.header-line {
  flex: 1;
  height: 2px;
  background-color: #eaeaea;
}

.featured-articles {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.article-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.article-image {
  position: relative;
  height: 180px;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-category {
  position: absolute;
  bottom: 15px;
  left: 15px;
  background-color: #7743DB;
  color: white;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.article-content {
  padding: 15px;
}

.article-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
  line-height: 1.4;
}

/* Newsletter and Banner sections */
.homepage-newsletter {
  padding: 40px 0;
  background-color: #f5f0ff;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.homepage-banner {
  padding: 30px 0;
  background-color: #ffffff;
  width: 100%;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .home-layout {
    grid-template-columns: 220px 1fr 0px;
    gap: 15px;
  }
  
  .right-column {
    display: none;
    flex-direction: column;
    gap: 15px;
  }
.filter-toggle-btn {
  display: block !important ;
  background-color: #7743DB;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;
}
}

@media (max-width: 1615px) {
  .homepage-container > *:not(.homepage-search-area) {
    padding-left: 60px;
    padding-right: 60px;
    box-sizing: border-box;
}


}

@media (max-width: 768px) {
  .home-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 10px 0;
  }

  .home-content-wrapper {
    max-width: 100%;
    margin: 0;
    padding: 0 5px;
  }

  .home-layout {
    grid-template-columns: 1fr;
    gap: 10px;
    margin-bottom: 20px;
  }  .left-column,
  .middle-column {
    width: 100%;
    max-width: 100%;
    padding: 0 10px;
    margin: 25px 0;
    gap: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  /* Hide right column (filters) in mobile view - filters should only be accessible via filter button */
  .right-column {
    display: none;
  }
  .urgent-job-card,
  .job-card-redesigned {
    min-width: 0;
    width: 100%;
    max-width: 320px;
    margin: 15px auto;
    margin-bottom: 20px;
    min-height: unset;
    box-sizing: border-box;
    align-self: center;
    margin: 0 auto;
    width: 90%;
  }

  .urgent-job-image,
  .job-image-container {
    width: 100%;
    height: 160px ;
    margin-bottom: 10px;
    margin-top: 0;
  }

  .job-listing-card {
    flex-direction: column;
    height: auto;
    min-height: unset;
  }

  .job-card-content {
    flex-direction: column;
    gap: 10px;
    padding: 12px 10px;
  }

  .job-main-content {
    width: 100%;
  }

  .job-title-redesigned {
    font-size: 17px;
    margin-bottom: 8px;
  }

  .job-meta-info {
    gap: 6px;
    margin-bottom: 10px;
  }

  .job-description-redesigned {
    font-size: 13px;
    -webkit-line-clamp: 4;
  }

  .job-card-footer {
    flex-direction: column;
    gap: 10px;
    padding: 10px 10px;
  }

  .job-action-buttons {
    width: 100%;
    justify-content: center;
    gap: 6px;
  }

  .apply-section {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .apply-btn-redesigned {
    width: 100%;
    font-size: 14px;
    padding: 9px 0;
  }

  .urgent-job-card {
    min-height: unset;
    padding: 10px;
  }  .recruiting-box {
    width: 100%;
    max-width: 320px;
    height: 280px;
    padding: 12px;
    margin: 15px auto;
    align-self: center;
    display: flex;
    flex-direction: column;
  }

  .subtopics-box {
    padding: 10px;
    margin-bottom: 10px;
  }

  .featured-articles {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

@media (max-width: 576px) {
  .homepage-container {
    padding-left: 0px;
    padding-right: 0px;
  }

  .home-header {
    padding: 7px 0;
  }

  .main-title {
    font-size: 18px;
  }

  .header-tabs {
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
  }

  .tab {
    font-size: 12px;
    padding: 4px 7px;
  }

  .job-title-redesigned {
    font-size: 15px;
  }

  .job-meta-info {
    font-size: 11px;
    gap: 4px;
  }

  .job-description-redesigned {
    font-size: 12px;
    -webkit-line-clamp: 5;
  }

  .job-card-content,
  .job-card-footer {
    padding: 7px 4px;
  }

  .job-action-btn {
    font-size: 12px;
    padding: 6px 8px;
  }

  .apply-btn-redesigned {
    font-size: 13px;
    padding: 8px 0;
  }

  .urgent-job-image,
  .job-image-container {
    height: 110px;
  }  
  .recruiting-box,
  .subtopics-box {
    width: 100%;
    max-width: 320px;
    height: 260px;
    padding: 8px;
    margin: 15px auto;
  }

  .job-meta-row {
    flex-direction: column;
    gap: 5px;
  }
    .urgent-job-card,
  .job-card-redesigned {
    min-width: 0;
    width: 100%;
    max-width: 320px;
    margin: 15px auto;
    margin-bottom: 20px;
    min-height: unset;
    box-sizing: border-box;
    align-self: center;
    margin: 0 auto;
    width: 90%;
  }
}

/* Subtopics filter box (replacing categories) */
.subtopics-box {
  /* background-color: #fff; */
  border-radius: 12px;
  padding: 0px;
  /* box-shadow: 0 2px 8px rgba(0,0,0,0.05); */
  margin-bottom: 20px;
}

.subtopics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.subtopics-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.clear-filters-btn {
  background: none;
  border: none;
  color: #6234c7;
  font-size: 13px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.clear-filters-btn:hover {
  text-decoration: underline;
}

.subtopics-list {
  /* overflow-y: auto; */
  margin-bottom: 15px;
  padding-right: 0px;
}

/* Custom scrollbar for the subtopics list */
.subtopics-list::-webkit-scrollbar {
  width: 6px;
}

.subtopics-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.subtopics-list::-webkit-scrollbar-thumb {
  background: #c4c4c4;
  border-radius: 10px;
}

.subtopics-list::-webkit-scrollbar-thumb:hover {
  background: #7743DB;
}

/* Subtopic checkbox styling - Clean design without borders */
.subtopic-checkbox {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background-color: transparent;
}

.subtopic-checkbox:hover {
  background-color: rgba(119, 67, 219, 0.05);
}

.subtopic-checkbox.selected {
  background-color: rgba(119, 67, 219, 0.08);
}

.subtopic-checkbox input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
  margin: 0;
  flex-shrink: 0;
}

.subtopic-checkbox input[type="checkbox"]:hover {
  border-color: #7743DB;
}

.subtopic-checkbox input[type="checkbox"]:checked {
  background-color: #7743DB;
  border-color: #7743DB;
}

.subtopic-checkbox input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  top: 1px;
  left: 4px;
  transform: rotate(45deg);
}

.subtopic-checkbox label {
  cursor: pointer;
  font-size: 12px;
  color: #374151;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  flex: 1;  
}

.subtopic-checkbox:hover label {
  color: #7743DB;
}

.subtopic-checkbox.selected label {
  color: #7743DB;
  font-weight: 500;
}

/* Selected filters display */
.selected-filters {
  border-top: 1px solid #eaeaea;
  padding-top: 15px;
}

.filters-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.selected-topics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-topic-tag {
  background-color: #f0e7ff;
  color: #6234c7;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.remove-topic {
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  color: #6234c7;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.remove-topic:hover {
  background-color: #dbc7ff;
}

.no-filters {
  color: #999;
  font-size: 14px;
  font-style: italic;
}

/* Updated job card styling to match Figma design */
.job-card-redesigned {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  margin: 0 auto;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  width: 90%;
  border: 1px solid #ddd;
}

.job-card-redesigned:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(119, 67, 219, 0.08);
}

.job-card-header {
  display: none; /* Hide the entire header since it's now empty */
}

.job-card-content {
  padding: 16px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.job-main-content {
  flex: 1;
}

.job-title-redesigned {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
}

.company-info-with-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  width: 100%;
}

.company-logo-small {
  width: 20px;
  height: 20px;
  object-fit: cover;
  border-radius: 3px;
  flex-shrink: 0;
}

.company-name {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  line-height: 1.2;
}

.job-title-redesigned a {
  color: #333;
  text-decoration: none;
  transition: color 0.2s;
}

.job-title-redesigned a:hover {
  color: #7743DB;
}

.job-meta-info {
  display: flex;
  flex-wrap: wrap;
  color: #333;
  gap: 10px;
  margin-bottom: 4px;
}

.job-tag {
  background-color: #f5f7ff;
  color: #333;
  font-size: 12px;
  padding: 5px 12px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.job-tag.location {
  background-color: #f0e7ff;
  color: #333;
}

.job-tag.work-type {
  background-color: #f5f0ff;
  color: #333;
}

.job-tag.salary {
  background-color: #efe6ff;
  color: #333;
  font-weight: 500;
}

.job-description-redesigned {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.job-image-container {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 10px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.job-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.job-card-redesigned:hover .job-thumbnail {
  transform: scale(1.05);
}

.job-card-footer {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f2f2f2;
  background-color: #fff;
  font-size: 13px;
}

/* Action buttons container */
.job-action-buttons {
  display: flex;
  gap: 8px;
}

/* Apply section with date and button */
.apply-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
}

.posting-date {
  font-size: 13px;
  color: #888;
  font-style: italic;
  white-space: nowrap;
}

/* Individual action buttons */
.job-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  color: #555;
  cursor: pointer;
  text-decoration: none;
}

.job-action-btn:hover {
  background-color: #f9f5fa;
  border-color: #e5d8f4;
}

.job-action-btn.save-btn, 
.job-action-btn.share-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 14px;
  color: #7743DB;
}

/* Keep the details button with text */
.job-action-btn.details-btn {
  padding: 8px 12px;
  color: #7743DB;
  border-color: #e5d8f4;
}

.job-action-btn.details-btn:hover {
  background-color: #f5f0ff;
}

/* Apply button */
.apply-btn-redesigned {
  background-color: #7743DB;
  color: white;
  border: none;
  padding: 10px 22px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.apply-btn-redesigned:hover {
  background-color: #6234c7;
}

/* Loading and no jobs states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #4a6bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-overlay {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  /* background-color: rgba(255, 255, 255, 0.95); */
  border-radius: 12px;
  margin: 20px 0;
  min-height: 200px;
}

.loading-overlay .spinner {
  font-size: 2rem;
  color: #4a6bff;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-jobs-redesigned {
  background-color: white;
  border-radius: 12px;
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); */
  padding: 40px 20px;
  text-align: center;
  margin: 0 auto;
}

.no-jobs-icon {
  font-size: 32px;
  color: #c7d2fe;
  margin-bottom: 16px;
}

.no-jobs-redesigned h3 {
  font-size: 18px;
  margin-bottom: 12px;
  color: #333;
}

.no-jobs-redesigned p {
  color: #666;
  margin-bottom: 8px;
}

/* Filter Toggle Button */
.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-toggle-btn {
  display: none ;
  background-color: #7743DB;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s;
}

.filter-toggle-btn:hover {
  background-color: #6234c7;
}

/* Filter Sidebar - Hidden on desktop, only shown on mobile/tablet */
.filter-sidebar {
  display: flex;
  position: fixed;
  top: 0;
  right: -400px;
  width: 350px;
  height: 100vh;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

/* Ensure filter sidebar is definitely hidden on desktop */
@media (min-width: 1025px) {
  .filter-sidebar,
  .filter-sidebar-overlay {
    display: block;
  }
}

.filter-sidebar.open {
  right: 0;
}

.filter-sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  /* border-bottom: 1px solid #eaeaea; */
}

.filter-sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-sidebar-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-sidebar-btn:hover {
  background-color: rgba(119, 67, 219, 0.1);
  color: #7743DB;
}

.filter-sidebar-content {
  padding: 0px;
}

.filter-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none; /* Hidden by default on desktop */
}

/* Show filter button only on mobile/tablet */
@media (max-width: 1028px) {
  /* Show filter sidebar and overlay on mobile/tablet */
  .filter-sidebar {
    display: block;
  }
  
  .filter-sidebar-overlay {
    display: block;
  }
}

/* Responsive padding for HomePage content (excluding SearchArea) */
@media (max-width: 1400px) {
  .homepage-container > *:not(.homepage-search-area) {
    padding-left: 30px;
    padding-right: 30px;
    box-sizing: border-box;
  }
  .home-header{
     display: block;
     padding: 7px 3px  ;
  }
  /* .filter-toggle-btn {
  display: none !important; 
  } */

}

@media (max-width: 768px) {
  .homepage-container > *:not(.homepage-search-area) {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media (max-width: 480px) {
  .homepage-container > *:not(.homepage-search-area) {
    padding-left: 0px;
    padding-right: 0px;
  }
}
