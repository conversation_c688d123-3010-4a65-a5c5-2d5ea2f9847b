{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\NotFound.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faHome, faSearch, faBriefcase, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\nimport '../css/NotFound.css';\nimport PageHelmet from './PageHelmet';\n\n/**\r\n * NotFound Component - 404 Error Page\r\n * Displays when user navigates to a non-existent route\r\n * Provides navigation options to help users find what they're looking for\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(PageHelmet, {\n      title: \"Page Not Found - 404 Error\",\n      description: \"The page you're looking for doesn't exist. Find jobs and opportunities on our job portal.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"not-found-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"not-found-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon-section\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faExclamationTriangle,\n            className: \"error-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"error-code\",\n            children: \"404\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"error-title\",\n            children: \"Oops! Page Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"error-description\",\n            children: \"The page you're looking for doesn't exist or has been moved. Don't worry, let's get you back on track!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navigation-options\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-button primary\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faHome\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), \"Go to Homepage\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/browse\",\n            className: \"nav-button secondary\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faSearch\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), \"Browse Jobs\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/company\",\n            className: \"nav-button secondary\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBriefcase\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), \"View Companies\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"helpful-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Popular Pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"links-grid\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/blogs\",\n              className: \"helpful-link\",\n              children: \"Job Blog & Articles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/about\",\n              className: \"helpful-link\",\n              children: \"About Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"helpful-link\",\n              children: \"Contact Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/faq\",\n              className: \"helpful-link\",\n              children: \"FAQ\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-suggestion\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Or try searching for what you need:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search jobs, companies, or topics...\",\n              className: \"search-input\",\n              onKeyPress: e => {\n                if (e.key === 'Enter' && e.target.value.trim()) {\n                  window.location.href = `/browse?search=${encodeURIComponent(e.target.value.trim())}`;\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"search-button\",\n              onClick: e => {\n                const input = e.target.previousElementSibling;\n                if (input.value.trim()) {\n                  window.location.href = `/browse?search=${encodeURIComponent(input.value.trim())}`;\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: faSearch\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "FontAwesomeIcon", "faHome", "faSearch", "faBriefcase", "faExclamationTriangle", "PageHelmet", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotFound", "children", "title", "description", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "icon", "to", "type", "placeholder", "onKeyPress", "e", "key", "target", "value", "trim", "window", "location", "href", "encodeURIComponent", "onClick", "input", "previousElementSibling", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/NotFound.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faHome, faSearch, faBriefcase, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\r\nimport '../css/NotFound.css';\r\nimport PageHelmet from './PageHelmet';\r\n\r\n/**\r\n * NotFound Component - 404 Error Page\r\n * Displays when user navigates to a non-existent route\r\n * Provides navigation options to help users find what they're looking for\r\n */\r\nconst NotFound = () => {\r\n  return (\r\n    <>\r\n      <PageHelmet \r\n        title=\"Page Not Found - 404 Error\"\r\n        description=\"The page you're looking for doesn't exist. Find jobs and opportunities on our job portal.\"\r\n      />\r\n      \r\n      <div className=\"not-found-container\">\r\n        <div className=\"not-found-content\">\r\n          {/* Error Icon and Code */}\r\n          <div className=\"error-icon-section\">\r\n            <FontAwesomeIcon icon={faExclamationTriangle} className=\"error-icon\" />\r\n            <h1 className=\"error-code\">404</h1>\r\n          </div>\r\n          \r\n          {/* Main Error Message */}\r\n          <div className=\"error-message-section\">\r\n            <h2 className=\"error-title\">Oops! Page Not Found</h2>\r\n            <p className=\"error-description\">\r\n              The page you're looking for doesn't exist or has been moved. \r\n              Don't worry, let's get you back on track!\r\n            </p>\r\n          </div>\r\n          \r\n          {/* Navigation Options */}\r\n          <div className=\"navigation-options\">\r\n            <Link to=\"/\" className=\"nav-button primary\">\r\n              <FontAwesomeIcon icon={faHome} />\r\n              Go to Homepage\r\n            </Link>\r\n            \r\n            <Link to=\"/browse\" className=\"nav-button secondary\">\r\n              <FontAwesomeIcon icon={faSearch} />\r\n              Browse Jobs\r\n            </Link>\r\n            \r\n            <Link to=\"/company\" className=\"nav-button secondary\">\r\n              <FontAwesomeIcon icon={faBriefcase} />\r\n              View Companies\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* Helpful Links */}\r\n          <div className=\"helpful-links\">\r\n            <h3>Popular Pages</h3>\r\n            <div className=\"links-grid\">\r\n              <Link to=\"/blogs\" className=\"helpful-link\">\r\n                Job Blog & Articles\r\n              </Link>\r\n              <Link to=\"/about\" className=\"helpful-link\">\r\n                About Us\r\n              </Link>\r\n              <Link to=\"/contact\" className=\"helpful-link\">\r\n                Contact Support\r\n              </Link>\r\n              <Link to=\"/faq\" className=\"helpful-link\">\r\n                FAQ\r\n              </Link>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Search Suggestion */}\r\n          <div className=\"search-suggestion\">\r\n            <p>Or try searching for what you need:</p>\r\n            <div className=\"search-box\">\r\n              <input \r\n                type=\"text\" \r\n                placeholder=\"Search jobs, companies, or topics...\"\r\n                className=\"search-input\"\r\n                onKeyPress={(e) => {\r\n                  if (e.key === 'Enter' && e.target.value.trim()) {\r\n                    window.location.href = `/browse?search=${encodeURIComponent(e.target.value.trim())}`;\r\n                  }\r\n                }}\r\n              />\r\n              <button \r\n                className=\"search-button\"\r\n                onClick={(e) => {\r\n                  const input = e.target.previousElementSibling;\r\n                  if (input.value.trim()) {\r\n                    window.location.href = `/browse?search=${encodeURIComponent(input.value.trim())}`;\r\n                  }\r\n                }}\r\n              >\r\n                <FontAwesomeIcon icon={faSearch} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotFound;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,mCAAmC;AACxG,OAAO,qBAAqB;AAC5B,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACF,UAAU;MACTO,KAAK,EAAC,4BAA4B;MAClCC,WAAW,EAAC;IAA2F;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eAEFV,OAAA;MAAKW,SAAS,EAAC,qBAAqB;MAAAP,QAAA,eAClCJ,OAAA;QAAKW,SAAS,EAAC,mBAAmB;QAAAP,QAAA,gBAEhCJ,OAAA;UAAKW,SAAS,EAAC,oBAAoB;UAAAP,QAAA,gBACjCJ,OAAA,CAACP,eAAe;YAACmB,IAAI,EAAEf,qBAAsB;YAACc,SAAS,EAAC;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvEV,OAAA;YAAIW,SAAS,EAAC,YAAY;YAAAP,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,uBAAuB;UAAAP,QAAA,gBACpCJ,OAAA;YAAIW,SAAS,EAAC,aAAa;YAAAP,QAAA,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDV,OAAA;YAAGW,SAAS,EAAC,mBAAmB;YAAAP,QAAA,EAAC;UAGjC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,oBAAoB;UAAAP,QAAA,gBACjCJ,OAAA,CAACR,IAAI;YAACqB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,oBAAoB;YAAAP,QAAA,gBACzCJ,OAAA,CAACP,eAAe;cAACmB,IAAI,EAAElB;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEPV,OAAA,CAACR,IAAI;YAACqB,EAAE,EAAC,SAAS;YAACF,SAAS,EAAC,sBAAsB;YAAAP,QAAA,gBACjDJ,OAAA,CAACP,eAAe;cAACmB,IAAI,EAAEjB;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEPV,OAAA,CAACR,IAAI;YAACqB,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,sBAAsB;YAAAP,QAAA,gBAClDJ,OAAA,CAACP,eAAe;cAACmB,IAAI,EAAEhB;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,eAAe;UAAAP,QAAA,gBAC5BJ,OAAA;YAAAI,QAAA,EAAI;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAP,QAAA,gBACzBJ,OAAA,CAACR,IAAI;cAACqB,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,cAAc;cAAAP,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPV,OAAA,CAACR,IAAI;cAACqB,EAAE,EAAC,QAAQ;cAACF,SAAS,EAAC,cAAc;cAAAP,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPV,OAAA,CAACR,IAAI;cAACqB,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,cAAc;cAAAP,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPV,OAAA,CAACR,IAAI;cAACqB,EAAE,EAAC,MAAM;cAACF,SAAS,EAAC,cAAc;cAAAP,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAP,QAAA,gBAChCJ,OAAA;YAAAI,QAAA,EAAG;UAAmC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1CV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAP,QAAA,gBACzBJ,OAAA;cACEc,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,sCAAsC;cAClDJ,SAAS,EAAC,cAAc;cACxBK,UAAU,EAAGC,CAAC,IAAK;gBACjB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAID,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;kBAC9CC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkBC,kBAAkB,CAACR,CAAC,CAACE,MAAM,CAACC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACtF;cACF;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFV,OAAA;cACEW,SAAS,EAAC,eAAe;cACzBe,OAAO,EAAGT,CAAC,IAAK;gBACd,MAAMU,KAAK,GAAGV,CAAC,CAACE,MAAM,CAACS,sBAAsB;gBAC7C,IAAID,KAAK,CAACP,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;kBACtBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkBC,kBAAkB,CAACE,KAAK,CAACP,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE;gBACnF;cACF,CAAE;cAAAjB,QAAA,eAEFJ,OAAA,CAACP,eAAe;gBAACmB,IAAI,EAAEjB;cAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACmB,EAAA,GA7FI1B,QAAQ;AA+Fd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}