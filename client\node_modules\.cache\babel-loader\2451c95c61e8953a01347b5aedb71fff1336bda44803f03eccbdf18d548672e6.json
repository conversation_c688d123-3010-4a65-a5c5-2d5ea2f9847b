{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\NotFound.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faHome, faSearch, faBriefcase, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\nimport '../css/NotFound.css';\nimport PageHelmet from './PageHelmet';\n\n/**\r\n * NotFound Component - 404 Error Page\r\n * Displays when user navigates to a non-existent route\r\n * Provides navigation options to help users find what they're looking for\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(PageHelmet, {\n      title: \"Page Not Found - 404 Error\",\n      description: \"The page you're looking for doesn't exist. Find jobs and opportunities on our job portal.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"not-found-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"not-found-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-icon-section\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faExclamationTriangle,\n            className: \"error-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"error-code\",\n            children: \"404\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"not-found-title\",\n            children: \"Oops! Page Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"error-description\",\n            children: \"The page you're looking for doesn't exist or has been moved. Don't worry, let's get you back on track!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navigation-options\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-button primary\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faHome\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), \"Go to Homepage\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "FontAwesomeIcon", "faHome", "faSearch", "faBriefcase", "faExclamationTriangle", "PageHelmet", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotFound", "children", "title", "description", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "icon", "to", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/NotFound.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faHome, faSearch, faBriefcase, faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';\r\nimport '../css/NotFound.css';\r\nimport PageHelmet from './PageHelmet';\r\n\r\n/**\r\n * NotFound Component - 404 Error Page\r\n * Displays when user navigates to a non-existent route\r\n * Provides navigation options to help users find what they're looking for\r\n */\r\nconst NotFound = () => {\r\n  return (\r\n    <>\r\n      <PageHelmet \r\n        title=\"Page Not Found - 404 Error\"\r\n        description=\"The page you're looking for doesn't exist. Find jobs and opportunities on our job portal.\"\r\n      />\r\n      \r\n      <div className=\"not-found-container\">\r\n        <div className=\"not-found-content\">\r\n          {/* Error Icon and Code */}\r\n          <div className=\"error-icon-section\">\r\n            <FontAwesomeIcon icon={faExclamationTriangle} className=\"error-icon\" />\r\n            <h1 className=\"error-code\">404</h1>\r\n          </div>\r\n          \r\n          {/* Main Error Message */}\r\n          <div className=\"error-message-section\">\r\n            <h2 className=\"not-found-title\">Oops! Page Not Found</h2>\r\n            <p className=\"error-description\">\r\n              The page you're looking for doesn't exist or has been moved. \r\n              Don't worry, let's get you back on track!\r\n            </p>\r\n          </div>\r\n          \r\n          {/* Navigation Options */}\r\n          <div className=\"navigation-options\">\r\n            <Link to=\"/\" className=\"nav-button primary\">\r\n              <FontAwesomeIcon icon={faHome} />\r\n              Go to Homepage\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotFound;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,qBAAqB,QAAQ,mCAAmC;AACxG,OAAO,qBAAqB;AAC5B,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA,CAACF,UAAU;MACTO,KAAK,EAAC,4BAA4B;MAClCC,WAAW,EAAC;IAA2F;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxG,CAAC,eAEFV,OAAA;MAAKW,SAAS,EAAC,qBAAqB;MAAAP,QAAA,eAClCJ,OAAA;QAAKW,SAAS,EAAC,mBAAmB;QAAAP,QAAA,gBAEhCJ,OAAA;UAAKW,SAAS,EAAC,oBAAoB;UAAAP,QAAA,gBACjCJ,OAAA,CAACP,eAAe;YAACmB,IAAI,EAAEf,qBAAsB;YAACc,SAAS,EAAC;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvEV,OAAA;YAAIW,SAAS,EAAC,YAAY;YAAAP,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,uBAAuB;UAAAP,QAAA,gBACpCJ,OAAA;YAAIW,SAAS,EAAC,iBAAiB;YAAAP,QAAA,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDV,OAAA;YAAGW,SAAS,EAAC,mBAAmB;YAAAP,QAAA,EAAC;UAGjC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNV,OAAA;UAAKW,SAAS,EAAC,oBAAoB;UAAAP,QAAA,eACjCJ,OAAA,CAACR,IAAI;YAACqB,EAAE,EAAC,GAAG;YAACF,SAAS,EAAC,oBAAoB;YAAAP,QAAA,gBACzCJ,OAAA,CAACP,eAAe;cAACmB,IAAI,EAAElB;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACI,EAAA,GApCIX,QAAQ;AAsCd,eAAeA,QAAQ;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}