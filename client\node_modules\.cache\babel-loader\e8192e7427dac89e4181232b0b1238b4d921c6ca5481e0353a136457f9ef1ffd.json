{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\Admin\\\\JobsAdmin.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-unused-vars */\n/* eslint-disable no-useless-escape */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faPlus, faEdit, faTrash, faSearch, faExclamationCircle, faCheckCircle, faEye, faFire, faTimes, faBuilding, faImage, faSpinner, faChevronDown, faExclamationTriangle, faSync } from '@fortawesome/free-solid-svg-icons';\nimport '../../css/JobsAdmin.css';\nimport '../../css/JobFormModal.css';\nimport '../../css/shared-delete-dialog.css';\nimport ApiService from '../../services/apiService';\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../../utils/subTopicsUtils';\n// Import search-fix.css last to ensure it takes precedence\nimport '../../css/search-fix.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EXPERIENCE_OPTIONS = [\"No Experience Required\", \"Entry Level\", \"Mid Level\", \"Senior Level\", \"Manager\", \"Executive\"];\nconst JobsAdmin = () => {\n  _s();\n  const [jobs, setJobs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [companies, setCompanies] = useState([]);\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\n\n  // Delete confirmation dialog state\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [jobToDelete, setJobToDelete] = useState(null);\n\n  // New job form state\n  // Get today's date in YYYY-MM-DD format for the date inputs\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\n\n  // Format date for display in table\n  const formatDisplayDate = dateString => {\n    if (!dateString || dateString === '-') return '-';\n    try {\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (err) {\n      console.error(\"Error formatting display date:\", err);\n      return dateString;\n    }\n  };\n  const [newJobForm, setNewJobForm] = useState({\n    job_title: '',\n    year: new Date().getFullYear(),\n    start_date: getTodayFormatted(),\n    end_date: '',\n    send_cv_email: '',\n    main_topics: 'Government Jobs',\n    // Set a default valid value from the enum\n    sub_topics: [],\n    job_type: 'Full Time Jobs',\n    // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n    experience_level: 'Entry Level',\n    // New field for experience level\n    min_salary: '',\n    max_salary: '',\n    job_description: '',\n    job_post_image: null,\n    job_post_thumbnail: null,\n    company_name: '',\n    company_logo: null,\n    // UI-only fields (not in database)\n    status: 'Active',\n    hot: false\n  });\n\n  // Image preview states\n  const [jobImagePreview, setJobImagePreview] = useState(null);\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\n  const [logoPreview, setLogoPreview] = useState(null);\n  // State for managing sub-topics\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\n\n  // Load available sub-topics from localStorage\n  const loadSubTopicsFromStorage = () => {\n    try {\n      const activeSubTopicNames = getActiveSubTopicNames();\n      if (activeSubTopicNames.length > 0) {\n        setAvailableSubTopics(activeSubTopicNames);\n      } else {\n        // Use default sub-topics if no saved data\n        const defaultSubTopics = getDefaultSubTopics();\n        setAvailableSubTopics(defaultSubTopics);\n      }\n    } catch (err) {\n      console.error(\"Error loading sub-topics from localStorage:\", err);\n      // Fallback to default sub-topics\n      const defaultSubTopics = getDefaultSubTopics();\n      setAvailableSubTopics(defaultSubTopics);\n    }\n  };\n\n  // Initialize available sub-topics\n  useEffect(() => {\n    loadSubTopicsFromStorage();\n  }, []);\n\n  // Listen for sub-topics updates from SubTopicsAdmin\n  useEffect(() => {\n    const handleSubTopicsUpdate = event => {\n      console.log(\"Sub-topics updated, refreshing available options...\");\n      loadSubTopicsFromStorage();\n    };\n\n    // Add event listener for sub-topics updates\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n\n    // Cleanup event listener on component unmount\n    return () => {\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\n    };\n  }, []);\n\n  // Fetch jobs from backend\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        const response = await ApiService.jobs.getAll();\n\n        // Transform backend data to match the format used in frontend\n        // Adding default values for fields that don't exist in the database\n        const formattedJobs = response.data.map(job => {\n          // Determine the best date to use\n          let dateToUse;\n          if (job.created_at) {\n            // Prefer created_at if available\n            dateToUse = job.created_at;\n          } else if (job.start_date) {\n            // Otherwise use start_date\n            dateToUse = job.start_date;\n          } else if (job.posted_date) {\n            // Fall back to posted_date\n            dateToUse = job.posted_date;\n          } else {\n            // If no dates are available, use current date\n            dateToUse = new Date().toISOString();\n          }\n\n          // Compose salary display string\n          let salaryDisplay = '';\n          if (job.min_salary && job.max_salary) {\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n          } else if (job.min_salary) {\n            salaryDisplay = `${job.min_salary}`;\n          } else if (job.max_salary) {\n            salaryDisplay = `${job.max_salary}`;\n          } else {\n            salaryDisplay = '';\n          }\n          return {\n            id: job.job_id,\n            hot: job.hot || false,\n            // Use actual hot value from database\n            company: job.company_name,\n            position: job.job_title,\n            location: job.main_topics,\n            workTime: job.job_type,\n            experience_level: job.experience_level,\n            cv_email: job.send_cv_email,\n            salary: salaryDisplay,\n            status: 'Active',\n            // Default since it doesn't exist in DB\n            datePosted: dateToUse,\n            rawDatePosted: job.start_date || job.posted_date || job.created_at,\n            // Store raw date for debugging\n            views: job.view_count || 0,\n            // Use actual view count if available\n            end_date: job.end_date // Add end_date for auto-delete functionality\n          };\n        });\n\n        // Sort jobs by creation date descending (newest first), then by ID descending as fallback\n        const sortedJobs = [...formattedJobs].sort((a, b) => {\n          // First, try to sort by creation date\n          const dateA = new Date(a.datePosted);\n          const dateB = new Date(b.datePosted);\n          if (dateB.getTime() !== dateA.getTime()) {\n            return dateB.getTime() - dateA.getTime();\n          }\n\n          // If dates are the same, sort by ID descending (higher ID = newer)\n          return b.id - a.id;\n        });\n        setJobs(sortedJobs);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching jobs:\", err);\n        setError(\"Failed to load jobs from server\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchJobs();\n  }, []);\n\n  // Auto-delete expired jobs (2 weeks after end date)\n  useEffect(() => {\n    const checkAndDeleteExpiredJobs = async () => {\n      try {\n        const currentDate = new Date();\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\n\n        // Get all jobs to check for expired ones\n        const response = await ApiService.jobs.getAll();\n        const allJobs = response.data;\n        const expiredJobs = allJobs.filter(job => {\n          if (!job.end_date) return false; // Skip jobs without end date\n\n          const endDate = new Date(job.end_date);\n          const timeDifference = currentDate.getTime() - endDate.getTime();\n\n          // Check if job is expired by more than 2 weeks\n          return timeDifference > twoWeeksInMs;\n        });\n\n        // Delete expired jobs\n        for (const expiredJob of expiredJobs) {\n          try {\n            await ApiService.jobs.delete(expiredJob.job_id);\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\n          } catch (deleteErr) {\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\n          }\n        }\n\n        // If any jobs were deleted, refresh the jobs list\n        if (expiredJobs.length > 0) {\n          // Refresh jobs list by fetching again\n          const updatedResponse = await ApiService.jobs.getAll();\n          const formattedJobs = updatedResponse.data.map(job => {\n            let dateToUse;\n            if (job.created_at) {\n              dateToUse = job.created_at;\n            } else if (job.start_date) {\n              dateToUse = job.start_date;\n            } else if (job.posted_date) {\n              dateToUse = job.posted_date;\n            } else {\n              dateToUse = new Date().toISOString();\n            }\n            let salaryDisplay = '';\n            if (job.min_salary && job.max_salary) {\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n            } else if (job.min_salary) {\n              salaryDisplay = `${job.min_salary}`;\n            } else if (job.max_salary) {\n              salaryDisplay = `${job.max_salary}`;\n            } else {\n              salaryDisplay = '';\n            }\n            return {\n              id: job.job_id,\n              hot: job.hot || false,\n              company: job.company_name,\n              position: job.job_title,\n              location: job.main_topics,\n              workTime: job.job_type,\n              experience_level: job.experience_level,\n              cv_email: job.send_cv_email,\n              salary: salaryDisplay,\n              status: 'Active',\n              datePosted: dateToUse,\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\n              views: job.view_count || 0,\n              end_date: job.end_date\n            };\n          });\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n          setJobs(sortedJobs);\n        }\n      } catch (err) {\n        console.error(\"Error checking for expired jobs:\", err);\n        // Don't set error state here to avoid disrupting normal operation\n      }\n    };\n\n    // Run the check immediately when component mounts\n    checkAndDeleteExpiredJobs();\n\n    // Set up interval to check every hour (3600000 ms)\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\n\n    // Cleanup interval on component unmount\n    return () => clearInterval(intervalId);\n  }, []);\n\n  // Fetch companies when modal is opened\n  useEffect(() => {\n    if (isModalOpen) {\n      console.log(\"Modal opened, fetching companies...\");\n      fetchCompanies();\n    }\n  }, [isModalOpen]);\n  const fetchCompanies = async () => {\n    try {\n      console.log(\"Fetching companies from API...\");\n      const response = await ApiService.companies.getAll();\n      const companiesData = response.data.data || [];\n      console.log(\"Companies fetched:\", companiesData);\n      setCompanies(companiesData);\n\n      // If we're editing a job and have a company name, select the matching company\n      if (selectedJob && newJobForm.company_name) {\n        const matchingCompany = companiesData.find(company => company.company_name === newJobForm.company_name);\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\n          setLogoPreview(matchingCompany.company_logo_url);\n        }\n      }\n    } catch (err) {\n      console.error('Error fetching companies:', err);\n      // Don't set error state here to avoid disrupting the job form\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const handleEditJob = async job => {\n    try {\n      setLoading(true);\n      // Fetch full job details from backend\n      const response = await ApiService.jobs.getById(job.id);\n      const fullJobDetails = response.data;\n      console.log(\"Job details from API:\", fullJobDetails);\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\n      setSelectedJob(job);\n      // Initialize form with job data - only using fields from the actual database schema\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\n      const formatDate = dateString => {\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\n\n        try {\n          // Parse the date and ensure it's valid\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) {\n            console.warn(\"Invalid date detected:\", dateString);\n            return getTodayFormatted(); // Default to today for invalid dates\n          }\n\n          // Format as YYYY-MM-DD for date input\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n          return `${year}-${month}-${day}`;\n        } catch (err) {\n          console.error(\"Error formatting date:\", err);\n          return getTodayFormatted();\n        }\n      };\n\n      // Set image previews\n      if (fullJobDetails.job_post_image) {\n        setJobImagePreview(fullJobDetails.job_post_image);\n      }\n      if (fullJobDetails.job_post_thumbnail) {\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\n      }\n\n      // Handle company logo preview\n      let companyLogoUrl = null;\n      if (fullJobDetails.company_logo) {\n        setLogoPreview(fullJobDetails.company_logo);\n        companyLogoUrl = fullJobDetails.company_logo;\n      } else if (fullJobDetails.company_logo_url) {\n        setLogoPreview(fullJobDetails.company_logo_url);\n        companyLogoUrl = fullJobDetails.company_logo_url;\n      } else {\n        // Fetch companies to find the logo\n        try {\n          const companiesResponse = await ApiService.companies.getAll();\n          const companiesData = companiesResponse.data.data || [];\n          const matchingCompany = companiesData.find(company => company.company_name === fullJobDetails.company_name);\n          if (matchingCompany && matchingCompany.company_logo_url) {\n            setLogoPreview(matchingCompany.company_logo_url);\n            companyLogoUrl = matchingCompany.company_logo_url;\n          }\n        } catch (err) {\n          console.error(\"Error finding company logo:\", err);\n        }\n      }\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\n      let expLevel = fullJobDetails.experience_level;\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\n        expLevel = \"No Experience Required\";\n      }\n      setNewJobForm({\n        job_title: fullJobDetails.job_title,\n        company_name: fullJobDetails.company_name,\n        year: fullJobDetails.year || new Date().getFullYear(),\n        start_date: formatDate(fullJobDetails.start_date),\n        end_date: formatDate(fullJobDetails.end_date),\n        job_type: fullJobDetails.job_type,\n        experience_level: expLevel,\n        min_salary: fullJobDetails.min_salary || '',\n        max_salary: fullJobDetails.max_salary || '',\n        send_cv_email: fullJobDetails.send_cv_email || '',\n        main_topics: fullJobDetails.main_topics,\n        sub_topics: (() => {\n          try {\n            // Try to parse sub_topics as JSON if it's a string\n            if (!fullJobDetails.sub_topics) return [];\n            if (typeof fullJobDetails.sub_topics === 'string') {\n              // Check if it starts with [ which would indicate a likely JSON array\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\n                return JSON.parse(fullJobDetails.sub_topics);\n              } else {\n                // If it's not a JSON array, treat it as a single item\n                return [fullJobDetails.sub_topics];\n              }\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\n              return fullJobDetails.sub_topics;\n            } else {\n              return [];\n            }\n          } catch (err) {\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\n          }\n        })(),\n        job_description: fullJobDetails.job_description || '',\n        job_post_image: fullJobDetails.job_post_image,\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\n        company_logo: fullJobDetails.company_logo,\n        company_logo_url: companyLogoUrl,\n        // Store the company logo URL\n        // Use default values for UI-only fields that don't exist in the database\n        status: 'Active',\n        hot: fullJobDetails.hot || false // Use actual hot value from database\n      });\n      setIsModalOpen(true);\n      setError(null);\n    } catch (err) {\n      console.error(\"Error fetching job details:\", err);\n      setError(`Failed to load job details for ${job.position}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteJob = async jobId => {\n    try {\n      setLoading(true);\n      await ApiService.jobs.delete(jobId);\n      setJobs(jobs.filter(job => job.id !== jobId));\n      setError(null);\n      setShowDeleteConfirm(false);\n      setJobToDelete(null);\n    } catch (err) {\n      console.error(\"Error deleting job:\", err);\n      setError(\"Failed to delete job\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to show delete confirmation\n  const confirmDelete = job => {\n    setJobToDelete(job);\n    setShowDeleteConfirm(true);\n  };\n\n  // Function to cancel delete\n  const cancelDelete = () => {\n    setShowDeleteConfirm(false);\n    setJobToDelete(null);\n  };\n  const handleToggleStatus = async jobId => {\n    try {\n      setLoading(true);\n      const jobToUpdate = jobs.find(job => job.id === jobId);\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\n\n      // Since we don't have a status column in the database,\n      // we'll just update the local state without sending to the backend\n      // In a real application, you would add the status column to the database\n\n      // Update local state only\n      setJobs(jobs.map(job => job.id === jobId ? {\n        ...job,\n        status: newStatus\n      } : job));\n      setError(null);\n    } catch (err) {\n      console.error(\"Error updating job status:\", err);\n      setError(\"Failed to update job status\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openNewJobModal = () => {\n    setIsModalOpen(true);\n    setSelectedJob(null);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setSelectedJob(null);\n    // Reset form\n    setNewJobForm({\n      job_title: '',\n      year: new Date().getFullYear(),\n      start_date: getTodayFormatted(),\n      end_date: '',\n      send_cv_email: '',\n      main_topics: 'Government Jobs',\n      // Ensure we always have a valid value\n      sub_topics: [],\n      job_type: 'Full Time Jobs',\n      // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n      experience_level: 'Entry Level',\n      min_salary: '',\n      max_salary: '',\n      job_description: '',\n      job_post_image: null,\n      job_post_thumbnail: null,\n      company_name: '',\n      company_logo: null,\n      // UI-only fields (not in database)\n      status: 'Active',\n      hot: false\n    });\n    // Reset previews\n    setJobImagePreview(null);\n    setThumbnailPreview(null);\n    setLogoPreview(null);\n    setShowCompanyDropdown(false);\n  };\n\n  // Basic client-side text sanitization\n  const sanitizeInputText = text => {\n    if (!text) return '';\n    // Modified to preserve Unicode characters in the job title\n    // Only remove control characters and potentially problematic chars\n    return text.replace(/[--]/g, '') // Remove control characters\n    .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\n    .trim();\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n\n    // Apply sanitization to text fields that might contain problematic characters\n    if (name === 'job_title' || name === 'job_description') {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\n      });\n    } else {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value\n      });\n    }\n  };\n  const handleImageUpload = (e, imageType) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (imageType === 'job_post_image') {\n          setJobImagePreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_image: file\n          });\n        } else if (imageType === 'job_post_thumbnail') {\n          setThumbnailPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_thumbnail: file\n          });\n        } else if (imageType === 'company_logo') {\n          setLogoPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            company_logo: file\n          });\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSelectCompany = company => {\n    console.log(\"Company selected:\", company);\n\n    // Validate company has a name\n    if (!company || !company.company_name) {\n      console.error(\"Invalid company selected\");\n      return;\n    }\n\n    // Set company name in the form\n    setNewJobForm({\n      ...newJobForm,\n      company_name: company.company_name,\n      company_logo: null,\n      // Reset the file input since we're using an existing logo\n      company_logo_url: company.company_logo_url // Store the logo URL\n    });\n\n    // Set logo preview\n    if (company.company_logo_url) {\n      setLogoPreview(company.company_logo_url);\n    } else {\n      setLogoPreview(null);\n    }\n\n    // Close the dropdown\n    setShowCompanyDropdown(false);\n  };\n\n  // Add function to remove logo\n  const handleRemoveLogo = () => {\n    setLogoPreview(null);\n    // If there was a logo file in the form, clear it\n    if (newJobForm.company_logo) {\n      setNewJobForm({\n        ...newJobForm,\n        company_logo: null\n      });\n    }\n  };\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n\n    // Debug the form state\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\n    console.log(\"Original form state:\", newJobForm);\n    console.log(\"Selected job:\", selectedJob);\n\n    // Client-side validation for required fields\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\n      setError(\"Job title is required\");\n      return;\n    }\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\n      setError(\"Company name is required\");\n      return;\n    }\n\n    // Validate email/URL field if provided\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\n      const value = newJobForm.send_cv_email.trim();\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\n        setError(\"Please enter a valid email address or website URL\");\n        return;\n      }\n    }\n\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\n    let experienceLevelToSend = newJobForm.experience_level;\n    if (!experienceLevelToSend) {\n      experienceLevelToSend = \"No Experience Required\";\n    }\n    try {\n      setLoading(true);\n\n      // Create a sanitized copy of the form data to send to the server\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\n      const sanitizedForm = {\n        ...newJobForm,\n        job_title: sanitizedTitle,\n        job_description: sanitizeInputText(newJobForm.job_description),\n        experience_level: experienceLevelToSend,\n        // For new jobs, set today's date as the start_date if not provided\n        start_date: selectedJob ? newJobForm.start_date : newJobForm.start_date || getTodayFormatted()\n      };\n      console.log(\"Sanitized form data:\", sanitizedForm);\n\n      // Prepare form data for API - create a completely fresh FormData object\n      const formData = new FormData();\n      // Add only fields that exist in the database schema, ensuring each field is only added once\n      const dbFields = ['job_title', 'company_name', 'year', 'start_date', 'end_date', 'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level', 'min_salary', 'max_salary', 'job_description', 'hot'];\n\n      // Add text fields one by one to avoid duplicates\n      dbFields.forEach(key => {\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\n          // Special handling for sub_topics\n          if (key === 'sub_topics') {\n            try {\n              if (Array.isArray(sanitizedForm[key])) {\n                // Ensure we're sending a clean array without unexpected characters\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\n                formData.append(key, JSON.stringify(cleanSubTopics));\n              } else {\n                // If it's a string or something else, convert to array\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\n              }\n            } catch (err) {\n              console.error(\"Error formatting sub_topics:\", err);\n              formData.append(key, '[]'); // Fallback to empty array\n            }\n          } else {\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\n            formData.append(key, sanitizedForm[key]);\n          }\n        }\n      });\n\n      // Add file fields if they exist\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\n        formData.append('job_post_image', sanitizedForm.job_post_image);\n      }\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\n      }\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\n        formData.append('company_logo', sanitizedForm.company_logo);\n      }\n\n      // If we have a logo preview from an existing company but no file, add the URL\n      if (logoPreview && !sanitizedForm.company_logo) {\n        formData.append('existing_company_logo_url', logoPreview);\n      }\n\n      // Add company_logo_url if it exists\n      if (sanitizedForm.company_logo_url) {\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\n      }\n\n      // Debug: Log what's being sent in the FormData\n      console.log(\"FormData contents:\");\n      for (let pair of formData.entries()) {\n        console.log(pair[0] + ': ' + pair[1]);\n      }\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\n\n      // Prepare form data for submission\n      if (selectedJob) {\n        // Update existing job\n        try {\n          await ApiService.jobs.update(selectedJob.id, formData);\n          // Successfully updated job\n        } catch (apiError) {\n          // Handle update error silently\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      } else {\n        // Create new job\n        try {\n          await ApiService.jobs.create(formData);\n          // Successfully created job\n        } catch (apiError) {\n          var _apiError$response, _apiError$response2, _apiError$response3;\n          // Log detailed error information\n          console.error(\"API Error Details:\", {\n            message: apiError.message,\n            status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n            statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n            data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data\n          });\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      }\n      setError(null);\n      // Close modal\n      closeModal();\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      console.error(\"Error saving job:\", err);\n      // Show more detailed error message if available\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredJobs = jobs.filter(job => {\n    const matchesSearch = job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase()) || job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesSearch;\n  });\n\n  // Add this useEffect to handle closing the dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const dropdownElement = document.querySelector('.company-select-dropdown');\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\n        setShowCompanyDropdown(false);\n      }\n    };\n    if (showCompanyDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [showCompanyDropdown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"jobs-admin-container\",\n    children: [showDeleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-dialog\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faExclamationTriangle,\n            className: \"delete-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Deletion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to delete this job posting?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: jobToDelete === null || jobToDelete === void 0 ? void 0 : jobToDelete.position\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-delete-btn\",\n            onClick: cancelDelete,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-delete-btn\",\n            onClick: () => handleDeleteJob(jobToDelete.id),\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 913,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 912,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"jobs-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"jobs-title\",\n        children: \"Job Listings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"refresh-button\",\n          onClick: () => {\n            setLoading(true);\n            const fetchJobs = async () => {\n              try {\n                const response = await ApiService.jobs.getAll();\n\n                // Transform backend data to match the format used in frontend\n                const formattedJobs = response.data.map(job => {\n                  // Determine the best date to use\n                  let dateToUse;\n                  if (job.created_at) {\n                    dateToUse = job.created_at;\n                  } else if (job.start_date) {\n                    dateToUse = job.start_date;\n                  } else if (job.posted_date) {\n                    dateToUse = job.posted_date;\n                  } else {\n                    dateToUse = new Date().toISOString();\n                  }\n\n                  // Compose salary display string\n                  let salaryDisplay = '';\n                  if (job.min_salary && job.max_salary) {\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n                  } else if (job.min_salary) {\n                    salaryDisplay = `${job.min_salary}`;\n                  } else if (job.max_salary) {\n                    salaryDisplay = `${job.max_salary}`;\n                  } else {\n                    salaryDisplay = '';\n                  }\n                  return {\n                    id: job.job_id,\n                    hot: job.hot || false,\n                    company: job.company_name,\n                    position: job.job_title,\n                    location: job.main_topics,\n                    workTime: job.job_type,\n                    experience_level: job.experience_level,\n                    cv_email: job.send_cv_email,\n                    salary: salaryDisplay,\n                    status: 'Active',\n                    datePosted: dateToUse,\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\n                    views: job.view_count || 0,\n                    end_date: job.end_date\n                  };\n                });\n\n                // Sort jobs by datePosted descending (newest first)\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n                setJobs(sortedJobs);\n                setError(null);\n              } catch (err) {\n                console.error(\"Error fetching jobs:\", err);\n                setError(\"Failed to load jobs from server\");\n              } finally {\n                setLoading(false);\n              }\n            };\n            fetchJobs();\n          },\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSync\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-job-button\",\n          onClick: openNewJobModal,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add New Job\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 941,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: faExclamationCircle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1018,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 13\n          }, this), \"          \", /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search by company or position...\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            className: \"search-input\",\n            style: {\n              paddingLeft: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 82\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1024,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faSpinner,\n          spin: true,\n          size: \"2x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading jobs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"jobs-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Experience Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email / Web URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Salary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Expire Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Posted Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Views\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.length > 0 ? filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [job.hot && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hot-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFire,\n                    style: {\n                      marginRight: '4px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1069,\n                    columnNumber: 27\n                  }, this), \"URGENT\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-position\",\n                  title: job.position,\n                  children: job.position && job.position.length > 35 ? job.position.slice(0, 32) + '...' : job.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-company\",\n                  children: job.company\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.workTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1080,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"experience-level\",\n                  title: job.experience_level,\n                  children: job.experience_level || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cv-email\",\n                  title: job.cv_email,\n                  children: job.cv_email ? /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `mailto:${job.cv_email}`,\n                    className: \"email-link\",\n                    children: job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 27\n                  }, this) : 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.salary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.end_date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.datePosted)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1099,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"views-container\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faEye,\n                    className: \"views-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1102,\n                    columnNumber: 25\n                  }, this), job.views]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1101,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button edit-button\",\n                    onClick: () => handleEditJob(job),\n                    title: \"Edit job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1114,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button delete-button\",\n                    onClick: () => confirmDelete(job),\n                    title: \"Delete job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1123,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1065,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"9\",\n                className: \"no-jobs-message\",\n                children: searchTerm ? \"No jobs match your search criteria\" : \"No jobs available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1062,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1048,\n          columnNumber: 11\n        }, this)\n      }, void 0, false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1040,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: e => {\n        // Close modal when clicking outside\n        if (e.target.className === 'modal-overlay') {\n          closeModal();\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: selectedJob ? 'Edit Job' : 'Add New Job'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: closeModal,\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faTimes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1157,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleFormSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_title\",\n                  children: [\"Job Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1164,\n                    columnNumber: 58\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"job_title\",\n                  name: \"job_title\",\n                  value: newJobForm.job_title,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter job title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [\"Company \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1176,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"company-selector\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-select-dropdown\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"company-select-button\",\n                      onClick: () => setShowCompanyDropdown(!showCompanyDropdown),\n                      children: [newJobForm.company_name ? newJobForm.company_name : 'Select a company', /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faChevronDown\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1185,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1179,\n                      columnNumber: 25\n                    }, this), showCompanyDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"company-list-dropdown\",\n                      children: [companies && companies.length > 0 ? companies.map(company => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"company-item\",\n                        onClick: () => {\n                          handleSelectCompany(company);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"company-item-logo\",\n                          children: company.company_logo_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: company.company_logo_url,\n                            alt: company.company_name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1201,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                            icon: faBuilding\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1203,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1199,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: company.company_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1206,\n                          columnNumber: 35\n                        }, this)]\n                      }, company.company_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 33\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-companies\",\n                        children: \"No companies available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1210,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"manual-company-entry\",\n                        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                          children: \"Or enter company name manually:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1213,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          placeholder: \"Enter company name\",\n                          value: newJobForm.company_name || '',\n                          onChange: e => {\n                            setNewJobForm({\n                              ...newJobForm,\n                              company_name: e.target.value\n                            });\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1214,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: \"apply-company-btn\",\n                          onClick: () => setShowCompanyDropdown(false),\n                          children: \"Apply\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1225,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1212,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1189,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1178,\n                    columnNumber: 23\n                  }, this), logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"logo-preview-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: logoPreview,\n                      alt: \"Company logo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"remove-logo-button\",\n                      onClick: handleRemoveLogo,\n                      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faTimes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1246,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1241,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1239,\n                    columnNumber: 25\n                  }, this), !logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      id: \"company_logo\",\n                      name: \"company_logo\",\n                      onChange: e => handleImageUpload(e, 'company_logo'),\n                      accept: \"image/*\",\n                      style: {\n                        display: 'none'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1254,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"file-upload-label\",\n                      onClick: () => document.getElementById('company_logo').click(),\n                      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faImage\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1267,\n                        columnNumber: 29\n                      }, this), \"Upload Company Logo\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1262,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1253,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"year\",\n                  children: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1278,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"year\",\n                  name: \"year\",\n                  value: newJobForm.year,\n                  onChange: handleFormChange,\n                  required: true,\n                  min: \"2000\",\n                  max: \"2100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1279,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1277,\n                columnNumber: 19\n              }, this), \"                  \", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_type\",\n                  children: \"Job Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"job_type\",\n                  name: \"job_type\",\n                  value: newJobForm.job_type,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time Jobs\",\n                    children: \"Full Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1298,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time Jobs\",\n                    children: \"Part Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Remote Jobs\",\n                    children: \"Remote Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1300,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Freelance\",\n                    children: \"Freelance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1301,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Temporary\",\n                    children: \"Temporary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 43\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"experience_level\",\n                  children: \"Experience Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"experience_level\",\n                  name: \"experience_level\",\n                  value: newJobForm.experience_level,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: EXPERIENCE_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1315,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"start_date\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1323,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"start_date\",\n                  name: \"start_date\",\n                  value: newJobForm.start_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"end_date\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"end_date\",\n                  name: \"end_date\",\n                  value: newJobForm.end_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"send_cv_email\",\n                  children: \"Email/WebURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"send_cv_email\",\n                  name: \"send_cv_email\",\n                  value: newJobForm.send_cv_email,\n                  onChange: handleFormChange,\n                  placeholder: \"Enter email or website URL for CV submissions (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"min_salary\",\n                  children: \"Minimum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"min_salary\",\n                  name: \"min_salary\",\n                  value: newJobForm.min_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Minimum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1360,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"max_salary\",\n                  children: \"Maximum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"max_salary\",\n                  name: \"max_salary\",\n                  value: newJobForm.max_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Maximum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1346,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group checkbox-group\",\n                style: {\n                  textAlign: 'left'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"hot\",\n                    checked: newJobForm.hot,\n                    onChange: handleFormChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkbox-text\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faFire,\n                      className: \"hot-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1397,\n                      columnNumber: 25\n                    }, this), \"Mark as URGENT Job\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1396,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1389,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"main_topics\",\n                  children: \"Main Topic/Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"main_topics\",\n                  name: \"main_topics\",\n                  value: newJobForm.main_topics,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Government Jobs\",\n                    children: \"Government Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1414,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Private Jobs\",\n                    children: \"Private Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1415,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Foreign Jobs\",\n                    children: \"Foreign Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1416,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Internships\",\n                    children: \"Internships\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1417,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1404,\n              columnNumber: 17\n            }, this), \"                \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sub Topics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1423,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small-text\",\n                    children: [newJobForm.sub_topics.length, \" selected\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1424,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    style: {\n                      marginLeft: '16px',\n                      padding: '2px 8px',\n                      fontSize: '0.9em'\n                    },\n                    onClick: () => setNewJobForm(prev => ({\n                      ...prev,\n                      sub_topics: []\n                    })),\n                    children: \"Clear All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1425,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    style: {\n                      marginLeft: '8px',\n                      padding: '2px 8px',\n                      fontSize: '0.9em',\n                      backgroundColor: '#17a2b8',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '3px'\n                    },\n                    onClick: loadSubTopicsFromStorage,\n                    title: \"Refresh sub-topics list\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faSync,\n                      style: {\n                        marginRight: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1438,\n                      columnNumber: 5\n                    }, this), \"Refresh\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 3\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-management-info\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    style: {\n                      color: '#6c757d',\n                      fontStyle: 'italic'\n                    },\n                    children: \"\\uD83D\\uDCA1 Tip: You can manage available sub-topics from the \\\"Sub Topics\\\" section in the sidebar. Click \\\"Refresh\\\" if you don't see newly added sub-topics.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 3\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1442,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-checkbox-container\",\n                  children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `subtopic-${index}`,\n                      checked: newJobForm.sub_topics.includes(topic),\n                      onChange: e => {\n                        const checked = e.target.checked;\n                        setNewJobForm(prev => {\n                          const set = new Set(prev.sub_topics);\n                          if (checked) {\n                            set.add(topic);\n                          } else {\n                            set.delete(topic);\n                          }\n                          return {\n                            ...prev,\n                            sub_topics: Array.from(set)\n                          };\n                        });\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1454,\n                      columnNumber: 1\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `subtopic-${index}`,\n                      children: topic\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1474,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1448,\n                  columnNumber: 1\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1420,\n              columnNumber: 39\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_description\",\n                  children: \"Job Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1485,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"job_description\",\n                  name: \"job_description\",\n                  rows: \"5\",\n                  value: newJobForm.job_description,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter detailed job description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"form-help-text\",\n                  children: \"Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1495,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1484,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1503,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_image\",\n                    onChange: e => handleImageUpload(e, 'job_post_image'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_image\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1513,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1514,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1512,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 21\n                }, this), jobImagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: jobImagePreview,\n                    alt: \"Job Post\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1519,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1518,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1524,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_thumbnail\",\n                    onChange: e => handleImageUpload(e, 'job_post_thumbnail'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1526,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_thumbnail\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1534,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Thumbnail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1535,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1533,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1525,\n                  columnNumber: 21\n                }, this), thumbnailPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"thumbnail-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: thumbnailPreview,\n                    alt: \"Thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1540,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1539,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1523,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"cancel-button\",\n                onClick: closeModal,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1549,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-button\",\n                children: selectedJob ? 'Update Job' : 'Create Job'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1552,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1548,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1161,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1153,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1147,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 909,\n    columnNumber: 5\n  }, this);\n};\n_s(JobsAdmin, \"K5jflC6THa/hDQwnclixIfI/qg8=\");\n_c = JobsAdmin;\nexport default JobsAdmin;\nvar _c;\n$RefreshReg$(_c, \"JobsAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FontAwesomeIcon", "faPlus", "faEdit", "faTrash", "faSearch", "faExclamationCircle", "faCheckCircle", "faEye", "faFire", "faTimes", "faBuilding", "faImage", "faSpinner", "faChevronDown", "faExclamationTriangle", "faSync", "ApiService", "getActiveSubTopicNames", "getDefaultSubTopics", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EXPERIENCE_OPTIONS", "JobsAdmin", "_s", "jobs", "setJobs", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "isModalOpen", "setIsModalOpen", "companies", "setCompanies", "showCompanyDropdown", "setShowCompanyDropdown", "showDeleteConfirm", "setShowDeleteConfirm", "jobToDelete", "setJobToDelete", "getTodayFormatted", "Date", "toISOString", "split", "formatDisplayDate", "dateString", "date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "err", "console", "newJobForm", "setNewJobForm", "job_title", "getFullYear", "start_date", "end_date", "send_cv_email", "main_topics", "sub_topics", "job_type", "experience_level", "min_salary", "max_salary", "job_description", "job_post_image", "job_post_thumbnail", "company_name", "company_logo", "status", "hot", "jobImagePreview", "setJobImagePreview", "thumbnailPreview", "setThumbnailPreview", "logoPreview", "setLogoPreview", "availableSubTopics", "setAvailableSubTopics", "loadSubTopicsFromStorage", "activeSubTopicNames", "length", "defaultSubTopics", "handleSubTopicsUpdate", "event", "log", "window", "addEventListener", "removeEventListener", "fetchJobs", "response", "getAll", "formattedJobs", "data", "map", "job", "dateToUse", "created_at", "posted_date", "salaryDisplay", "id", "job_id", "company", "position", "location", "workTime", "cv_email", "salary", "datePosted", "rawDatePosted", "views", "view_count", "sortedJobs", "sort", "a", "b", "dateA", "dateB", "checkAndDeleteExpiredJobs", "currentDate", "twoWeeksInMs", "allJobs", "expiredJobs", "filter", "endDate", "timeDifference", "<PERSON><PERSON><PERSON>", "delete", "deleteErr", "updatedResponse", "intervalId", "setInterval", "clearInterval", "fetchCompanies", "companiesData", "matchingCompany", "find", "company_logo_url", "handleSearchChange", "e", "target", "value", "handleEditJob", "getById", "fullJobDetails", "formatDate", "warn", "String", "getMonth", "padStart", "getDate", "companyLogoUrl", "companiesResponse", "expLevel", "includes", "trim", "startsWith", "JSON", "parse", "Array", "isArray", "handleDeleteJob", "jobId", "confirmDelete", "cancelDelete", "handleToggleStatus", "jobToUpdate", "newStatus", "openNewJobModal", "closeModal", "sanitizeInputText", "text", "replace", "handleFormChange", "name", "type", "checked", "handleImageUpload", "imageType", "file", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSelectCompany", "handleRemoveLogo", "handleFormSubmit", "preventDefault", "emailRegex", "urlRegex", "test", "experienceLevelToSend", "sanitizedTitle", "sanitizedForm", "formData", "FormData", "dbFields", "for<PERSON>ach", "key", "undefined", "cleanSubTopics", "topic", "append", "stringify", "File", "pair", "entries", "get", "update", "apiError", "jobsResponse", "create", "_apiError$response", "_apiError$response2", "_apiError$response3", "message", "statusText", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "errorMessage", "filteredJobs", "matchesSearch", "toLowerCase", "handleClickOutside", "dropdownElement", "document", "querySelector", "contains", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "style", "paddingLeft", "spin", "size", "marginRight", "title", "slice", "href", "display", "colSpan", "onSubmit", "htmlFor", "color", "required", "src", "alt", "company_id", "accept", "getElementById", "click", "min", "max", "option", "step", "textAlign", "marginLeft", "padding", "fontSize", "prev", "backgroundColor", "border", "borderRadius", "fontStyle", "index", "set", "Set", "add", "from", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/Admin/JobsAdmin.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable no-unused-vars */\r\n/* eslint-disable no-useless-escape */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n  faPlus,\r\n  faEdit,\r\n  faTrash,\r\n  faSearch,\r\n  faExclamationCircle,\r\n  faCheckCircle,\r\n  faEye,\r\n  faFire,\r\n  faTimes,\r\n  faBuilding,\r\n  faImage,\r\n  faSpinner,\r\n  faChevronDown,\r\n  faExclamationTriangle,\r\n  faSync\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport '../../css/JobsAdmin.css';\r\nimport '../../css/JobFormModal.css';\r\nimport '../../css/shared-delete-dialog.css';\r\nimport ApiService from '../../services/apiService';\r\nimport { getActiveSubTopicNames, getDefaultSubTopics } from '../../utils/subTopicsUtils';\r\n// Import search-fix.css last to ensure it takes precedence\r\nimport '../../css/search-fix.css';\r\n\r\nconst EXPERIENCE_OPTIONS = [\r\n  \"No Experience Required\",\r\n  \"Entry Level\",\r\n  \"Mid Level\",\r\n  \"Senior Level\",\r\n  \"Manager\",\r\n  \"Executive\"\r\n];\r\n\r\nconst JobsAdmin = () => {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedJob, setSelectedJob] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\r\n  \r\n  // Delete confirmation dialog state\r\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n  const [jobToDelete, setJobToDelete] = useState(null);\r\n  \r\n  // New job form state\r\n  // Get today's date in YYYY-MM-DD format for the date inputs\r\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\r\n  \r\n  // Format date for display in table\r\n  const formatDisplayDate = (dateString) => {\r\n    if (!dateString || dateString === '-') return '-';\r\n    try {\r\n      const date = new Date(dateString);\r\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\r\n      return date.toLocaleDateString('en-US', { \r\n        year: 'numeric', \r\n        month: 'short', \r\n        day: 'numeric' \r\n      });\r\n    } catch (err) {\r\n      console.error(\"Error formatting display date:\", err);\r\n      return dateString;\r\n    }\r\n  };\r\n    const [newJobForm, setNewJobForm] = useState({\r\n    job_title: '',\r\n    year: new Date().getFullYear(),\r\n    start_date: getTodayFormatted(),\r\n    end_date: '',\r\n    send_cv_email: '',\r\n    main_topics: 'Government Jobs', // Set a default valid value from the enum\r\n    sub_topics: [],\r\n    job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n    experience_level: 'Entry Level', // New field for experience level\r\n    min_salary: '',\r\n    max_salary: '',\r\n    job_description: '',\r\n    job_post_image: null,\r\n    job_post_thumbnail: null,\r\n    company_name: '',\r\n    company_logo: null,\r\n    // UI-only fields (not in database)\r\n    status: 'Active',\r\n    hot: false\r\n  });\r\n  \r\n  // Image preview states\r\n  const [jobImagePreview, setJobImagePreview] = useState(null);\r\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\r\n  const [logoPreview, setLogoPreview] = useState(null);\r\n  // State for managing sub-topics\r\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\r\n\r\n  // Load available sub-topics from localStorage\r\n  const loadSubTopicsFromStorage = () => {\r\n    try {\r\n      const activeSubTopicNames = getActiveSubTopicNames();\r\n      if (activeSubTopicNames.length > 0) {\r\n        setAvailableSubTopics(activeSubTopicNames);\r\n      } else {\r\n        // Use default sub-topics if no saved data\r\n        const defaultSubTopics = getDefaultSubTopics();\r\n        setAvailableSubTopics(defaultSubTopics);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error loading sub-topics from localStorage:\", err);\r\n      // Fallback to default sub-topics\r\n      const defaultSubTopics = getDefaultSubTopics();\r\n      setAvailableSubTopics(defaultSubTopics);\r\n    }\r\n  };\r\n\r\n  // Initialize available sub-topics\r\n  useEffect(() => {\r\n    loadSubTopicsFromStorage();\r\n  }, []);\r\n\r\n  // Listen for sub-topics updates from SubTopicsAdmin\r\n  useEffect(() => {\r\n    const handleSubTopicsUpdate = (event) => {\r\n      console.log(\"Sub-topics updated, refreshing available options...\");\r\n      loadSubTopicsFromStorage();\r\n    };\r\n\r\n    // Add event listener for sub-topics updates\r\n    window.addEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener('subTopicsUpdated', handleSubTopicsUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Fetch jobs from backend\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await ApiService.jobs.getAll();\r\n        \r\n        // Transform backend data to match the format used in frontend\r\n        // Adding default values for fields that don't exist in the database\r\n        const formattedJobs = response.data.map(job => {\r\n          // Determine the best date to use\r\n          let dateToUse;\r\n          if (job.created_at) {\r\n            // Prefer created_at if available\r\n            dateToUse = job.created_at;\r\n          } else if (job.start_date) {\r\n            // Otherwise use start_date\r\n            dateToUse = job.start_date;\r\n          } else if (job.posted_date) {\r\n            // Fall back to posted_date\r\n            dateToUse = job.posted_date;\r\n          } else {\r\n            // If no dates are available, use current date\r\n            dateToUse = new Date().toISOString();\r\n          }\r\n\r\n          // Compose salary display string\r\n          let salaryDisplay = '';\r\n          if (job.min_salary && job.max_salary) {\r\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n          } else if (job.min_salary) {\r\n            salaryDisplay = `${job.min_salary}`;\r\n          } else if (job.max_salary) {\r\n            salaryDisplay = `${job.max_salary}`;\r\n          } else {\r\n            salaryDisplay = '';\r\n          }\r\n            return {\r\n            id: job.job_id,\r\n            hot: job.hot || false, // Use actual hot value from database\r\n            company: job.company_name,\r\n            position: job.job_title,\r\n            location: job.main_topics,\r\n            workTime: job.job_type,\r\n            experience_level: job.experience_level,\r\n            cv_email: job.send_cv_email,\r\n            salary: salaryDisplay,\r\n            status: 'Active', // Default since it doesn't exist in DB\r\n            datePosted: dateToUse,\r\n            rawDatePosted: job.start_date || job.posted_date || job.created_at, // Store raw date for debugging\r\n            views: job.view_count || 0, // Use actual view count if available\r\n            end_date: job.end_date // Add end_date for auto-delete functionality\r\n          };\r\n        });\r\n        \r\n        // Sort jobs by creation date descending (newest first), then by ID descending as fallback\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => {\r\n          // First, try to sort by creation date\r\n          const dateA = new Date(a.datePosted);\r\n          const dateB = new Date(b.datePosted);\r\n\r\n          if (dateB.getTime() !== dateA.getTime()) {\r\n            return dateB.getTime() - dateA.getTime();\r\n          }\r\n\r\n          // If dates are the same, sort by ID descending (higher ID = newer)\r\n          return b.id - a.id;\r\n        });\r\n        setJobs(sortedJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching jobs:\", err);\r\n        setError(\"Failed to load jobs from server\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchJobs();\r\n  }, []);\r\n\r\n  // Auto-delete expired jobs (2 weeks after end date)\r\n  useEffect(() => {\r\n    const checkAndDeleteExpiredJobs = async () => {\r\n      try {\r\n        const currentDate = new Date();\r\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\r\n        \r\n        // Get all jobs to check for expired ones\r\n        const response = await ApiService.jobs.getAll();\r\n        const allJobs = response.data;\r\n        \r\n        const expiredJobs = allJobs.filter(job => {\r\n          if (!job.end_date) return false; // Skip jobs without end date\r\n          \r\n          const endDate = new Date(job.end_date);\r\n          const timeDifference = currentDate.getTime() - endDate.getTime();\r\n          \r\n          // Check if job is expired by more than 2 weeks\r\n          return timeDifference > twoWeeksInMs;\r\n        });\r\n        \r\n        // Delete expired jobs\r\n        for (const expiredJob of expiredJobs) {\r\n          try {\r\n            await ApiService.jobs.delete(expiredJob.job_id);\r\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\r\n          } catch (deleteErr) {\r\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\r\n          }\r\n        }\r\n        \r\n        // If any jobs were deleted, refresh the jobs list\r\n        if (expiredJobs.length > 0) {\r\n          // Refresh jobs list by fetching again\r\n          const updatedResponse = await ApiService.jobs.getAll();\r\n          const formattedJobs = updatedResponse.data.map(job => {\r\n            let dateToUse;\r\n            if (job.created_at) {\r\n              dateToUse = job.created_at;\r\n            } else if (job.start_date) {\r\n              dateToUse = job.start_date;\r\n            } else if (job.posted_date) {\r\n              dateToUse = job.posted_date;\r\n            } else {\r\n              dateToUse = new Date().toISOString();\r\n            }\r\n\r\n            let salaryDisplay = '';\r\n            if (job.min_salary && job.max_salary) {\r\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n            } else if (job.min_salary) {\r\n              salaryDisplay = `${job.min_salary}`;\r\n            } else if (job.max_salary) {\r\n              salaryDisplay = `${job.max_salary}`;\r\n            } else {\r\n              salaryDisplay = '';\r\n            }\r\n            \r\n            return {\r\n              id: job.job_id,\r\n              hot: job.hot || false,\r\n              company: job.company_name,\r\n              position: job.job_title,\r\n              location: job.main_topics,\r\n              workTime: job.job_type,\r\n              experience_level: job.experience_level,\r\n              cv_email: job.send_cv_email,\r\n              salary: salaryDisplay,\r\n              status: 'Active',\r\n              datePosted: dateToUse,\r\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n              views: job.view_count || 0,\r\n              end_date: job.end_date\r\n            };\r\n          });\r\n          \r\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n          setJobs(sortedJobs);\r\n        }\r\n        \r\n      } catch (err) {\r\n        console.error(\"Error checking for expired jobs:\", err);\r\n        // Don't set error state here to avoid disrupting normal operation\r\n      }\r\n    };\r\n\r\n    // Run the check immediately when component mounts\r\n    checkAndDeleteExpiredJobs();\r\n    \r\n    // Set up interval to check every hour (3600000 ms)\r\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\r\n    \r\n    // Cleanup interval on component unmount\r\n    return () => clearInterval(intervalId);\r\n  }, []);\r\n\r\n  // Fetch companies when modal is opened\r\n  useEffect(() => {\r\n    if (isModalOpen) {\r\n      console.log(\"Modal opened, fetching companies...\");\r\n      fetchCompanies();\r\n    }\r\n  }, [isModalOpen]);\r\n\r\n  const fetchCompanies = async () => {\r\n    try {\r\n      console.log(\"Fetching companies from API...\");\r\n      const response = await ApiService.companies.getAll();\r\n      const companiesData = response.data.data || [];\r\n      console.log(\"Companies fetched:\", companiesData);\r\n      setCompanies(companiesData);\r\n      \r\n      // If we're editing a job and have a company name, select the matching company\r\n      if (selectedJob && newJobForm.company_name) {\r\n        const matchingCompany = companiesData.find(\r\n          company => company.company_name === newJobForm.company_name\r\n        );\r\n        \r\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\r\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\r\n          setLogoPreview(matchingCompany.company_logo_url);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching companies:', err);\r\n      // Don't set error state here to avoid disrupting the job form\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  const handleEditJob = async (job) => {\r\n    try {\r\n      setLoading(true);\r\n      // Fetch full job details from backend\r\n      const response = await ApiService.jobs.getById(job.id);\r\n      const fullJobDetails = response.data;\r\n      \r\n      console.log(\"Job details from API:\", fullJobDetails);\r\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\r\n      \r\n      setSelectedJob(job);\r\n      // Initialize form with job data - only using fields from the actual database schema\r\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\r\n      const formatDate = (dateString) => {\r\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\r\n        \r\n        try {\r\n          // Parse the date and ensure it's valid\r\n          const date = new Date(dateString);\r\n          if (isNaN(date.getTime())) {\r\n            console.warn(\"Invalid date detected:\", dateString);\r\n            return getTodayFormatted(); // Default to today for invalid dates\r\n          }\r\n          \r\n          // Format as YYYY-MM-DD for date input\r\n          const year = date.getFullYear();\r\n          const month = String(date.getMonth() + 1).padStart(2, '0');\r\n          const day = String(date.getDate()).padStart(2, '0');\r\n          return `${year}-${month}-${day}`;\r\n        } catch (err) {\r\n          console.error(\"Error formatting date:\", err);\r\n          return getTodayFormatted();\r\n        }\r\n      };\r\n      \r\n      // Set image previews\r\n      if (fullJobDetails.job_post_image) {\r\n        setJobImagePreview(fullJobDetails.job_post_image);\r\n      }\r\n      \r\n      if (fullJobDetails.job_post_thumbnail) {\r\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\r\n      }\r\n      \r\n      // Handle company logo preview\r\n      let companyLogoUrl = null;\r\n      if (fullJobDetails.company_logo) {\r\n        setLogoPreview(fullJobDetails.company_logo);\r\n        companyLogoUrl = fullJobDetails.company_logo;\r\n      } else if (fullJobDetails.company_logo_url) {\r\n        setLogoPreview(fullJobDetails.company_logo_url);\r\n        companyLogoUrl = fullJobDetails.company_logo_url;\r\n      } else {\r\n        // Fetch companies to find the logo\r\n        try {\r\n          const companiesResponse = await ApiService.companies.getAll();\r\n          const companiesData = companiesResponse.data.data || [];\r\n          \r\n          const matchingCompany = companiesData.find(\r\n            company => company.company_name === fullJobDetails.company_name\r\n          );\r\n          \r\n          if (matchingCompany && matchingCompany.company_logo_url) {\r\n            setLogoPreview(matchingCompany.company_logo_url);\r\n            companyLogoUrl = matchingCompany.company_logo_url;\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error finding company logo:\", err);\r\n        }\r\n      }\r\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\r\n      let expLevel = fullJobDetails.experience_level;\r\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\r\n        expLevel = \"No Experience Required\";\r\n      }\r\n      setNewJobForm({\r\n        job_title: fullJobDetails.job_title,\r\n        company_name: fullJobDetails.company_name,\r\n        year: fullJobDetails.year || new Date().getFullYear(),\r\n        start_date: formatDate(fullJobDetails.start_date),\r\n        end_date: formatDate(fullJobDetails.end_date),\r\n        job_type: fullJobDetails.job_type,\r\n        experience_level: expLevel,\r\n        min_salary: fullJobDetails.min_salary || '',\r\n        max_salary: fullJobDetails.max_salary || '',\r\n        send_cv_email: fullJobDetails.send_cv_email || '',\r\n        main_topics: fullJobDetails.main_topics,\r\n        sub_topics: (() => {\r\n          try {\r\n            // Try to parse sub_topics as JSON if it's a string\r\n            if (!fullJobDetails.sub_topics) return [];\r\n            \r\n            if (typeof fullJobDetails.sub_topics === 'string') {\r\n              // Check if it starts with [ which would indicate a likely JSON array\r\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\r\n                return JSON.parse(fullJobDetails.sub_topics);\r\n              } else {\r\n                // If it's not a JSON array, treat it as a single item\r\n                return [fullJobDetails.sub_topics];\r\n              }\r\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\r\n              return fullJobDetails.sub_topics;\r\n            } else {\r\n              return [];\r\n            }\r\n          } catch (err) {\r\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\r\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\r\n          }\r\n        })(),\r\n        job_description: fullJobDetails.job_description || '',\r\n        job_post_image: fullJobDetails.job_post_image,\r\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\r\n        company_logo: fullJobDetails.company_logo,\r\n        company_logo_url: companyLogoUrl, // Store the company logo URL\r\n        // Use default values for UI-only fields that don't exist in the database\r\n        status: 'Active',\r\n        hot: fullJobDetails.hot || false // Use actual hot value from database\r\n      });\r\n      \r\n      setIsModalOpen(true);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error fetching job details:\", err);\r\n      setError(`Failed to load job details for ${job.position}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteJob = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      await ApiService.jobs.delete(jobId);\r\n      setJobs(jobs.filter(job => job.id !== jobId));\r\n      setError(null);\r\n      setShowDeleteConfirm(false);\r\n      setJobToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting job:\", err);\r\n      setError(\"Failed to delete job\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to show delete confirmation\r\n  const confirmDelete = (job) => {\r\n    setJobToDelete(job);\r\n    setShowDeleteConfirm(true);\r\n  };\r\n\r\n  // Function to cancel delete\r\n  const cancelDelete = () => {\r\n    setShowDeleteConfirm(false);\r\n    setJobToDelete(null);\r\n  };\r\n\r\n  const handleToggleStatus = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      const jobToUpdate = jobs.find(job => job.id === jobId);\r\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\r\n      \r\n      // Since we don't have a status column in the database,\r\n      // we'll just update the local state without sending to the backend\r\n      // In a real application, you would add the status column to the database\r\n      \r\n      // Update local state only\r\n      setJobs(jobs.map(job => \r\n        job.id === jobId \r\n          ? {...job, status: newStatus} \r\n          : job\r\n      ));\r\n      \r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error updating job status:\", err);\r\n      setError(\"Failed to update job status\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n    const openNewJobModal = () => {\r\n    setIsModalOpen(true);\r\n    setSelectedJob(null);\r\n  };  \r\n    const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setSelectedJob(null);\r\n    // Reset form\r\n    setNewJobForm({\r\n      job_title: '',\r\n      year: new Date().getFullYear(),\r\n      start_date: getTodayFormatted(),\r\n      end_date: '',\r\n      send_cv_email: '',\r\n      main_topics: 'Government Jobs', // Ensure we always have a valid value\r\n      sub_topics: [],\r\n      job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n      experience_level: 'Entry Level',\r\n      min_salary: '',\r\n      max_salary: '',\r\n      job_description: '',\r\n      job_post_image: null,\r\n      job_post_thumbnail: null,\r\n      company_name: '',\r\n      company_logo: null,\r\n      // UI-only fields (not in database)\r\n      status: 'Active',\r\n      hot: false\r\n    });\r\n    // Reset previews\r\n    setJobImagePreview(null);\r\n    setThumbnailPreview(null);\r\n    setLogoPreview(null);\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Basic client-side text sanitization\r\n  const sanitizeInputText = (text) => {\r\n    if (!text) return '';\r\n    // Modified to preserve Unicode characters in the job title\r\n    // Only remove control characters and potentially problematic chars\r\n    return text\r\n      .replace(/[--]/g, '') // Remove control characters\r\n      .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\r\n      .trim();\r\n  };\r\n  \r\n  const handleFormChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    \r\n    // Apply sanitization to text fields that might contain problematic characters\r\n    if (name === 'job_title' || name === 'job_description') {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\r\n      });\r\n    } else {\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    });\r\n    }\r\n  };\r\n  \r\n  const handleImageUpload = (e, imageType) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        if (imageType === 'job_post_image') {\r\n          setJobImagePreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_image: file });\r\n        } else if (imageType === 'job_post_thumbnail') {\r\n          setThumbnailPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_thumbnail: file });\r\n        } else if (imageType === 'company_logo') {\r\n          setLogoPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, company_logo: file });\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSelectCompany = (company) => {\r\n    console.log(\"Company selected:\", company);\r\n    \r\n    // Validate company has a name\r\n    if (!company || !company.company_name) {\r\n      console.error(\"Invalid company selected\");\r\n      return;\r\n    }\r\n    \r\n    // Set company name in the form\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      company_name: company.company_name,\r\n      company_logo: null, // Reset the file input since we're using an existing logo\r\n      company_logo_url: company.company_logo_url // Store the logo URL\r\n    });\r\n    \r\n    // Set logo preview\r\n    if (company.company_logo_url) {\r\n      setLogoPreview(company.company_logo_url);\r\n    } else {\r\n      setLogoPreview(null);\r\n    }\r\n    \r\n    // Close the dropdown\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Add function to remove logo\r\n  const handleRemoveLogo = () => {\r\n    setLogoPreview(null);\r\n    // If there was a logo file in the form, clear it\r\n    if (newJobForm.company_logo) {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        company_logo: null\r\n      });\r\n    }\r\n  };\r\n  \r\n  const handleFormSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Debug the form state\r\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\r\n    console.log(\"Original form state:\", newJobForm);\r\n    console.log(\"Selected job:\", selectedJob);\r\n    \r\n    // Client-side validation for required fields\r\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\r\n      setError(\"Job title is required\");\r\n      return;\r\n    }\r\n    \r\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\r\n      setError(\"Company name is required\");\r\n      return;\r\n    }\r\n\r\n    // Validate email/URL field if provided\r\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\r\n      \r\n      const value = newJobForm.send_cv_email.trim();\r\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\r\n        setError(\"Please enter a valid email address or website URL\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\r\n    let experienceLevelToSend = newJobForm.experience_level;\r\n    if (!experienceLevelToSend) {\r\n      experienceLevelToSend = \"No Experience Required\";\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Create a sanitized copy of the form data to send to the server\r\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\r\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\r\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\r\n      \r\n      const sanitizedForm = {\r\n        ...newJobForm,\r\n        job_title: sanitizedTitle,\r\n        job_description: sanitizeInputText(newJobForm.job_description),\r\n        experience_level: experienceLevelToSend,\r\n        // For new jobs, set today's date as the start_date if not provided\r\n        start_date: selectedJob ? newJobForm.start_date : (newJobForm.start_date || getTodayFormatted())\r\n      };\r\n      \r\n      console.log(\"Sanitized form data:\", sanitizedForm);\r\n      \r\n      // Prepare form data for API - create a completely fresh FormData object\r\n      const formData = new FormData();\r\n        // Add only fields that exist in the database schema, ensuring each field is only added once\r\n      const dbFields = [\r\n        'job_title', 'company_name', 'year', 'start_date', 'end_date',\r\n        'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level',\r\n        'min_salary', 'max_salary', 'job_description', 'hot'\r\n      ];\r\n      \r\n      // Add text fields one by one to avoid duplicates\r\n      dbFields.forEach(key => {\r\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\r\n          // Special handling for sub_topics\r\n          if (key === 'sub_topics') {\r\n            try {\r\n              if (Array.isArray(sanitizedForm[key])) {\r\n                // Ensure we're sending a clean array without unexpected characters\r\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\r\n                formData.append(key, JSON.stringify(cleanSubTopics));\r\n              } else {\r\n                // If it's a string or something else, convert to array\r\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\r\n              }\r\n            } catch (err) {\r\n              console.error(\"Error formatting sub_topics:\", err);\r\n              formData.append(key, '[]'); // Fallback to empty array\r\n            }\r\n          } else {\r\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\r\n            formData.append(key, sanitizedForm[key]);\r\n          }\r\n        }\r\n      });\r\n      \r\n      // Add file fields if they exist\r\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\r\n        formData.append('job_post_image', sanitizedForm.job_post_image);\r\n      }\r\n      \r\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\r\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\r\n      }      \r\n      \r\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\r\n        formData.append('company_logo', sanitizedForm.company_logo);\r\n      }\r\n      \r\n      // If we have a logo preview from an existing company but no file, add the URL\r\n      if (logoPreview && !sanitizedForm.company_logo) {\r\n        formData.append('existing_company_logo_url', logoPreview);\r\n      }\r\n      \r\n      // Add company_logo_url if it exists\r\n      if (sanitizedForm.company_logo_url) {\r\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\r\n      }\r\n      \r\n      // Debug: Log what's being sent in the FormData\r\n      console.log(\"FormData contents:\");\r\n      for (let pair of formData.entries()) {\r\n        console.log(pair[0] + ': ' + pair[1]);\r\n      }\r\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\r\n      \r\n      // Prepare form data for submission\r\n      if (selectedJob) {\r\n        // Update existing job\r\n        try {\r\n          await ApiService.jobs.update(selectedJob.id, formData);\r\n          // Successfully updated job\r\n        } catch (apiError) {\r\n          // Handle update error silently\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      } else {\r\n        // Create new job\r\n        try {\r\n          await ApiService.jobs.create(formData);\r\n          // Successfully created job\r\n        } catch (apiError) {\r\n          // Log detailed error information\r\n          console.error(\"API Error Details:\", {\r\n            message: apiError.message,\r\n            status: apiError.response?.status,\r\n            statusText: apiError.response?.statusText,\r\n            data: apiError.response?.data\r\n          });\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      }\r\n      \r\n      setError(null);\r\n      // Close modal\r\n      closeModal();\r\n    } catch (err) {\r\n      console.error(\"Error saving job:\", err);\r\n      // Show more detailed error message if available\r\n      const errorMessage = err.response?.data?.message || \r\n                          err.response?.data?.error || \r\n                          (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const filteredJobs = jobs.filter(job => {\r\n    const matchesSearch = (job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase())) || \r\n                          (job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase()));\r\n    \r\n    return matchesSearch;\r\n  });\r\n\r\n  // Add this useEffect to handle closing the dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      const dropdownElement = document.querySelector('.company-select-dropdown');\r\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\r\n        setShowCompanyDropdown(false);\r\n      }\r\n    };\r\n\r\n    if (showCompanyDropdown) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n      return () => {\r\n        document.removeEventListener('mousedown', handleClickOutside);\r\n      };\r\n    }\r\n  }, [showCompanyDropdown]);\r\n\r\n  return (\r\n    <div className=\"jobs-admin-container\">\r\n      {/* Delete Confirmation Dialog */}\r\n      {showDeleteConfirm && (\r\n        <div className=\"delete-confirm-overlay\">\r\n          <div className=\"delete-confirm-dialog\">\r\n            <div className=\"delete-confirm-header\">\r\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"delete-icon\" />\r\n              <h3>Confirm Deletion</h3>\r\n            </div>\r\n            <div className=\"delete-confirm-content\">\r\n              <p>Are you sure you want to delete this job posting?</p>\r\n              <p><strong>{jobToDelete?.position}</strong></p>\r\n              <p>This action cannot be undone.</p>\r\n            </div>\r\n            <div className=\"delete-confirm-actions\">\r\n              <button \r\n                className=\"cancel-delete-btn\" \r\n                onClick={cancelDelete}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button \r\n                className=\"confirm-delete-btn\" \r\n                onClick={() => handleDeleteJob(jobToDelete.id)}\r\n              >\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"jobs-header\">\r\n        <h1 className=\"jobs-title\">Job Listings</h1>\r\n        <div className=\"job-header-actions\">\r\n          <button className=\"refresh-button\" onClick={() => {\r\n            setLoading(true);\r\n            const fetchJobs = async () => {\r\n              try {\r\n                const response = await ApiService.jobs.getAll();\r\n                \r\n                // Transform backend data to match the format used in frontend\r\n                const formattedJobs = response.data.map(job => {\r\n                  // Determine the best date to use\r\n                  let dateToUse;\r\n                  if (job.created_at) {\r\n                    dateToUse = job.created_at;\r\n                  } else if (job.start_date) {\r\n                    dateToUse = job.start_date;\r\n                  } else if (job.posted_date) {\r\n                    dateToUse = job.posted_date;\r\n                  } else {\r\n                    dateToUse = new Date().toISOString();\r\n                  }\r\n\r\n                  // Compose salary display string\r\n                  let salaryDisplay = '';\r\n                  if (job.min_salary && job.max_salary) {\r\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n                  } else if (job.min_salary) {\r\n                    salaryDisplay = `${job.min_salary}`;\r\n                  } else if (job.max_salary) {\r\n                    salaryDisplay = `${job.max_salary}`;\r\n                  } else {\r\n                    salaryDisplay = '';\r\n                  }\r\n                    return {\r\n                    id: job.job_id,\r\n                    hot: job.hot || false,\r\n                    company: job.company_name,\r\n                    position: job.job_title,\r\n                    location: job.main_topics,\r\n                    workTime: job.job_type,\r\n                    experience_level: job.experience_level,\r\n                    cv_email: job.send_cv_email,\r\n                    salary: salaryDisplay,\r\n                    status: 'Active',\r\n                    datePosted: dateToUse,\r\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n                    views: job.view_count || 0,\r\n                    end_date: job.end_date\r\n                  };\r\n                });\r\n                \r\n                // Sort jobs by datePosted descending (newest first)\r\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n                setJobs(sortedJobs);\r\n                setError(null);\r\n              } catch (err) {\r\n                console.error(\"Error fetching jobs:\", err);\r\n                setError(\"Failed to load jobs from server\");\r\n              } finally {\r\n                setLoading(false);\r\n              }\r\n            };\r\n\r\n            fetchJobs();\r\n          }} disabled={loading}>\r\n            <FontAwesomeIcon icon={faSync} />\r\n            <span>Refresh</span>\r\n          </button>\r\n          <button className=\"add-job-button\" onClick={openNewJobModal} disabled={loading}>\r\n            <FontAwesomeIcon icon={faPlus} />\r\n            <span>Add New Job</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"error-message\">\r\n          <FontAwesomeIcon icon={faExclamationCircle} />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"filters-container\">\r\n        <div className=\"search-container\">\r\n          <div className=\"search-input-wrapper\">\r\n            <FontAwesomeIcon icon={faSearch} className=\"search-icon\" />          <input\r\n            type=\"text\"\r\n            placeholder=\"Search by company or position...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n            className=\"search-input\"\r\n            style={{ paddingLeft: '40px' }}\r\n          />\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div className=\"table-container\">\r\n        {loading ? (\r\n          <div className=\"loading-container\">\r\n            <FontAwesomeIcon icon={faSpinner} spin size=\"2x\" />\r\n            <span>Loading jobs...</span>\r\n          </div>\r\n        ) : (\r\n          <>\r\n          <table className=\"jobs-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Job</th>\r\n                <th>Type</th>\r\n                <th>Experience Level</th>\r\n                <th>Email / Web URL</th>\r\n                <th>Salary</th>\r\n                <th>Expire Date</th>\r\n                <th>Posted Date</th>\r\n                <th>Views</th>\r\n                <th>Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {filteredJobs.length > 0 ? (\r\n                filteredJobs.map(job => (\r\n                  <tr key={job.id}>\r\n                    <td>\r\n                      {job.hot && (\r\n                        <span className=\"hot-badge\">\r\n                          <FontAwesomeIcon icon={faFire} style={{ marginRight: '4px' }} />\r\n                          URGENT\r\n                        </span>\r\n                      )}\r\n<div className=\"job-position\" title={job.position}>\r\n  {job.position && job.position.length > 35\r\n    ? job.position.slice(0, 32) + '...'\r\n    : job.position}\r\n</div>\r\n                      <div className=\"job-company\">{job.company}</div>\r\n                    </td>\r\n                    <td>{job.workTime}</td>\r\n                    <td>\r\n                      <div className=\"experience-level\" title={job.experience_level}>\r\n                        {job.experience_level || 'Not specified'}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div className=\"cv-email\" title={job.cv_email}>\r\n                        {job.cv_email ? (\r\n                          <a href={`mailto:${job.cv_email}`} className=\"email-link\">\r\n                            {job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email}\r\n                          </a>\r\n                        ) : (\r\n                          'Not provided'\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                    <td>{job.salary}</td>\r\n                    <td>{formatDisplayDate(job.end_date)}</td>\r\n                      <td>{formatDisplayDate(job.datePosted)}</td>\r\n                    <td>\r\n                      <div className=\"views-container\">\r\n                        <FontAwesomeIcon icon={faEye} className=\"views-icon\" />\r\n                        {job.views}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div style={{ display: 'flex' }}>\r\n                        <button \r\n                          className=\"action-button edit-button\" \r\n                          onClick={() => handleEditJob(job)}\r\n                          title=\"Edit job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faEdit} />\r\n                        </button>\r\n                        {/* Removed Inactive/Active toggle button */}\r\n                        <button \r\n                          className=\"action-button delete-button\" \r\n                          onClick={() => confirmDelete(job)}\r\n                          title=\"Delete job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faTrash} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td colSpan=\"9\" className=\"no-jobs-message\">\r\n                    {searchTerm ? \r\n                      \"No jobs match your search criteria\" : \r\n                      \"No jobs available\"\r\n                    }\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* New Job Modal */}\r\n      {isModalOpen && (\r\n        <div className=\"modal-overlay\" onClick={(e) => {\r\n          // Close modal when clicking outside\r\n          if (e.target.className === 'modal-overlay') {\r\n            closeModal();\r\n          }\r\n        }}>\r\n          <div className=\"job-modal\">\r\n            <div className=\"modal-header\">\r\n              <h2>{selectedJob ? 'Edit Job' : 'Add New Job'}</h2>\r\n              <button className=\"close-button\" onClick={closeModal}>\r\n                <FontAwesomeIcon icon={faTimes} />\r\n              </button>\r\n            </div>\r\n            <div className=\"modal-content\">\r\n              <form onSubmit={handleFormSubmit}>\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_title\">Job Title <span style={{ color: 'red' }}>*</span></label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"job_title\"\r\n                      name=\"job_title\"\r\n                      value={newJobForm.job_title}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter job title\"\r\n                    />\r\n                    </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Company <span style={{ color: 'red' }}>*</span></label>\r\n                    <div className=\"company-selector\">\r\n                      <div className=\"company-select-dropdown\">\r\n                        <button \r\n                          type=\"button\"\r\n                          className=\"company-select-button\"\r\n                          onClick={() => setShowCompanyDropdown(!showCompanyDropdown)}\r\n                        >\r\n                          {newJobForm.company_name ? newJobForm.company_name : 'Select a company'}\r\n                          <FontAwesomeIcon icon={faChevronDown} />\r\n                        </button>\r\n                        \r\n                        {showCompanyDropdown && (\r\n                          <div className=\"company-list-dropdown\">\r\n                            {companies && companies.length > 0 ? (\r\n                              companies.map(company => (\r\n                                <div \r\n                                  key={company.company_id}\r\n                                  className=\"company-item\"\r\n                                  onClick={() => {\r\n                                    handleSelectCompany(company);\r\n                                  }}\r\n                                >\r\n                                  <div className=\"company-item-logo\">\r\n                                    {company.company_logo_url ? (\r\n                                      <img src={company.company_logo_url} alt={company.company_name} />\r\n                                    ) : (\r\n                                      <FontAwesomeIcon icon={faBuilding} />\r\n                                    )}\r\n                                  </div>\r\n                                  <span>{company.company_name}</span>\r\n                                </div>\r\n                              ))\r\n                            ) : (\r\n                              <div className=\"no-companies\">No companies available</div>\r\n                            )}\r\n                            <div className=\"manual-company-entry\">\r\n                              <label>Or enter company name manually:</label>\r\n                              <input\r\n                                type=\"text\"\r\n                                placeholder=\"Enter company name\"\r\n                                value={newJobForm.company_name || ''}\r\n                                onChange={(e) => {\r\n                                  setNewJobForm({\r\n                                    ...newJobForm,\r\n                                    company_name: e.target.value\r\n                                  });\r\n                                }}\r\n                              />\r\n                              <button\r\n                                type=\"button\"\r\n                                className=\"apply-company-btn\"\r\n                                onClick={() => setShowCompanyDropdown(false)}\r\n                              >\r\n                                Apply\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      \r\n                      {/* Display logo preview if available */}\r\n                      {logoPreview && (\r\n                        <div className=\"logo-preview-container\">\r\n                          <img src={logoPreview} alt=\"Company logo\" />\r\n                          <button \r\n                            type=\"button\" \r\n                            className=\"remove-logo-button\"\r\n                            onClick={handleRemoveLogo}\r\n                          >\r\n                            <FontAwesomeIcon icon={faTimes} />\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                      \r\n                      {/* Allow uploading a new logo if no existing one is selected */}\r\n                      {!logoPreview && (\r\n                        <div>\r\n                          <input\r\n                            type=\"file\"\r\n                            id=\"company_logo\"\r\n                            name=\"company_logo\"\r\n                            onChange={(e) => handleImageUpload(e, 'company_logo')}\r\n                            accept=\"image/*\"\r\n                            style={{ display: 'none' }}\r\n                          />\r\n                          <button\r\n                            type=\"button\"\r\n                            className=\"file-upload-label\"\r\n                            onClick={() => document.getElementById('company_logo').click()}\r\n                          >\r\n                            <FontAwesomeIcon icon={faImage} />\r\n                            Upload Company Logo\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"year\">Year</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"year\"\r\n                      name=\"year\"\r\n                      value={newJobForm.year}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      min=\"2000\"\r\n                      max=\"2100\"\r\n                    />\r\n                  </div>                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_type\">Job Type</label>\r\n                    <select\r\n                      id=\"job_type\"\r\n                      name=\"job_type\"\r\n                      value={newJobForm.job_type}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Full Time Jobs\">Full Time Jobs</option>\r\n                      <option value=\"Part Time Jobs\">Part Time Jobs</option>\r\n                      <option value=\"Remote Jobs\">Remote Jobs</option>\r\n                      <option value=\"Freelance\">Freelance</option>\r\n                      <option value=\"Temporary\">Temporary</option>\r\n                    </select>\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"experience_level\">Experience Level</label>\r\n                    <select\r\n                      id=\"experience_level\"\r\n                      name=\"experience_level\"\r\n                      value={newJobForm.experience_level}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      {EXPERIENCE_OPTIONS.map(option => (\r\n                        <option key={option} value={option}>{option}</option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"start_date\">Start Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"start_date\"\r\n                      name=\"start_date\"\r\n                      value={newJobForm.start_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"end_date\">End Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"end_date\"\r\n                      name=\"end_date\"\r\n                      value={newJobForm.end_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"send_cv_email\">Email/WebURL</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"send_cv_email\"\r\n                      name=\"send_cv_email\"\r\n                      value={newJobForm.send_cv_email}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Enter email or website URL for CV submissions (Optional)\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"min_salary\">Minimum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"min_salary\"\r\n                      name=\"min_salary\"\r\n                      value={newJobForm.min_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Minimum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"max_salary\">Maximum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"max_salary\"\r\n                      name=\"max_salary\"\r\n                      value={newJobForm.max_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Maximum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Mark as URGENT Job - Moved to middle */}\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group checkbox-group\" style={{textAlign: 'left'}}>\r\n                    <label className=\"checkbox-container\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"hot\"\r\n                        checked={newJobForm.hot}\r\n                        onChange={handleFormChange}\r\n                      />\r\n                      <span className=\"checkbox-text\">\r\n                        <FontAwesomeIcon icon={faFire} className=\"hot-icon\" />\r\n                        Mark as URGENT Job\r\n                      </span>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"main_topics\">Main Topic/Category</label>\r\n                    <select\r\n                      id=\"main_topics\"\r\n                      name=\"main_topics\"\r\n                      value={newJobForm.main_topics}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Government Jobs\">Government Jobs</option>\r\n                      <option value=\"Private Jobs\">Private Jobs</option>\r\n                      <option value=\"Foreign Jobs\">Foreign Jobs</option>\r\n                      <option value=\"Internships\">Internships</option>\r\n                    </select>\r\n                  </div>\r\n                </div>                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n<label>\r\n  <span>Sub Topics</span>\r\n  <span className=\"small-text\">{newJobForm.sub_topics.length} selected</span>\r\n  <button\r\n    type=\"button\"\r\n    style={{ marginLeft: '16px', padding: '2px 8px', fontSize: '0.9em' }}\r\n    onClick={() => setNewJobForm(prev => ({ ...prev, sub_topics: [] }))}\r\n  >\r\n    Clear All\r\n  </button>\r\n  <button\r\n    type=\"button\"\r\n    style={{ marginLeft: '8px', padding: '2px 8px', fontSize: '0.9em', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '3px' }}\r\n    onClick={loadSubTopicsFromStorage}\r\n    title=\"Refresh sub-topics list\"\r\n  >\r\n    <FontAwesomeIcon icon={faSync} style={{ marginRight: '4px' }} />\r\n    Refresh\r\n  </button>\r\n</label>\r\n<div className=\"subtopics-management-info\">\r\n  <small style={{ color: '#6c757d', fontStyle: 'italic' }}>\r\n    💡 Tip: You can manage available sub-topics from the \"Sub Topics\" section in the sidebar.\r\n    Click \"Refresh\" if you don't see newly added sub-topics.\r\n  </small>\r\n</div>\r\n<div className=\"subtopics-checkbox-container\">\r\n  {availableSubTopics.map((topic, index) => (\r\n                        <div \r\n                          key={index} \r\n                          className={`subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`}\r\n                        >\r\n<input\r\n  type=\"checkbox\"\r\n  id={`subtopic-${index}`}\r\n  checked={newJobForm.sub_topics.includes(topic)}\r\n  onChange={e => {\r\n    const checked = e.target.checked;\r\n    setNewJobForm(prev => {\r\n      const set = new Set(prev.sub_topics);\r\n      if (checked) {\r\n        set.add(topic);\r\n      } else {\r\n        set.delete(topic);\r\n      }\r\n      return {\r\n        ...prev,\r\n        sub_topics: Array.from(set)\r\n      };\r\n    });\r\n  }}\r\n                          />\r\n                          <label htmlFor={`subtopic-${index}`}>\r\n                            {topic}\r\n                          </label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n                    <label htmlFor=\"job_description\">Job Description</label>\r\n                    <textarea\r\n                      id=\"job_description\"\r\n                      name=\"job_description\"\r\n                      rows=\"5\"\r\n                      value={newJobForm.job_description}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter detailed job description\"\r\n                    ></textarea>\r\n                    <small className=\"form-help-text\">\r\n                      Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Image</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_image\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_image')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_image\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Image</span>\r\n                      </label>\r\n                    </div>\r\n                    {jobImagePreview && (\r\n                      <div className=\"image-preview\">\r\n                        <img src={jobImagePreview} alt=\"Job Post\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Thumbnail</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_thumbnail\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_thumbnail')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_thumbnail\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Thumbnail</span>\r\n                      </label>\r\n                    </div>\r\n                    {thumbnailPreview && (\r\n                      <div className=\"thumbnail-preview\">\r\n                        <img src={thumbnailPreview} alt=\"Thumbnail\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n                <div className=\"modal-footer\">\r\n                  <button type=\"button\" className=\"cancel-button\" onClick={closeModal}>\r\n                    Cancel\r\n                  </button>\r\n                  <button type=\"submit\" className=\"submit-button\">\r\n                    {selectedJob ? 'Update Job' : 'Create Job'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobsAdmin;\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,mBAAmB,EACnBC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,SAAS,EACTC,aAAa,EACbC,qBAAqB,EACrBC,MAAM,QACD,mCAAmC;AAC1C,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AACnC,OAAO,oCAAoC;AAC3C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,SAASC,sBAAsB,EAAEC,mBAAmB,QAAQ,4BAA4B;AACxF;AACA,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,kBAAkB,GAAG,CACzB,wBAAwB,EACxB,aAAa,EACb,WAAW,EACX,cAAc,EACd,SAAS,EACT,WAAW,CACZ;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA;EACA,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEtE;EACA,MAAMC,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,GAAG,EAAE,OAAO,GAAG;IACjD,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;MACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOH,UAAU,CAAC,CAAC;MAC9C,OAAOC,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;MACpD,OAAOR,UAAU;IACnB;EACF,CAAC;EACC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC;IAC7CkE,SAAS,EAAE,EAAE;IACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;IAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;IAC/BoB,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,iBAAiB;IAAE;IAChCC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,gBAAgB;IAAE;IAC5BC,gBAAgB,EAAE,aAAa;IAAE;IACjCC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClB;IACAC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACpD;EACA,MAAM,CAAC0F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM4F,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI;MACF,MAAMC,mBAAmB,GAAGzE,sBAAsB,CAAC,CAAC;MACpD,IAAIyE,mBAAmB,CAACC,MAAM,GAAG,CAAC,EAAE;QAClCH,qBAAqB,CAACE,mBAAmB,CAAC;MAC5C,CAAC,MAAM;QACL;QACA,MAAME,gBAAgB,GAAG1E,mBAAmB,CAAC,CAAC;QAC9CsE,qBAAqB,CAACI,gBAAgB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOjC,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,6CAA6C,EAAE6B,GAAG,CAAC;MACjE;MACA,MAAMiC,gBAAgB,GAAG1E,mBAAmB,CAAC,CAAC;MAC9CsE,qBAAqB,CAACI,gBAAgB,CAAC;IACzC;EACF,CAAC;;EAED;EACA9F,SAAS,CAAC,MAAM;IACd2F,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3F,SAAS,CAAC,MAAM;IACd,MAAM+F,qBAAqB,GAAIC,KAAK,IAAK;MACvClC,OAAO,CAACmC,GAAG,CAAC,qDAAqD,CAAC;MAClEN,wBAAwB,CAAC,CAAC;IAC5B,CAAC;;IAED;IACAO,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,qBAAqB,CAAC;;IAElE;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,kBAAkB,EAAEL,qBAAqB,CAAC;IACvE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/F,SAAS,CAAC,MAAM;IACd,MAAMqG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFtE,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMuE,QAAQ,GAAG,MAAMpF,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;;QAE/C;QACA;QACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;UAC7C;UACA,IAAIC,SAAS;UACb,IAAID,GAAG,CAACE,UAAU,EAAE;YAClB;YACAD,SAAS,GAAGD,GAAG,CAACE,UAAU;UAC5B,CAAC,MAAM,IAAIF,GAAG,CAACxC,UAAU,EAAE;YACzB;YACAyC,SAAS,GAAGD,GAAG,CAACxC,UAAU;UAC5B,CAAC,MAAM,IAAIwC,GAAG,CAACG,WAAW,EAAE;YAC1B;YACAF,SAAS,GAAGD,GAAG,CAACG,WAAW;UAC7B,CAAC,MAAM;YACL;YACAF,SAAS,GAAG,IAAI3D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtC;;UAEA;UACA,IAAI6D,aAAa,GAAG,EAAE;UACtB,IAAIJ,GAAG,CAACjC,UAAU,IAAIiC,GAAG,CAAChC,UAAU,EAAE;YACpCoC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,MAAMiC,GAAG,CAAChC,UAAU,EAAE;UACzD,CAAC,MAAM,IAAIgC,GAAG,CAACjC,UAAU,EAAE;YACzBqC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,EAAE;UACrC,CAAC,MAAM,IAAIiC,GAAG,CAAChC,UAAU,EAAE;YACzBoC,aAAa,GAAG,GAAGJ,GAAG,CAAChC,UAAU,EAAE;UACrC,CAAC,MAAM;YACLoC,aAAa,GAAG,EAAE;UACpB;UACE,OAAO;YACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;YACd/B,GAAG,EAAEyB,GAAG,CAACzB,GAAG,IAAI,KAAK;YAAE;YACvBgC,OAAO,EAAEP,GAAG,CAAC5B,YAAY;YACzBoC,QAAQ,EAAER,GAAG,CAAC1C,SAAS;YACvBmD,QAAQ,EAAET,GAAG,CAACrC,WAAW;YACzB+C,QAAQ,EAAEV,GAAG,CAACnC,QAAQ;YACtBC,gBAAgB,EAAEkC,GAAG,CAAClC,gBAAgB;YACtC6C,QAAQ,EAAEX,GAAG,CAACtC,aAAa;YAC3BkD,MAAM,EAAER,aAAa;YACrB9B,MAAM,EAAE,QAAQ;YAAE;YAClBuC,UAAU,EAAEZ,SAAS;YACrBa,aAAa,EAAEd,GAAG,CAACxC,UAAU,IAAIwC,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;YAAE;YACpEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;YAAE;YAC5BvD,QAAQ,EAAEuC,GAAG,CAACvC,QAAQ,CAAC;UACzB,CAAC;QACH,CAAC,CAAC;;QAEF;QACA,MAAMwD,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACnD;UACA,MAAMC,KAAK,GAAG,IAAI/E,IAAI,CAAC6E,CAAC,CAACN,UAAU,CAAC;UACpC,MAAMS,KAAK,GAAG,IAAIhF,IAAI,CAAC8E,CAAC,CAACP,UAAU,CAAC;UAEpC,IAAIS,KAAK,CAACzE,OAAO,CAAC,CAAC,KAAKwE,KAAK,CAACxE,OAAO,CAAC,CAAC,EAAE;YACvC,OAAOyE,KAAK,CAACzE,OAAO,CAAC,CAAC,GAAGwE,KAAK,CAACxE,OAAO,CAAC,CAAC;UAC1C;;UAEA;UACA,OAAOuE,CAAC,CAACf,EAAE,GAAGc,CAAC,CAACd,EAAE;QACpB,CAAC,CAAC;QACFnF,OAAO,CAAC+F,UAAU,CAAC;QACnB3F,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;QAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDsE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArG,SAAS,CAAC,MAAM;IACd,MAAMkI,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,IAAI;QACF,MAAMC,WAAW,GAAG,IAAIlF,IAAI,CAAC,CAAC;QAC9B,MAAMmF,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;QAE/C;QACA,MAAM9B,QAAQ,GAAG,MAAMpF,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;QAC/C,MAAM8B,OAAO,GAAG/B,QAAQ,CAACG,IAAI;QAE7B,MAAM6B,WAAW,GAAGD,OAAO,CAACE,MAAM,CAAC5B,GAAG,IAAI;UACxC,IAAI,CAACA,GAAG,CAACvC,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;UAEjC,MAAMoE,OAAO,GAAG,IAAIvF,IAAI,CAAC0D,GAAG,CAACvC,QAAQ,CAAC;UACtC,MAAMqE,cAAc,GAAGN,WAAW,CAAC3E,OAAO,CAAC,CAAC,GAAGgF,OAAO,CAAChF,OAAO,CAAC,CAAC;;UAEhE;UACA,OAAOiF,cAAc,GAAGL,YAAY;QACtC,CAAC,CAAC;;QAEF;QACA,KAAK,MAAMM,UAAU,IAAIJ,WAAW,EAAE;UACpC,IAAI;YACF,MAAMpH,UAAU,CAACU,IAAI,CAAC+G,MAAM,CAACD,UAAU,CAACzB,MAAM,CAAC;YAC/CnD,OAAO,CAACmC,GAAG,CAAC,6BAA6ByC,UAAU,CAACzE,SAAS,SAASyE,UAAU,CAACzB,MAAM,GAAG,CAAC;UAC7F,CAAC,CAAC,OAAO2B,SAAS,EAAE;YAClB9E,OAAO,CAAC9B,KAAK,CAAC,6BAA6B0G,UAAU,CAACzB,MAAM,GAAG,EAAE2B,SAAS,CAAC;UAC7E;QACF;;QAEA;QACA,IAAIN,WAAW,CAACzC,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMgD,eAAe,GAAG,MAAM3H,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;UACtD,MAAMC,aAAa,GAAGqC,eAAe,CAACpC,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;YACpD,IAAIC,SAAS;YACb,IAAID,GAAG,CAACE,UAAU,EAAE;cAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;YAC5B,CAAC,MAAM,IAAIF,GAAG,CAACxC,UAAU,EAAE;cACzByC,SAAS,GAAGD,GAAG,CAACxC,UAAU;YAC5B,CAAC,MAAM,IAAIwC,GAAG,CAACG,WAAW,EAAE;cAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;YAC7B,CAAC,MAAM;cACLF,SAAS,GAAG,IAAI3D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtC;YAEA,IAAI6D,aAAa,GAAG,EAAE;YACtB,IAAIJ,GAAG,CAACjC,UAAU,IAAIiC,GAAG,CAAChC,UAAU,EAAE;cACpCoC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,MAAMiC,GAAG,CAAChC,UAAU,EAAE;YACzD,CAAC,MAAM,IAAIgC,GAAG,CAACjC,UAAU,EAAE;cACzBqC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,EAAE;YACrC,CAAC,MAAM,IAAIiC,GAAG,CAAChC,UAAU,EAAE;cACzBoC,aAAa,GAAG,GAAGJ,GAAG,CAAChC,UAAU,EAAE;YACrC,CAAC,MAAM;cACLoC,aAAa,GAAG,EAAE;YACpB;YAEA,OAAO;cACLC,EAAE,EAAEL,GAAG,CAACM,MAAM;cACd/B,GAAG,EAAEyB,GAAG,CAACzB,GAAG,IAAI,KAAK;cACrBgC,OAAO,EAAEP,GAAG,CAAC5B,YAAY;cACzBoC,QAAQ,EAAER,GAAG,CAAC1C,SAAS;cACvBmD,QAAQ,EAAET,GAAG,CAACrC,WAAW;cACzB+C,QAAQ,EAAEV,GAAG,CAACnC,QAAQ;cACtBC,gBAAgB,EAAEkC,GAAG,CAAClC,gBAAgB;cACtC6C,QAAQ,EAAEX,GAAG,CAACtC,aAAa;cAC3BkD,MAAM,EAAER,aAAa;cACrB9B,MAAM,EAAE,QAAQ;cAChBuC,UAAU,EAAEZ,SAAS;cACrBa,aAAa,EAAEd,GAAG,CAACxC,UAAU,IAAIwC,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;cAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;cAC1BvD,QAAQ,EAAEuC,GAAG,CAACvC;YAChB,CAAC;UACH,CAAC,CAAC;UAEF,MAAMwD,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI9E,IAAI,CAAC8E,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIvE,IAAI,CAAC6E,CAAC,CAACN,UAAU,CAAC,CAAC;UACrG3F,OAAO,CAAC+F,UAAU,CAAC;QACrB;MAEF,CAAC,CAAC,OAAO/D,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;QACtD;MACF;IACF,CAAC;;IAED;IACAqE,yBAAyB,CAAC,CAAC;;IAE3B;IACA,MAAMY,UAAU,GAAGC,WAAW,CAACb,yBAAyB,EAAE,OAAO,CAAC;;IAElE;IACA,OAAO,MAAMc,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9I,SAAS,CAAC,MAAM;IACd,IAAIsC,WAAW,EAAE;MACfwB,OAAO,CAACmC,GAAG,CAAC,qCAAqC,CAAC;MAClDgD,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3G,WAAW,CAAC,CAAC;EAEjB,MAAM2G,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFnF,OAAO,CAACmC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMK,QAAQ,GAAG,MAAMpF,UAAU,CAACsB,SAAS,CAAC+D,MAAM,CAAC,CAAC;MACpD,MAAM2C,aAAa,GAAG5C,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE;MAC9C3C,OAAO,CAACmC,GAAG,CAAC,oBAAoB,EAAEiD,aAAa,CAAC;MAChDzG,YAAY,CAACyG,aAAa,CAAC;;MAE3B;MACA,IAAI9G,WAAW,IAAI2B,UAAU,CAACgB,YAAY,EAAE;QAC1C,MAAMoE,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxClC,OAAO,IAAIA,OAAO,CAACnC,YAAY,KAAKhB,UAAU,CAACgB,YACjD,CAAC;QAED,IAAIoE,eAAe,IAAIA,eAAe,CAACE,gBAAgB,IAAI,CAAC9D,WAAW,EAAE;UACvEzB,OAAO,CAACmC,GAAG,CAAC,4CAA4C,EAAEkD,eAAe,CAACpE,YAAY,CAAC;UACvFS,cAAc,CAAC2D,eAAe,CAACE,gBAAgB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOxF,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAE6B,GAAG,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMyF,kBAAkB,GAAIC,CAAC,IAAK;IAChCpH,aAAa,CAACoH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAO/C,GAAG,IAAK;IACnC,IAAI;MACF5E,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMuE,QAAQ,GAAG,MAAMpF,UAAU,CAACU,IAAI,CAAC+H,OAAO,CAAChD,GAAG,CAACK,EAAE,CAAC;MACtD,MAAM4C,cAAc,GAAGtD,QAAQ,CAACG,IAAI;MAEpC3C,OAAO,CAACmC,GAAG,CAAC,uBAAuB,EAAE2D,cAAc,CAAC;MACpD9F,OAAO,CAACmC,GAAG,CAAC,qBAAqB,EAAE2D,cAAc,CAAC3F,SAAS,CAAC;MAE5D5B,cAAc,CAACsE,GAAG,CAAC;MACnB;MACA;MACA,MAAMkD,UAAU,GAAIxG,UAAU,IAAK;QACjC,IAAI,CAACA,UAAU,EAAE,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;;QAE7C,IAAI;UACF;UACA,MAAMM,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;UACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;YACzBM,OAAO,CAACgG,IAAI,CAAC,wBAAwB,EAAEzG,UAAU,CAAC;YAClD,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC9B;;UAEA;UACA,MAAMU,IAAI,GAAGJ,IAAI,CAACY,WAAW,CAAC,CAAC;UAC/B,MAAMP,KAAK,GAAGoG,MAAM,CAACzG,IAAI,CAAC0G,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAC1D,MAAMrG,GAAG,GAAGmG,MAAM,CAACzG,IAAI,CAAC4G,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACnD,OAAO,GAAGvG,IAAI,IAAIC,KAAK,IAAIC,GAAG,EAAE;QAClC,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,wBAAwB,EAAE6B,GAAG,CAAC;UAC5C,OAAOb,iBAAiB,CAAC,CAAC;QAC5B;MACF,CAAC;;MAED;MACA,IAAI4G,cAAc,CAAC/E,cAAc,EAAE;QACjCO,kBAAkB,CAACwE,cAAc,CAAC/E,cAAc,CAAC;MACnD;MAEA,IAAI+E,cAAc,CAAC9E,kBAAkB,EAAE;QACrCQ,mBAAmB,CAACsE,cAAc,CAAC9E,kBAAkB,CAAC;MACxD;;MAEA;MACA,IAAIqF,cAAc,GAAG,IAAI;MACzB,IAAIP,cAAc,CAAC5E,YAAY,EAAE;QAC/BQ,cAAc,CAACoE,cAAc,CAAC5E,YAAY,CAAC;QAC3CmF,cAAc,GAAGP,cAAc,CAAC5E,YAAY;MAC9C,CAAC,MAAM,IAAI4E,cAAc,CAACP,gBAAgB,EAAE;QAC1C7D,cAAc,CAACoE,cAAc,CAACP,gBAAgB,CAAC;QAC/Cc,cAAc,GAAGP,cAAc,CAACP,gBAAgB;MAClD,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAMe,iBAAiB,GAAG,MAAMlJ,UAAU,CAACsB,SAAS,CAAC+D,MAAM,CAAC,CAAC;UAC7D,MAAM2C,aAAa,GAAGkB,iBAAiB,CAAC3D,IAAI,CAACA,IAAI,IAAI,EAAE;UAEvD,MAAM0C,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxClC,OAAO,IAAIA,OAAO,CAACnC,YAAY,KAAK6E,cAAc,CAAC7E,YACrD,CAAC;UAED,IAAIoE,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;YACvD7D,cAAc,CAAC2D,eAAe,CAACE,gBAAgB,CAAC;YAChDc,cAAc,GAAGhB,eAAe,CAACE,gBAAgB;UACnD;QACF,CAAC,CAAC,OAAOxF,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;QACnD;MACF;MACA;MACA,IAAIwG,QAAQ,GAAGT,cAAc,CAACnF,gBAAgB;MAC9C,IAAI,CAAChD,kBAAkB,CAAC6I,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC1CA,QAAQ,GAAG,wBAAwB;MACrC;MACArG,aAAa,CAAC;QACZC,SAAS,EAAE2F,cAAc,CAAC3F,SAAS;QACnCc,YAAY,EAAE6E,cAAc,CAAC7E,YAAY;QACzCrB,IAAI,EAAEkG,cAAc,CAAClG,IAAI,IAAI,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACrDC,UAAU,EAAE0F,UAAU,CAACD,cAAc,CAACzF,UAAU,CAAC;QACjDC,QAAQ,EAAEyF,UAAU,CAACD,cAAc,CAACxF,QAAQ,CAAC;QAC7CI,QAAQ,EAAEoF,cAAc,CAACpF,QAAQ;QACjCC,gBAAgB,EAAE4F,QAAQ;QAC1B3F,UAAU,EAAEkF,cAAc,CAAClF,UAAU,IAAI,EAAE;QAC3CC,UAAU,EAAEiF,cAAc,CAACjF,UAAU,IAAI,EAAE;QAC3CN,aAAa,EAAEuF,cAAc,CAACvF,aAAa,IAAI,EAAE;QACjDC,WAAW,EAAEsF,cAAc,CAACtF,WAAW;QACvCC,UAAU,EAAE,CAAC,MAAM;UACjB,IAAI;YACF;YACA,IAAI,CAACqF,cAAc,CAACrF,UAAU,EAAE,OAAO,EAAE;YAEzC,IAAI,OAAOqF,cAAc,CAACrF,UAAU,KAAK,QAAQ,EAAE;cACjD;cACA,IAAIqF,cAAc,CAACrF,UAAU,CAACgG,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACpD,OAAOC,IAAI,CAACC,KAAK,CAACd,cAAc,CAACrF,UAAU,CAAC;cAC9C,CAAC,MAAM;gBACL;gBACA,OAAO,CAACqF,cAAc,CAACrF,UAAU,CAAC;cACpC;YACF,CAAC,MAAM,IAAIoG,KAAK,CAACC,OAAO,CAAChB,cAAc,CAACrF,UAAU,CAAC,EAAE;cACnD,OAAOqF,cAAc,CAACrF,UAAU;YAClC,CAAC,MAAM;cACL,OAAO,EAAE;YACX;UACF,CAAC,CAAC,OAAOV,GAAG,EAAE;YACZC,OAAO,CAACgG,IAAI,CAAC,iDAAiD,EAAEjG,GAAG,CAAC;YACpE,OAAO+F,cAAc,CAACrF,UAAU,GAAG,CAACqF,cAAc,CAACrF,UAAU,CAAC,GAAG,EAAE;UACrE;QACF,CAAC,EAAE,CAAC;QACJK,eAAe,EAAEgF,cAAc,CAAChF,eAAe,IAAI,EAAE;QACrDC,cAAc,EAAE+E,cAAc,CAAC/E,cAAc;QAC7CC,kBAAkB,EAAE8E,cAAc,CAAC9E,kBAAkB;QACrDE,YAAY,EAAE4E,cAAc,CAAC5E,YAAY;QACzCqE,gBAAgB,EAAEc,cAAc;QAAE;QAClC;QACAlF,MAAM,EAAE,QAAQ;QAChBC,GAAG,EAAE0E,cAAc,CAAC1E,GAAG,IAAI,KAAK,CAAC;MACnC,CAAC,CAAC;MAEF3C,cAAc,CAAC,IAAI,CAAC;MACpBN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;MACjD5B,QAAQ,CAAC,kCAAkC0E,GAAG,CAACQ,QAAQ,EAAE,CAAC;IAC5D,CAAC,SAAS;MACRpF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8I,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI;MACF/I,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMb,UAAU,CAACU,IAAI,CAAC+G,MAAM,CAACmC,KAAK,CAAC;MACnCjJ,OAAO,CAACD,IAAI,CAAC2G,MAAM,CAAC5B,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK8D,KAAK,CAAC,CAAC;MAC7C7I,QAAQ,CAAC,IAAI,CAAC;MACdY,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,qBAAqB,EAAE6B,GAAG,CAAC;MACzC5B,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgJ,aAAa,GAAIpE,GAAG,IAAK;IAC7B5D,cAAc,CAAC4D,GAAG,CAAC;IACnB9D,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmI,YAAY,GAAGA,CAAA,KAAM;IACzBnI,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMkI,kBAAkB,GAAG,MAAOH,KAAK,IAAK;IAC1C,IAAI;MACF/I,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmJ,WAAW,GAAGtJ,IAAI,CAACwH,IAAI,CAACzC,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK8D,KAAK,CAAC;MACtD,MAAMK,SAAS,GAAGD,WAAW,CAACjG,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;;MAEzE;MACA;MACA;;MAEA;MACApD,OAAO,CAACD,IAAI,CAAC8E,GAAG,CAACC,GAAG,IAClBA,GAAG,CAACK,EAAE,KAAK8D,KAAK,GACZ;QAAC,GAAGnE,GAAG;QAAE1B,MAAM,EAAEkG;MAAS,CAAC,GAC3BxE,GACN,CAAC,CAAC;MAEF1E,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,4BAA4B,EAAE6B,GAAG,CAAC;MAChD5B,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACC,MAAMqJ,eAAe,GAAGA,CAAA,KAAM;IAC9B7I,cAAc,CAAC,IAAI,CAAC;IACpBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACC,MAAMgJ,UAAU,GAAGA,CAAA,KAAM;IACzB9I,cAAc,CAAC,KAAK,CAAC;IACrBF,cAAc,CAAC,IAAI,CAAC;IACpB;IACA2B,aAAa,CAAC;MACZC,SAAS,EAAE,EAAE;MACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;MAC/BoB,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,iBAAiB;MAAE;MAChCC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,gBAAgB;MAAE;MAC5BC,gBAAgB,EAAE,aAAa;MAC/BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,IAAI;MACpBC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE;IACP,CAAC,CAAC;IACF;IACAE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,mBAAmB,CAAC,IAAI,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;IACpB7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM2I,iBAAiB,GAAIC,IAAI,IAAK;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA;IACA,OAAOA,IAAI,CACRC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAAA,CAC5BjB,IAAI,CAAC,CAAC;EACX,CAAC;EAED,MAAMkB,gBAAgB,GAAIlC,CAAC,IAAK;IAC9B,MAAM;MAAEmC,IAAI;MAAEjC,KAAK;MAAEkC,IAAI;MAAEC;IAAQ,CAAC,GAAGrC,CAAC,CAACC,MAAM;;IAE/C;IACA,IAAIkC,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,iBAAiB,EAAE;MACtD1H,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAAC2H,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGnC,KAAK,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACPzF,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAAC2H,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGnC;MAC1C,CAAC,CAAC;IACF;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAGA,CAACtC,CAAC,EAAEuC,SAAS,KAAK;IAC1C,MAAMC,IAAI,GAAGxC,CAAC,CAACC,MAAM,CAACwC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpB,IAAIL,SAAS,KAAK,gBAAgB,EAAE;UAClC1G,kBAAkB,CAAC6G,MAAM,CAACG,MAAM,CAAC;UACjCpI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEc,cAAc,EAAEkH;UAAK,CAAC,CAAC;QACxD,CAAC,MAAM,IAAID,SAAS,KAAK,oBAAoB,EAAE;UAC7CxG,mBAAmB,CAAC2G,MAAM,CAACG,MAAM,CAAC;UAClCpI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEe,kBAAkB,EAAEiH;UAAK,CAAC,CAAC;QAC5D,CAAC,MAAM,IAAID,SAAS,KAAK,cAAc,EAAE;UACvCtG,cAAc,CAACyG,MAAM,CAACG,MAAM,CAAC;UAC7BpI,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEiB,YAAY,EAAE+G;UAAK,CAAC,CAAC;QACtD;MACF,CAAC;MACDE,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAIpF,OAAO,IAAK;IACvCpD,OAAO,CAACmC,GAAG,CAAC,mBAAmB,EAAEiB,OAAO,CAAC;;IAEzC;IACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACnC,YAAY,EAAE;MACrCjB,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;;IAEA;IACAgC,aAAa,CAAC;MACZ,GAAGD,UAAU;MACbgB,YAAY,EAAEmC,OAAO,CAACnC,YAAY;MAClCC,YAAY,EAAE,IAAI;MAAE;MACpBqE,gBAAgB,EAAEnC,OAAO,CAACmC,gBAAgB,CAAC;IAC7C,CAAC,CAAC;;IAEF;IACA,IAAInC,OAAO,CAACmC,gBAAgB,EAAE;MAC5B7D,cAAc,CAAC0B,OAAO,CAACmC,gBAAgB,CAAC;IAC1C,CAAC,MAAM;MACL7D,cAAc,CAAC,IAAI,CAAC;IACtB;;IAEA;IACA7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM4J,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/G,cAAc,CAAC,IAAI,CAAC;IACpB;IACA,IAAIzB,UAAU,CAACiB,YAAY,EAAE;MAC3BhB,aAAa,CAAC;QACZ,GAAGD,UAAU;QACbiB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMwH,gBAAgB,GAAG,MAAOjD,CAAC,IAAK;IACpCA,CAAC,CAACkD,cAAc,CAAC,CAAC;;IAElB;IACA3I,OAAO,CAACmC,GAAG,CAAC,sCAAsC,CAAC;IACnDnC,OAAO,CAACmC,GAAG,CAAC,sBAAsB,EAAElC,UAAU,CAAC;IAC/CD,OAAO,CAACmC,GAAG,CAAC,eAAe,EAAE7D,WAAW,CAAC;;IAEzC;IACA,IAAI,CAAC2B,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACE,SAAS,CAACsG,IAAI,CAAC,CAAC,EAAE;MACzDtI,QAAQ,CAAC,uBAAuB,CAAC;MACjC;IACF;IAEA,IAAI,CAAC8B,UAAU,CAACgB,YAAY,IAAI,CAAChB,UAAU,CAACgB,YAAY,CAACwF,IAAI,CAAC,CAAC,EAAE;MAC/DtI,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;;IAEA;IACA,IAAI8B,UAAU,CAACM,aAAa,IAAIN,UAAU,CAACM,aAAa,CAACkG,IAAI,CAAC,CAAC,EAAE;MAC/D,MAAMmC,UAAU,GAAG,4BAA4B;MAC/C,MAAMC,QAAQ,GAAG,+DAA+D;MAEhF,MAAMlD,KAAK,GAAG1F,UAAU,CAACM,aAAa,CAACkG,IAAI,CAAC,CAAC;MAC7C,IAAI,CAACmC,UAAU,CAACE,IAAI,CAACnD,KAAK,CAAC,IAAI,CAACkD,QAAQ,CAACC,IAAI,CAACnD,KAAK,CAAC,EAAE;QACpDxH,QAAQ,CAAC,mDAAmD,CAAC;QAC7D;MACF;IACF;;IAEA;IACA,IAAI4K,qBAAqB,GAAG9I,UAAU,CAACU,gBAAgB;IACvD,IAAI,CAACoI,qBAAqB,EAAE;MAC1BA,qBAAqB,GAAG,wBAAwB;IAClD;IAEA,IAAI;MACF9K,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA+B,OAAO,CAACmC,GAAG,CAAC,gCAAgC,EAAElC,UAAU,CAACE,SAAS,CAAC;MACnE,MAAM6I,cAAc,GAAGxB,iBAAiB,CAACvH,UAAU,CAACE,SAAS,CAAC;MAC9DH,OAAO,CAACmC,GAAG,CAAC,+BAA+B,EAAE6G,cAAc,CAAC;MAE5D,MAAMC,aAAa,GAAG;QACpB,GAAGhJ,UAAU;QACbE,SAAS,EAAE6I,cAAc;QACzBlI,eAAe,EAAE0G,iBAAiB,CAACvH,UAAU,CAACa,eAAe,CAAC;QAC9DH,gBAAgB,EAAEoI,qBAAqB;QACvC;QACA1I,UAAU,EAAE/B,WAAW,GAAG2B,UAAU,CAACI,UAAU,GAAIJ,UAAU,CAACI,UAAU,IAAInB,iBAAiB,CAAC;MAChG,CAAC;MAEDc,OAAO,CAACmC,GAAG,CAAC,sBAAsB,EAAE8G,aAAa,CAAC;;MAElD;MACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC7B;MACF,MAAMC,QAAQ,GAAG,CACf,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAC7D,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAC5E,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,KAAK,CACrD;;MAED;MACAA,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAI;QACtB,IAAIL,aAAa,CAACK,GAAG,CAAC,KAAKC,SAAS,IAAIN,aAAa,CAACK,GAAG,CAAC,KAAK,IAAI,EAAE;UACnE;UACA,IAAIA,GAAG,KAAK,YAAY,EAAE;YACxB,IAAI;cACF,IAAIzC,KAAK,CAACC,OAAO,CAACmC,aAAa,CAACK,GAAG,CAAC,CAAC,EAAE;gBACrC;gBACA,MAAME,cAAc,GAAGP,aAAa,CAACK,GAAG,CAAC,CAAC1G,GAAG,CAAC6G,KAAK,IAAIxD,MAAM,CAACwD,KAAK,CAAC,CAAChD,IAAI,CAAC,CAAC,CAAC;gBAC5EyC,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE3C,IAAI,CAACgD,SAAS,CAACH,cAAc,CAAC,CAAC;cACtD,CAAC,MAAM;gBACL;gBACAN,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE3C,IAAI,CAACgD,SAAS,CAAC,CAAC1D,MAAM,CAACgD,aAAa,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;cACpE;YACF,CAAC,CAAC,OAAOvJ,GAAG,EAAE;cACZC,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,EAAE6B,GAAG,CAAC;cAClDmJ,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9B;UACF,CAAC,MAAM;YACLtJ,OAAO,CAACmC,GAAG,CAAC,sBAAsBmH,GAAG,MAAML,aAAa,CAACK,GAAG,CAAC,EAAE,CAAC;YAChEJ,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAEL,aAAa,CAACK,GAAG,CAAC,CAAC;UAC1C;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIL,aAAa,CAAClI,cAAc,IAAIkI,aAAa,CAAClI,cAAc,YAAY6I,IAAI,EAAE;QAChFV,QAAQ,CAACQ,MAAM,CAAC,gBAAgB,EAAET,aAAa,CAAClI,cAAc,CAAC;MACjE;MAEA,IAAIkI,aAAa,CAACjI,kBAAkB,IAAIiI,aAAa,CAACjI,kBAAkB,YAAY4I,IAAI,EAAE;QACxFV,QAAQ,CAACQ,MAAM,CAAC,oBAAoB,EAAET,aAAa,CAACjI,kBAAkB,CAAC;MACzE;MAEA,IAAIiI,aAAa,CAAC/H,YAAY,IAAI+H,aAAa,CAAC/H,YAAY,YAAY0I,IAAI,EAAE;QAC5EV,QAAQ,CAACQ,MAAM,CAAC,cAAc,EAAET,aAAa,CAAC/H,YAAY,CAAC;MAC7D;;MAEA;MACA,IAAIO,WAAW,IAAI,CAACwH,aAAa,CAAC/H,YAAY,EAAE;QAC9CgI,QAAQ,CAACQ,MAAM,CAAC,2BAA2B,EAAEjI,WAAW,CAAC;MAC3D;;MAEA;MACA,IAAIwH,aAAa,CAAC1D,gBAAgB,EAAE;QAClC2D,QAAQ,CAACQ,MAAM,CAAC,kBAAkB,EAAET,aAAa,CAAC1D,gBAAgB,CAAC;MACrE;;MAEA;MACAvF,OAAO,CAACmC,GAAG,CAAC,oBAAoB,CAAC;MACjC,KAAK,IAAI0H,IAAI,IAAIX,QAAQ,CAACY,OAAO,CAAC,CAAC,EAAE;QACnC9J,OAAO,CAACmC,GAAG,CAAC0H,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC;MACA7J,OAAO,CAACmC,GAAG,CAAC,4BAA4B,EAAE+G,QAAQ,CAACa,GAAG,CAAC,WAAW,CAAC,CAAC;;MAEpE;MACA,IAAIzL,WAAW,EAAE;QACf;QACA,IAAI;UACF,MAAMlB,UAAU,CAACU,IAAI,CAACkM,MAAM,CAAC1L,WAAW,CAAC4E,EAAE,EAAEgG,QAAQ,CAAC;UACtD;QACF,CAAC,CAAC,OAAOe,QAAQ,EAAE;UACjB;UACA,MAAMA,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAM9M,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGwH,YAAY,CAACvH,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACd/B,GAAG,EAAEyB,GAAG,CAACzB,GAAG,IAAI,KAAK;UAAE;UACvBgC,OAAO,EAAEP,GAAG,CAAC5B,YAAY;UACzBoC,QAAQ,EAAER,GAAG,CAAC1C,SAAS;UACvBmD,QAAQ,EAAET,GAAG,CAACrC,WAAW;UACzB+C,QAAQ,EAAEV,GAAG,CAACnC,QAAQ;UACtBC,gBAAgB,EAAEkC,GAAG,CAAClC,gBAAgB;UACtC6C,QAAQ,EAAEX,GAAG,CAACtC,aAAa;UAC3BkD,MAAM,EAAGZ,GAAG,CAACjC,UAAU,IAAIiC,GAAG,CAAChC,UAAU,GACrC,GAAGgC,GAAG,CAACjC,UAAU,MAAMiC,GAAG,CAAChC,UAAU,EAAE,GACvCgC,GAAG,CAACjC,UAAU,GACZ,GAAGiC,GAAG,CAACjC,UAAU,EAAE,GACnBiC,GAAG,CAAChC,UAAU,GACZ,GAAGgC,GAAG,CAAChC,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClBuC,UAAU,EAAEb,GAAG,CAACxC,UAAU,GAAG,IAAIlB,IAAI,CAAC0D,GAAG,CAACxC,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvEwD,GAAG,CAACG,WAAW,GAAG,IAAI7D,IAAI,CAAC0D,GAAG,CAACG,WAAW,CAAC,CAAC5D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvFuE,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BvD,QAAQ,EAAEuC,GAAG,CAACvC,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMwD,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI9E,IAAI,CAAC8E,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIvE,IAAI,CAAC6E,CAAC,CAACN,UAAU,CAAC,CAAC;QACrG3F,OAAO,CAAC+F,UAAU,CAAC;MACrB,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAM1G,UAAU,CAACU,IAAI,CAACqM,MAAM,CAACjB,QAAQ,CAAC;UACtC;QACF,CAAC,CAAC,OAAOe,QAAQ,EAAE;UAAA,IAAAG,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;UACjB;UACAtK,OAAO,CAAC9B,KAAK,CAAC,oBAAoB,EAAE;YAClCqM,OAAO,EAAEN,QAAQ,CAACM,OAAO;YACzBpJ,MAAM,GAAAiJ,kBAAA,GAAEH,QAAQ,CAACzH,QAAQ,cAAA4H,kBAAA,uBAAjBA,kBAAA,CAAmBjJ,MAAM;YACjCqJ,UAAU,GAAAH,mBAAA,GAAEJ,QAAQ,CAACzH,QAAQ,cAAA6H,mBAAA,uBAAjBA,mBAAA,CAAmBG,UAAU;YACzC7H,IAAI,GAAA2H,mBAAA,GAAEL,QAAQ,CAACzH,QAAQ,cAAA8H,mBAAA,uBAAjBA,mBAAA,CAAmB3H;UAC3B,CAAC,CAAC;UACF,MAAMsH,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAM9M,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGwH,YAAY,CAACvH,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACd/B,GAAG,EAAEyB,GAAG,CAACzB,GAAG,IAAI,KAAK;UAAE;UACvBgC,OAAO,EAAEP,GAAG,CAAC5B,YAAY;UACzBoC,QAAQ,EAAER,GAAG,CAAC1C,SAAS;UACvBmD,QAAQ,EAAET,GAAG,CAACrC,WAAW;UACzB+C,QAAQ,EAAEV,GAAG,CAACnC,QAAQ;UACtBC,gBAAgB,EAAEkC,GAAG,CAAClC,gBAAgB;UACtC6C,QAAQ,EAAEX,GAAG,CAACtC,aAAa;UAC3BkD,MAAM,EAAGZ,GAAG,CAACjC,UAAU,IAAIiC,GAAG,CAAChC,UAAU,GACrC,GAAGgC,GAAG,CAACjC,UAAU,MAAMiC,GAAG,CAAChC,UAAU,EAAE,GACvCgC,GAAG,CAACjC,UAAU,GACZ,GAAGiC,GAAG,CAACjC,UAAU,EAAE,GACnBiC,GAAG,CAAChC,UAAU,GACZ,GAAGgC,GAAG,CAAChC,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClBuC,UAAU,EAAEb,GAAG,CAACxC,UAAU,GAAG,IAAIlB,IAAI,CAAC0D,GAAG,CAACxC,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvEwD,GAAG,CAACG,WAAW,GAAG,IAAI7D,IAAI,CAAC0D,GAAG,CAACG,WAAW,CAAC,CAAC5D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvFuE,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5BvD,QAAQ,EAAEuC,GAAG,CAACvC,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMwD,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI9E,IAAI,CAAC8E,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIvE,IAAI,CAAC6E,CAAC,CAACN,UAAU,CAAC,CAAC;QACrG3F,OAAO,CAAC+F,UAAU,CAAC;MACrB;MAEA3F,QAAQ,CAAC,IAAI,CAAC;MACd;MACAoJ,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOxH,GAAG,EAAE;MAAA,IAAA0K,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ5K,OAAO,CAAC9B,KAAK,CAAC,mBAAmB,EAAE6B,GAAG,CAAC;MACvC;MACA,MAAM8K,YAAY,GAAG,EAAAJ,aAAA,GAAA1K,GAAG,CAACyC,QAAQ,cAAAiI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAc9H,IAAI,cAAA+H,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,OAAAI,cAAA,GAC5B5K,GAAG,CAACyC,QAAQ,cAAAmI,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchI,IAAI,cAAAiI,mBAAA,uBAAlBA,mBAAA,CAAoB1M,KAAK,MACxBI,WAAW,GAAG,sBAAsB,GAAG,sBAAsB,CAAC;MACnFH,QAAQ,CAAC0M,YAAY,CAAC;IACxB,CAAC,SAAS;MACR5M,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6M,YAAY,GAAGhN,IAAI,CAAC2G,MAAM,CAAC5B,GAAG,IAAI;IACtC,MAAMkI,aAAa,GAAIlI,GAAG,CAACO,OAAO,IAAIP,GAAG,CAACO,OAAO,CAAC4H,WAAW,CAAC,CAAC,CAACxE,QAAQ,CAACpI,UAAU,CAAC4M,WAAW,CAAC,CAAC,CAAC,IAC3EnI,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAAC2H,WAAW,CAAC,CAAC,CAACxE,QAAQ,CAACpI,UAAU,CAAC4M,WAAW,CAAC,CAAC,CAAE;IAErG,OAAOD,aAAa;EACtB,CAAC,CAAC;;EAEF;EACA7O,SAAS,CAAC,MAAM;IACd,MAAM+O,kBAAkB,GAAI/I,KAAK,IAAK;MACpC,MAAMgJ,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;MAC1E,IAAIF,eAAe,IAAI,CAACA,eAAe,CAACG,QAAQ,CAACnJ,KAAK,CAACwD,MAAM,CAAC,EAAE;QAC9D7G,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAED,IAAID,mBAAmB,EAAE;MACvBuM,QAAQ,CAAC9I,gBAAgB,CAAC,WAAW,EAAE4I,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXE,QAAQ,CAAC7I,mBAAmB,CAAC,WAAW,EAAE2I,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACrM,mBAAmB,CAAC,CAAC;EAEzB,oBACEpB,OAAA;IAAK8N,SAAS,EAAC,sBAAsB;IAAAC,QAAA,GAElCzM,iBAAiB,iBAChBtB,OAAA;MAAK8N,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC/N,OAAA;QAAK8N,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpC/N,OAAA;UAAK8N,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC/N,OAAA,CAACpB,eAAe;YAACoP,IAAI,EAAEtO,qBAAsB;YAACoO,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxEpO,OAAA;YAAA+N,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNpO,OAAA;UAAK8N,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/N,OAAA;YAAA+N,QAAA,EAAG;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDpO,OAAA;YAAA+N,QAAA,eAAG/N,OAAA;cAAA+N,QAAA,EAASvM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqE;YAAQ;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/CpO,OAAA;YAAA+N,QAAA,EAAG;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNpO,OAAA;UAAK8N,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC/N,OAAA;YACE8N,SAAS,EAAC,mBAAmB;YAC7BO,OAAO,EAAE3E,YAAa;YAAAqE,QAAA,EACvB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpO,OAAA;YACE8N,SAAS,EAAC,oBAAoB;YAC9BO,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAAC/H,WAAW,CAACkE,EAAE,CAAE;YAAAqI,QAAA,EAChD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpO,OAAA;MAAK8N,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/N,OAAA;QAAI8N,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CpO,OAAA;QAAK8N,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC/N,OAAA;UAAQ8N,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAEA,CAAA,KAAM;YAChD5N,UAAU,CAAC,IAAI,CAAC;YAChB,MAAMsE,SAAS,GAAG,MAAAA,CAAA,KAAY;cAC5B,IAAI;gBACF,MAAMC,QAAQ,GAAG,MAAMpF,UAAU,CAACU,IAAI,CAAC2E,MAAM,CAAC,CAAC;;gBAE/C;gBACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;kBAC7C;kBACA,IAAIC,SAAS;kBACb,IAAID,GAAG,CAACE,UAAU,EAAE;oBAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;kBAC5B,CAAC,MAAM,IAAIF,GAAG,CAACxC,UAAU,EAAE;oBACzByC,SAAS,GAAGD,GAAG,CAACxC,UAAU;kBAC5B,CAAC,MAAM,IAAIwC,GAAG,CAACG,WAAW,EAAE;oBAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;kBAC7B,CAAC,MAAM;oBACLF,SAAS,GAAG,IAAI3D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACtC;;kBAEA;kBACA,IAAI6D,aAAa,GAAG,EAAE;kBACtB,IAAIJ,GAAG,CAACjC,UAAU,IAAIiC,GAAG,CAAChC,UAAU,EAAE;oBACpCoC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,MAAMiC,GAAG,CAAChC,UAAU,EAAE;kBACzD,CAAC,MAAM,IAAIgC,GAAG,CAACjC,UAAU,EAAE;oBACzBqC,aAAa,GAAG,GAAGJ,GAAG,CAACjC,UAAU,EAAE;kBACrC,CAAC,MAAM,IAAIiC,GAAG,CAAChC,UAAU,EAAE;oBACzBoC,aAAa,GAAG,GAAGJ,GAAG,CAAChC,UAAU,EAAE;kBACrC,CAAC,MAAM;oBACLoC,aAAa,GAAG,EAAE;kBACpB;kBACE,OAAO;oBACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;oBACd/B,GAAG,EAAEyB,GAAG,CAACzB,GAAG,IAAI,KAAK;oBACrBgC,OAAO,EAAEP,GAAG,CAAC5B,YAAY;oBACzBoC,QAAQ,EAAER,GAAG,CAAC1C,SAAS;oBACvBmD,QAAQ,EAAET,GAAG,CAACrC,WAAW;oBACzB+C,QAAQ,EAAEV,GAAG,CAACnC,QAAQ;oBACtBC,gBAAgB,EAAEkC,GAAG,CAAClC,gBAAgB;oBACtC6C,QAAQ,EAAEX,GAAG,CAACtC,aAAa;oBAC3BkD,MAAM,EAAER,aAAa;oBACrB9B,MAAM,EAAE,QAAQ;oBAChBuC,UAAU,EAAEZ,SAAS;oBACrBa,aAAa,EAAEd,GAAG,CAACxC,UAAU,IAAIwC,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;oBAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;oBAC1BvD,QAAQ,EAAEuC,GAAG,CAACvC;kBAChB,CAAC;gBACH,CAAC,CAAC;;gBAEF;gBACA,MAAMwD,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI9E,IAAI,CAAC8E,CAAC,CAACP,UAAU,CAAC,GAAG,IAAIvE,IAAI,CAAC6E,CAAC,CAACN,UAAU,CAAC,CAAC;gBACrG3F,OAAO,CAAC+F,UAAU,CAAC;gBACnB3F,QAAQ,CAAC,IAAI,CAAC;cAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;gBACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;gBAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;cAC7C,CAAC,SAAS;gBACRF,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC;YAEDsE,SAAS,CAAC,CAAC;UACb,CAAE;UAACuJ,QAAQ,EAAE9N,OAAQ;UAAAuN,QAAA,gBACnB/N,OAAA,CAACpB,eAAe;YAACoP,IAAI,EAAErO;UAAO;YAAAsO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCpO,OAAA;YAAA+N,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACTpO,OAAA;UAAQ8N,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAEvE,eAAgB;UAACwE,QAAQ,EAAE9N,OAAQ;UAAAuN,QAAA,gBAC7E/N,OAAA,CAACpB,eAAe;YAACoP,IAAI,EAAEnP;UAAO;YAAAoP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCpO,OAAA;YAAA+N,QAAA,EAAM;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL1N,KAAK,iBACJV,OAAA;MAAK8N,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/N,OAAA,CAACpB,eAAe;QAACoP,IAAI,EAAE/O;MAAoB;QAAAgP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CpO,OAAA;QAAA+N,QAAA,EAAOrN;MAAK;QAAAuN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAEDpO,OAAA;MAAK8N,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC/N,OAAA;QAAK8N,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B/N,OAAA;UAAK8N,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC/N,OAAA,CAACpB,eAAe;YAACoP,IAAI,EAAEhP,QAAS;YAAC8O,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAAU,eAAApO,OAAA;YACrEqK,IAAI,EAAC,MAAM;YACXkE,WAAW,EAAC,kCAAkC;YAC9CpG,KAAK,EAAEvH,UAAW;YAClB4N,QAAQ,EAAExG,kBAAmB;YAC7B8F,SAAS,EAAC,cAAc;YACxBW,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAENpO,OAAA;MAAK8N,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BvN,OAAO,gBACNR,OAAA;QAAK8N,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC/N,OAAA,CAACpB,eAAe;UAACoP,IAAI,EAAExO,SAAU;UAACmP,IAAI;UAACC,IAAI,EAAC;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDpO,OAAA;UAAA+N,QAAA,EAAM;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAENpO,OAAA,CAAAE,SAAA;QAAA6N,QAAA,eACA/N,OAAA;UAAO8N,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC3B/N,OAAA;YAAA+N,QAAA,eACE/N,OAAA;cAAA+N,QAAA,gBACE/N,OAAA;gBAAA+N,QAAA,EAAI;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZpO,OAAA;gBAAA+N,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbpO,OAAA;gBAAA+N,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBpO,OAAA;gBAAA+N,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBpO,OAAA;gBAAA+N,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfpO,OAAA;gBAAA+N,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBpO,OAAA;gBAAA+N,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBpO,OAAA;gBAAA+N,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpO,OAAA;gBAAA+N,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpO,OAAA;YAAA+N,QAAA,EACGT,YAAY,CAAC/I,MAAM,GAAG,CAAC,GACtB+I,YAAY,CAAClI,GAAG,CAACC,GAAG,iBAClBrF,OAAA;cAAA+N,QAAA,gBACE/N,OAAA;gBAAA+N,QAAA,GACG1I,GAAG,CAACzB,GAAG,iBACN5D,OAAA;kBAAM8N,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACzB/N,OAAA,CAACpB,eAAe;oBAACoP,IAAI,EAAE5O,MAAO;oBAACqP,KAAK,EAAE;sBAAEI,WAAW,EAAE;oBAAM;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAElE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP,eACvBpO,OAAA;kBAAK8N,SAAS,EAAC,cAAc;kBAACgB,KAAK,EAAEzJ,GAAG,CAACQ,QAAS;kBAAAkI,QAAA,EAC/C1I,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAACtB,MAAM,GAAG,EAAE,GACrCc,GAAG,CAACQ,QAAQ,CAACkJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GACjC1J,GAAG,CAACQ;gBAAQ;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACgBpO,OAAA;kBAAK8N,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE1I,GAAG,CAACO;gBAAO;kBAAAqI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACLpO,OAAA;gBAAA+N,QAAA,EAAK1I,GAAG,CAACU;cAAQ;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBpO,OAAA;gBAAA+N,QAAA,eACE/N,OAAA;kBAAK8N,SAAS,EAAC,kBAAkB;kBAACgB,KAAK,EAAEzJ,GAAG,CAAClC,gBAAiB;kBAAA4K,QAAA,EAC3D1I,GAAG,CAAClC,gBAAgB,IAAI;gBAAe;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLpO,OAAA;gBAAA+N,QAAA,eACE/N,OAAA;kBAAK8N,SAAS,EAAC,UAAU;kBAACgB,KAAK,EAAEzJ,GAAG,CAACW,QAAS;kBAAA+H,QAAA,EAC3C1I,GAAG,CAACW,QAAQ,gBACXhG,OAAA;oBAAGgP,IAAI,EAAE,UAAU3J,GAAG,CAACW,QAAQ,EAAG;oBAAC8H,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtD1I,GAAG,CAACW,QAAQ,CAACzB,MAAM,GAAG,EAAE,GAAGc,GAAG,CAACW,QAAQ,CAAC+I,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG1J,GAAG,CAACW;kBAAQ;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,GAEJ;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLpO,OAAA;gBAAA+N,QAAA,EAAK1I,GAAG,CAACY;cAAM;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBpO,OAAA;gBAAA+N,QAAA,EAAKjM,iBAAiB,CAACuD,GAAG,CAACvC,QAAQ;cAAC;gBAAAmL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxCpO,OAAA;gBAAA+N,QAAA,EAAKjM,iBAAiB,CAACuD,GAAG,CAACa,UAAU;cAAC;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9CpO,OAAA;gBAAA+N,QAAA,eACE/N,OAAA;kBAAK8N,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9B/N,OAAA,CAACpB,eAAe;oBAACoP,IAAI,EAAE7O,KAAM;oBAAC2O,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtD/I,GAAG,CAACe,KAAK;gBAAA;kBAAA6H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLpO,OAAA;gBAAA+N,QAAA,eACE/N,OAAA;kBAAKyO,KAAK,EAAE;oBAAEQ,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,gBAC9B/N,OAAA;oBACE8N,SAAS,EAAC,2BAA2B;oBACrCO,OAAO,EAAEA,CAAA,KAAMjG,aAAa,CAAC/C,GAAG,CAAE;oBAClCyJ,KAAK,EAAC,UAAU;oBAChBR,QAAQ,EAAE9N,OAAQ;oBAAAuN,QAAA,eAElB/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAElP;oBAAO;sBAAAmP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eAETpO,OAAA;oBACE8N,SAAS,EAAC,6BAA6B;oBACvCO,OAAO,EAAEA,CAAA,KAAM5E,aAAa,CAACpE,GAAG,CAAE;oBAClCyJ,KAAK,EAAC,YAAY;oBAClBR,QAAQ,EAAE9N,OAAQ;oBAAAuN,QAAA,eAElB/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAEjP;oBAAQ;sBAAAkP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7DE/I,GAAG,CAACK,EAAE;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8DX,CACL,CAAC,gBAEFpO,OAAA;cAAA+N,QAAA,eACE/N,OAAA;gBAAIkP,OAAO,EAAC,GAAG;gBAACpB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EACxCnN,UAAU,GACT,oCAAoC,GACpC;cAAmB;gBAAAqN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACN;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLpN,WAAW,iBACVhB,OAAA;MAAK8N,SAAS,EAAC,eAAe;MAACO,OAAO,EAAGpG,CAAC,IAAK;QAC7C;QACA,IAAIA,CAAC,CAACC,MAAM,CAAC4F,SAAS,KAAK,eAAe,EAAE;UAC1C/D,UAAU,CAAC,CAAC;QACd;MACF,CAAE;MAAAgE,QAAA,eACA/N,OAAA;QAAK8N,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB/N,OAAA;UAAK8N,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/N,OAAA;YAAA+N,QAAA,EAAKjN,WAAW,GAAG,UAAU,GAAG;UAAa;YAAAmN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDpO,OAAA;YAAQ8N,SAAS,EAAC,cAAc;YAACO,OAAO,EAAEtE,UAAW;YAAAgE,QAAA,eACnD/N,OAAA,CAACpB,eAAe;cAACoP,IAAI,EAAE3O;YAAQ;cAAA4O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNpO,OAAA;UAAK8N,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5B/N,OAAA;YAAMmP,QAAQ,EAAEjE,gBAAiB;YAAA6C,QAAA,gBAC/B/N,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,WAAW;kBAAArB,QAAA,GAAC,YAAU,eAAA/N,OAAA;oBAAMyO,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpFpO,OAAA;kBACEqK,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,WAAW;kBACd0E,IAAI,EAAC,WAAW;kBAChBjC,KAAK,EAAE1F,UAAU,CAACE,SAAU;kBAC5B6L,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBACRf,WAAW,EAAC;gBAAiB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACRpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAA+N,QAAA,GAAO,UAAQ,eAAA/N,OAAA;oBAAMyO,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9DpO,OAAA;kBAAK8N,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B/N,OAAA;oBAAK8N,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtC/N,OAAA;sBACEqK,IAAI,EAAC,QAAQ;sBACbyD,SAAS,EAAC,uBAAuB;sBACjCO,OAAO,EAAEA,CAAA,KAAMhN,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;sBAAA2M,QAAA,GAE3DtL,UAAU,CAACgB,YAAY,GAAGhB,UAAU,CAACgB,YAAY,GAAG,kBAAkB,eACvEzD,OAAA,CAACpB,eAAe;wBAACoP,IAAI,EAAEvO;sBAAc;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,EAERhN,mBAAmB,iBAClBpB,OAAA;sBAAK8N,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnC7M,SAAS,IAAIA,SAAS,CAACqD,MAAM,GAAG,CAAC,GAChCrD,SAAS,CAACkE,GAAG,CAACQ,OAAO,iBACnB5F,OAAA;wBAEE8N,SAAS,EAAC,cAAc;wBACxBO,OAAO,EAAEA,CAAA,KAAM;0BACbrD,mBAAmB,CAACpF,OAAO,CAAC;wBAC9B,CAAE;wBAAAmI,QAAA,gBAEF/N,OAAA;0BAAK8N,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAC/BnI,OAAO,CAACmC,gBAAgB,gBACvB/H,OAAA;4BAAKuP,GAAG,EAAE3J,OAAO,CAACmC,gBAAiB;4BAACyH,GAAG,EAAE5J,OAAO,CAACnC;0BAAa;4BAAAwK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAEjEpO,OAAA,CAACpB,eAAe;4BAACoP,IAAI,EAAE1O;0BAAW;4BAAA2O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACrC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNpO,OAAA;0BAAA+N,QAAA,EAAOnI,OAAO,CAACnC;wBAAY;0BAAAwK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAb9BxI,OAAO,CAAC6J,UAAU;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAcpB,CACN,CAAC,gBAEFpO,OAAA;wBAAK8N,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAC1D,eACDpO,OAAA;wBAAK8N,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,gBACnC/N,OAAA;0BAAA+N,QAAA,EAAO;wBAA+B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC9CpO,OAAA;0BACEqK,IAAI,EAAC,MAAM;0BACXkE,WAAW,EAAC,oBAAoB;0BAChCpG,KAAK,EAAE1F,UAAU,CAACgB,YAAY,IAAI,EAAG;0BACrC+K,QAAQ,EAAGvG,CAAC,IAAK;4BACfvF,aAAa,CAAC;8BACZ,GAAGD,UAAU;8BACbgB,YAAY,EAAEwE,CAAC,CAACC,MAAM,CAACC;4BACzB,CAAC,CAAC;0BACJ;wBAAE;0BAAA8F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACFpO,OAAA;0BACEqK,IAAI,EAAC,QAAQ;0BACbyD,SAAS,EAAC,mBAAmB;0BAC7BO,OAAO,EAAEA,CAAA,KAAMhN,sBAAsB,CAAC,KAAK,CAAE;0BAAA0M,QAAA,EAC9C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAGLnK,WAAW,iBACVjE,OAAA;oBAAK8N,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrC/N,OAAA;sBAAKuP,GAAG,EAAEtL,WAAY;sBAACuL,GAAG,EAAC;oBAAc;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5CpO,OAAA;sBACEqK,IAAI,EAAC,QAAQ;sBACbyD,SAAS,EAAC,oBAAoB;sBAC9BO,OAAO,EAAEpD,gBAAiB;sBAAA8C,QAAA,eAE1B/N,OAAA,CAACpB,eAAe;wBAACoP,IAAI,EAAE3O;sBAAQ;wBAAA4O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGA,CAACnK,WAAW,iBACXjE,OAAA;oBAAA+N,QAAA,gBACE/N,OAAA;sBACEqK,IAAI,EAAC,MAAM;sBACX3E,EAAE,EAAC,cAAc;sBACjB0E,IAAI,EAAC,cAAc;sBACnBoE,QAAQ,EAAGvG,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,cAAc,CAAE;sBACtDyH,MAAM,EAAC,SAAS;sBAChBjB,KAAK,EAAE;wBAAEQ,OAAO,EAAE;sBAAO;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACFpO,OAAA;sBACEqK,IAAI,EAAC,QAAQ;sBACbyD,SAAS,EAAC,mBAAmB;sBAC7BO,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAACgC,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAAC,CAAE;sBAAA7B,QAAA,gBAE/D/N,OAAA,CAACpB,eAAe;wBAACoP,IAAI,EAAEzO;sBAAQ;wBAAA0O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,uBAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,MAAM;kBAAArB,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClCpO,OAAA;kBACEqK,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,MAAM;kBACT0E,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAE1F,UAAU,CAACL,IAAK;kBACvBoM,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBACRO,GAAG,EAAC,MAAM;kBACVC,GAAG,EAAC;gBAAM;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,sBAAkB,eAAApO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACjD/N,OAAA;kBAAOoP,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CpO,OAAA;kBACE0F,EAAE,EAAC,UAAU;kBACb0E,IAAI,EAAC,UAAU;kBACfjC,KAAK,EAAE1F,UAAU,CAACS,QAAS;kBAC3BsL,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBAAAvB,QAAA,gBAER/N,OAAA;oBAAQmI,KAAK,EAAC,gBAAgB;oBAAA4F,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtDpO,OAAA;oBAAQmI,KAAK,EAAC,gBAAgB;oBAAA4F,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtDpO,OAAA;oBAAQmI,KAAK,EAAC,aAAa;oBAAA4F,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChDpO,OAAA;oBAAQmI,KAAK,EAAC,WAAW;oBAAA4F,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CpO,OAAA;oBAAQmI,KAAK,EAAC,WAAW;oBAAA4F,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,kBAAkB;kBAAArB,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1DpO,OAAA;kBACE0F,EAAE,EAAC,kBAAkB;kBACrB0E,IAAI,EAAC,kBAAkB;kBACvBjC,KAAK,EAAE1F,UAAU,CAACU,gBAAiB;kBACnCqL,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBAAAvB,QAAA,EAEP5N,kBAAkB,CAACiF,GAAG,CAAC2K,MAAM,iBAC5B/P,OAAA;oBAAqBmI,KAAK,EAAE4H,MAAO;oBAAAhC,QAAA,EAAEgC;kBAAM,GAA9BA,MAAM;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CpO,OAAA;kBACEqK,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAE1F,UAAU,CAACI,UAAW;kBAC7B2L,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CpO,OAAA;kBACEqK,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,UAAU;kBACb0E,IAAI,EAAC,UAAU;kBACfjC,KAAK,EAAE1F,UAAU,CAACK,QAAS;kBAC3B0L,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,eAAe;kBAAArB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDpO,OAAA;kBACEqK,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,eAAe;kBAClB0E,IAAI,EAAC,eAAe;kBACpBjC,KAAK,EAAE1F,UAAU,CAACM,aAAc;kBAChCyL,QAAQ,EAAErE,gBAAiB;kBAC3BoE,WAAW,EAAC;gBAA0D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDpO,OAAA;kBACEqK,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAE1F,UAAU,CAACW,UAAW;kBAC7BoL,QAAQ,EAAErE,gBAAiB;kBAC3BoE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDpO,OAAA;kBACEqK,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAE1F,UAAU,CAACY,UAAW;kBAC7BmL,QAAQ,EAAErE,gBAAiB;kBAC3BoE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/N,OAAA;gBAAK8N,SAAS,EAAC,2BAA2B;gBAACW,KAAK,EAAE;kBAACwB,SAAS,EAAE;gBAAM,CAAE;gBAAAlC,QAAA,eACpE/N,OAAA;kBAAO8N,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACnC/N,OAAA;oBACEqK,IAAI,EAAC,UAAU;oBACfD,IAAI,EAAC,KAAK;oBACVE,OAAO,EAAE7H,UAAU,CAACmB,GAAI;oBACxB4K,QAAQ,EAAErE;kBAAiB;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACFpO,OAAA;oBAAM8N,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC7B/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAE5O,MAAO;sBAAC0O,SAAS,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,sBAExD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAOoP,OAAO,EAAC,aAAa;kBAAArB,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDpO,OAAA;kBACE0F,EAAE,EAAC,aAAa;kBAChB0E,IAAI,EAAC,aAAa;kBAClBjC,KAAK,EAAE1F,UAAU,CAACO,WAAY;kBAC9BwL,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBAAAvB,QAAA,gBAER/N,OAAA;oBAAQmI,KAAK,EAAC,iBAAiB;oBAAA4F,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxDpO,OAAA;oBAAQmI,KAAK,EAAC,cAAc;oBAAA4F,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDpO,OAAA;oBAAQmI,KAAK,EAAC,cAAc;oBAAA4F,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClDpO,OAAA;oBAAQmI,KAAK,EAAC,aAAa;oBAAA4F,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,oBAAgB,eAAApO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC7C/N,OAAA;gBAAK8N,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACxD/N,OAAA;kBAAA+N,QAAA,gBACE/N,OAAA;oBAAA+N,QAAA,EAAM;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvBpO,OAAA;oBAAM8N,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAEtL,UAAU,CAACQ,UAAU,CAACsB,MAAM,EAAC,WAAS;kBAAA;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3EpO,OAAA;oBACEqK,IAAI,EAAC,QAAQ;oBACboE,KAAK,EAAE;sBAAEyB,UAAU,EAAE,MAAM;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,QAAQ,EAAE;oBAAQ,CAAE;oBACrE/B,OAAO,EAAEA,CAAA,KAAM3L,aAAa,CAAC2N,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEpN,UAAU,EAAE;oBAAG,CAAC,CAAC,CAAE;oBAAA8K,QAAA,EACrE;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpO,OAAA;oBACEqK,IAAI,EAAC,QAAQ;oBACboE,KAAK,EAAE;sBAAEyB,UAAU,EAAE,KAAK;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,QAAQ,EAAE,OAAO;sBAAEE,eAAe,EAAE,SAAS;sBAAEjB,KAAK,EAAE,OAAO;sBAAEkB,MAAM,EAAE,MAAM;sBAAEC,YAAY,EAAE;oBAAM,CAAE;oBACrJnC,OAAO,EAAEhK,wBAAyB;oBAClCyK,KAAK,EAAC,yBAAyB;oBAAAf,QAAA,gBAE/B/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAErO,MAAO;sBAAC8O,KAAK,EAAE;wBAAEI,WAAW,EAAE;sBAAM;oBAAE;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAElE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACRpO,OAAA;kBAAK8N,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxC/N,OAAA;oBAAOyO,KAAK,EAAE;sBAAEY,KAAK,EAAE,SAAS;sBAAEoB,SAAS,EAAE;oBAAS,CAAE;oBAAA1C,QAAA,EAAC;kBAGzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNpO,OAAA;kBAAK8N,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1C5J,kBAAkB,CAACiB,GAAG,CAAC,CAAC6G,KAAK,EAAEyE,KAAK,kBACf1Q,OAAA;oBAEE8N,SAAS,EAAE,qBAAqBrL,UAAU,CAACQ,UAAU,CAAC+F,QAAQ,CAACiD,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAA8B,QAAA,gBAEpH/N,OAAA;sBACEqK,IAAI,EAAC,UAAU;sBACf3E,EAAE,EAAE,YAAYgL,KAAK,EAAG;sBACxBpG,OAAO,EAAE7H,UAAU,CAACQ,UAAU,CAAC+F,QAAQ,CAACiD,KAAK,CAAE;sBAC/CuC,QAAQ,EAAEvG,CAAC,IAAI;wBACb,MAAMqC,OAAO,GAAGrC,CAAC,CAACC,MAAM,CAACoC,OAAO;wBAChC5H,aAAa,CAAC2N,IAAI,IAAI;0BACpB,MAAMM,GAAG,GAAG,IAAIC,GAAG,CAACP,IAAI,CAACpN,UAAU,CAAC;0BACpC,IAAIqH,OAAO,EAAE;4BACXqG,GAAG,CAACE,GAAG,CAAC5E,KAAK,CAAC;0BAChB,CAAC,MAAM;4BACL0E,GAAG,CAACtJ,MAAM,CAAC4E,KAAK,CAAC;0BACnB;0BACA,OAAO;4BACL,GAAGoE,IAAI;4BACPpN,UAAU,EAAEoG,KAAK,CAACyH,IAAI,CAACH,GAAG;0BAC5B,CAAC;wBACH,CAAC,CAAC;sBACJ;oBAAE;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACuB,CAAC,eACFpO,OAAA;sBAAOoP,OAAO,EAAE,YAAYsB,KAAK,EAAG;sBAAA3C,QAAA,EACjC9B;oBAAK;sBAAAgC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA,GAzBHsC,KAAK;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0BP,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/N,OAAA;gBAAK8N,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC/N,OAAA;kBAAOoP,OAAO,EAAC,iBAAiB;kBAAArB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxDpO,OAAA;kBACE0F,EAAE,EAAC,iBAAiB;kBACpB0E,IAAI,EAAC,iBAAiB;kBACtB2G,IAAI,EAAC,GAAG;kBACR5I,KAAK,EAAE1F,UAAU,CAACa,eAAgB;kBAClCkL,QAAQ,EAAErE,gBAAiB;kBAC3BmF,QAAQ;kBACRf,WAAW,EAAC;gBAAgC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACZpO,OAAA;kBAAO8N,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpO,OAAA;cAAK8N,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB/N,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAA+N,QAAA,EAAO;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7BpO,OAAA;kBAAK8N,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC/N,OAAA;oBACEqK,IAAI,EAAC,MAAM;oBACX3E,EAAE,EAAC,gBAAgB;oBACnB8I,QAAQ,EAAGvG,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,gBAAgB,CAAE;oBACxDyH,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFpO,OAAA;oBAAOoP,OAAO,EAAC,gBAAgB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC3D/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAEzO;oBAAQ;sBAAA0O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCpO,OAAA;sBAAA+N,QAAA,EAAM;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLvK,eAAe,iBACd7D,OAAA;kBAAK8N,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5B/N,OAAA;oBAAKuP,GAAG,EAAE1L,eAAgB;oBAAC2L,GAAG,EAAC;kBAAU;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNpO,OAAA;gBAAK8N,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/N,OAAA;kBAAA+N,QAAA,EAAO;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjCpO,OAAA;kBAAK8N,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpC/N,OAAA;oBACEqK,IAAI,EAAC,MAAM;oBACX3E,EAAE,EAAC,oBAAoB;oBACvB8I,QAAQ,EAAGvG,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,oBAAoB,CAAE;oBAC5DyH,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFpO,OAAA;oBAAOoP,OAAO,EAAC,oBAAoB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/D/N,OAAA,CAACpB,eAAe;sBAACoP,IAAI,EAAEzO;oBAAQ;sBAAA0O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCpO,OAAA;sBAAA+N,QAAA,EAAM;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLrK,gBAAgB,iBACf/D,OAAA;kBAAK8N,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChC/N,OAAA;oBAAKuP,GAAG,EAAExL,gBAAiB;oBAACyL,GAAG,EAAC;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAINpO,OAAA;cAAK8N,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B/N,OAAA;gBAAQqK,IAAI,EAAC,QAAQ;gBAACyD,SAAS,EAAC,eAAe;gBAACO,OAAO,EAAEtE,UAAW;gBAAAgE,QAAA,EAAC;cAErE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpO,OAAA;gBAAQqK,IAAI,EAAC,QAAQ;gBAACyD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5CjN,WAAW,GAAG,YAAY,GAAG;cAAY;gBAAAmN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/N,EAAA,CAn/CID,SAAS;AAAA4Q,EAAA,GAAT5Q,SAAS;AAq/Cf,eAAeA,SAAS;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}