{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\Admin\\\\AdminPanel.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable no-unused-vars */\nimport React, { useState, useEffect } from 'react';\nimport '../../css/AdminPanel.css';\nimport '../../css/search-fix.css';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faUsers, faCalendar, faComments, faCog, faBars, faBell, faSearch, faBriefcase, faUserCircle, faChartPie, faFileAlt, faSignOutAlt, faBuilding, faIdCard, faTags } from '@fortawesome/free-solid-svg-icons';\nimport JobsAdmin from './JobsAdmin';\nimport AnalyticsDashboard from './AnalyticsDashboard';\nimport BlogAdmin from './BlogAdmin';\nimport CompaniesAdmin from './CompaniesAdmin';\nimport CvAdmin from './CvAdmin';\nimport AdminUsers from './AdminUsers';\nimport SubTopicsAdmin from './SubTopicsAdmin';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminPanel = ({\n  onJobPost\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [currentUser, setCurrentUser] = useState({\n    name: 'Admin User',\n    role: 'Administrator'\n  });\n\n  // Get current user information from localStorage\n  useEffect(() => {\n    const userData = localStorage.getItem('user');\n    if (userData) {\n      try {\n        const user = JSON.parse(userData);\n        setCurrentUser({\n          name: user.admin_username || user.name || user.username || 'Admin User',\n          role: user.role || 'Administrator'\n        });\n        // Set default active item based on user role\n        // For Editor role, default to Job Listings (id: 1)\n        setActiveSidebarItem(user.role === 'Editor' ? 1 : 0);\n      } catch (error) {\n        console.error('Error parsing user data:', error);\n      }\n    }\n  }, []);\n\n  // Auto sign-out when tab is closed or refreshed\n  useEffect(() => {\n    const handleBeforeUnload = () => {\n      // Clear authentication data when tab is being closed or refreshed\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n    };\n    const handleVisibilityChange = () => {\n      // Clear authentication data when tab becomes hidden (user switches tabs or minimizes)\n      if (document.visibilityState === 'hidden') {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    };\n\n    // Add event listeners\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n\n    // Cleanup event listeners on component unmount\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n    };\n  }, []);\n\n  // Function to show logout confirmation modal\n  const handleLogout = () => {\n    setShowLogoutModal(true);\n  };\n\n  // Function to confirm logout\n  const confirmLogout = () => {\n    // Clear authentication data before redirecting\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    navigate('/jp-admin');\n  };\n\n  // Function to cancel logout\n  const cancelLogout = () => {\n    setShowLogoutModal(false);\n  };\n  const [jobData, setJobData] = useState({\n    hot: false,\n    image: \"\",\n    logo: \"\",\n    company: \"\",\n    position: \"\",\n    location: \"\",\n    workTime: \"Full Time\",\n    salary: \"\",\n    postedTime: \"Just now\",\n    views: \"0\"\n  });\n  const [imagePreview, setImagePreview] = useState(null);\n  const [logoPreview, setLogoPreview] = useState(null);\n  const [activeTab, setActiveTab] = useState('newJob');\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [activeSidebarItem, setActiveSidebarItem] = useState(0);\n  const [activeAccordion, setActiveAccordion] = useState('dashboard');\n  const [showLogoutModal, setShowLogoutModal] = useState(false);\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setJobData({\n      ...jobData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n  const handleImageUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        setImagePreview(reader.result);\n        setJobData({\n          ...jobData,\n          image: reader.result\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleLogoUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        setLogoPreview(reader.result);\n        setJobData({\n          ...jobData,\n          logo: reader.result\n        });\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    onJobPost(jobData);\n\n    // Reset form\n    setJobData({\n      hot: false,\n      image: \"\",\n      logo: \"\",\n      company: \"\",\n      position: \"\",\n      location: \"\",\n      workTime: \"Full Time\",\n      salary: \"\",\n      postedTime: \"Just now\",\n      views: \"0\"\n    });\n    setImagePreview(null);\n    setLogoPreview(null);\n  };\n  const handleSidebarItemClick = index => {\n    setActiveSidebarItem(index);\n    setIsSidebarOpen(false); // Close sidebar on mobile after item selection\n  };\n  const toggleAccordion = accordionName => {\n    setActiveAccordion(activeAccordion === accordionName ? null : accordionName);\n  };\n\n  // Function to filter sidebar items based on user role\n  const getFilteredSidebarStructure = () => {\n    const fullSidebarStructure = [{\n      category: \"Main\",\n      items: [{\n        id: 0,\n        title: \"Analytics\",\n        icon: faChartPie\n      }]\n    }, {\n      category: \"Job Management\",\n      items: [{\n        id: 1,\n        title: \"Job Listings\",\n        icon: faBriefcase\n      }, {\n        id: 8,\n        title: \"Sub Topics\",\n        icon: faTags,\n        roles: ['Admin', 'Superadmin']\n      }, {\n        id: 6,\n        title: \"Companies\",\n        icon: faBuilding\n      }, {\n        id: 7,\n        title: \"CV Management\",\n        icon: faIdCard\n      }]\n    }, {\n      category: \"Content\",\n      items: [{\n        id: 2,\n        title: \"Blog\",\n        icon: faFileAlt\n      }]\n    }, {\n      category: \"Administration\",\n      items: [{\n        id: 4,\n        title: \"User Accounts\",\n        icon: faUserCircle\n      }, {\n        id: 5,\n        title: \"Logout\",\n        icon: faSignOutAlt\n      }]\n    }];\n\n    // Helper function to filter items based on user role\n    const filterItemsByRole = items => {\n      return items.filter(item => {\n        // If item has roles restriction, check if current user role is allowed\n        if (item.roles && Array.isArray(item.roles)) {\n          return item.roles.includes(currentUser.role);\n        }\n        // If no roles restriction, item is available to all roles\n        return true;\n      });\n    };\n\n    // Filter sidebar items based on user role\n    if (currentUser.role === 'Editor') {\n      return [{\n        category: \"Job Management\",\n        items: filterItemsByRole([{\n          id: 1,\n          title: \"Job Listings\",\n          icon: faBriefcase\n        }, {\n          id: 8,\n          title: \"Sub Topics\",\n          icon: faTags,\n          roles: ['Admin', 'Superadmin']\n        }, {\n          id: 6,\n          title: \"Companies\",\n          icon: faBuilding\n        }])\n      }, {\n        category: \"Content\",\n        items: [{\n          id: 2,\n          title: \"Blog\",\n          icon: faFileAlt\n        }]\n      }, {\n        category: \"Account\",\n        items: [{\n          id: 5,\n          title: \"Logout\",\n          icon: faSignOutAlt\n        }]\n      }];\n    }\n\n    // For Admin and Superadmin roles, filter the full structure\n    return fullSidebarStructure.map(category => ({\n      ...category,\n      items: filterItemsByRole(category.items)\n    }));\n  };\n\n  // Get filtered sidebar structure based on user role\n  const sidebarStructure = getFilteredSidebarStructure();\n\n  // Render content based on selected sidebar item\n  const renderContent = () => {\n    switch (activeSidebarItem) {\n      case 0:\n        // Analytics\n        return /*#__PURE__*/_jsxDEV(AnalyticsDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 16\n        }, this);\n      case 1:\n        // Jobs\n        return /*#__PURE__*/_jsxDEV(JobsAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 16\n        }, this);\n      case 2:\n        // Blog\n        return /*#__PURE__*/_jsxDEV(BlogAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 16\n        }, this);\n      case 4:\n        // User Accounts\n        return /*#__PURE__*/_jsxDEV(AdminUsers, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 16\n        }, this);\n      // case 5: // Logout - This was causing the double popup\n      //  handleLogout();\n      //  return null;\n      case 6:\n        // Companies\n        return /*#__PURE__*/_jsxDEV(CompaniesAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 16\n        }, this);\n      case 7:\n        // CV Management\n        return /*#__PURE__*/_jsxDEV(CvAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 16\n        }, this);\n      case 8:\n        // Sub Topics\n        return /*#__PURE__*/_jsxDEV(SubTopicsAdmin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Select an option from the sidebar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"admin-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"menu-button mobile-only\",\n          onClick: () => setIsSidebarOpen(!isSidebarOpen),\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faBars\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Job Page Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-controls\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n        className: `admin-sidebar ${isSidebarOpen ? 'open' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faBriefcase\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Job Page \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-nav-container\",\n          children: sidebarStructure.map((section, sectionIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"nav-group-title\",\n              children: section.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), section.items.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `nav-link ${activeSidebarItem === item.id ? 'active' : ''}`,\n              onClick: () => item.id === 5 ? handleLogout() : handleSidebarItemClick(item.id),\n              children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: item.icon,\n                className: `nav-icon ${activeSidebarItem === item.id ? 'active' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this))]\n          }, sectionIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-profile\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-avatar\",\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faUserCircle,\n              size: \"lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-name\",\n              children: currentUser.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"user-role\",\n              children: currentUser.role\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"admin-main\",\n        children: renderContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-bottom-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mobile-nav-item ${activeSidebarItem === 0 ? 'active' : ''}`,\n        onClick: () => handleSidebarItemClick(0),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faChartPie\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mobile-nav-item ${activeSidebarItem === 1 ? 'active' : ''}`,\n        onClick: () => handleSidebarItemClick(1),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faBriefcase\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Jobs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mobile-nav-item ${activeSidebarItem === 2 ? 'active' : ''}`,\n        onClick: () => handleSidebarItemClick(2),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faFileAlt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Blog\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mobile-nav-item ${activeSidebarItem === 7 ? 'active' : ''}`,\n        onClick: () => handleSidebarItemClick(7),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-icon\",\n          children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faIdCard\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"CVs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), showLogoutModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logout-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSignOutAlt,\n            className: \"modal-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-body\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to logout? You will need to sign in again to access the admin panel.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-cancel\",\n            onClick: cancelLogout,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-confirm\",\n            onClick: confirmLogout,\n            children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faSignOutAlt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminPanel, \"B7QKhcz+XxMKhh68YEHhs+T3Fpw=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminPanel;\nexport default AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FontAwesomeIcon", "faUsers", "faCalendar", "faComments", "faCog", "faBars", "faBell", "faSearch", "faBriefcase", "faUserCircle", "faChartPie", "faFileAlt", "faSignOutAlt", "faBuilding", "faIdCard", "faTags", "JobsAdmin", "AnalyticsDashboard", "BlogAdmin", "CompaniesAdmin", "CvAdmin", "AdminUsers", "SubTopicsAdmin", "useNavigate", "jsxDEV", "_jsxDEV", "AdminPanel", "onJobPost", "_s", "navigate", "currentUser", "setCurrentUser", "name", "role", "userData", "localStorage", "getItem", "user", "JSON", "parse", "admin_username", "username", "setActiveSidebarItem", "error", "console", "handleBeforeUnload", "removeItem", "handleVisibilityChange", "document", "visibilityState", "window", "addEventListener", "removeEventListener", "handleLogout", "setShowLogoutModal", "confirmLogout", "cancelLogout", "jobData", "setJobData", "hot", "image", "logo", "company", "position", "location", "workTime", "salary", "postedTime", "views", "imagePreview", "setImagePreview", "logoPreview", "setLogoPreview", "activeTab", "setActiveTab", "isSidebarOpen", "setIsSidebarOpen", "activeSidebarItem", "activeAccordion", "setActiveAccordion", "showLogoutModal", "handleInputChange", "e", "value", "type", "checked", "target", "handleImageUpload", "file", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleLogoUpload", "handleSubmit", "preventDefault", "handleSidebarItemClick", "index", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getFilteredSidebarStructure", "fullSidebarStructure", "category", "items", "id", "title", "icon", "roles", "filterItemsByRole", "filter", "item", "Array", "isArray", "includes", "map", "sidebarStructure", "renderContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "onClick", "section", "sectionIndex", "size", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/Admin/AdminPanel.jsx"], "sourcesContent": ["/* eslint-disable no-unused-vars */\r\nimport React, { useState, useEffect } from 'react';\r\nimport '../../css/AdminPanel.css';\r\nimport '../../css/search-fix.css';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n  faUsers,\r\n  faCalendar,\r\n  faComments,\r\n  faCog,\r\n  faBars,\r\n  faBell,\r\n  faSearch,\r\n  faBriefcase,\r\n  faUserCircle,\r\n  faChartPie,\r\n  faFileAlt,\r\n  faSignOutAlt,\r\n  faBuilding,\r\n  faIdCard,\r\n  faTags\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport JobsAdmin from './JobsAdmin';\r\nimport AnalyticsDashboard from './AnalyticsDashboard';\r\nimport BlogAdmin from './BlogAdmin';\r\nimport CompaniesAdmin from './CompaniesAdmin';\r\nimport CvAdmin from './CvAdmin';\r\nimport AdminUsers from './AdminUsers';\r\nimport SubTopicsAdmin from './SubTopicsAdmin';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst AdminPanel = ({ onJobPost }) => {\r\n  const navigate = useNavigate();\r\n  const [currentUser, setCurrentUser] = useState({ name: 'Admin User', role: 'Administrator' });\r\n\r\n  // Get current user information from localStorage\r\n  useEffect(() => {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        setCurrentUser({\r\n          name: user.admin_username || user.name || user.username || 'Admin User',\r\n          role: user.role || 'Administrator'\r\n        });\r\n        // Set default active item based on user role\r\n        // For Editor role, default to Job Listings (id: 1)\r\n        setActiveSidebarItem(user.role === 'Editor' ? 1 : 0);\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Auto sign-out when tab is closed or refreshed\r\n  useEffect(() => {\r\n    const handleBeforeUnload = () => {\r\n      // Clear authentication data when tab is being closed or refreshed\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('user');\r\n    };\r\n\r\n    const handleVisibilityChange = () => {\r\n      // Clear authentication data when tab becomes hidden (user switches tabs or minimizes)\r\n      if (document.visibilityState === 'hidden') {\r\n        localStorage.removeItem('token');\r\n        localStorage.removeItem('user');\r\n      }\r\n    };\r\n\r\n    // Add event listeners\r\n    window.addEventListener('beforeunload', handleBeforeUnload);\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n\r\n    // Cleanup event listeners on component unmount\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleBeforeUnload);\r\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n    };\r\n  }, []);\r\n\r\n  // Function to show logout confirmation modal\r\n  const handleLogout = () => {\r\n    setShowLogoutModal(true);\r\n  };\r\n\r\n  // Function to confirm logout\r\n  const confirmLogout = () => {\r\n    // Clear authentication data before redirecting\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n    navigate('/jp-admin');\r\n  };\r\n\r\n  // Function to cancel logout\r\n  const cancelLogout = () => {\r\n    setShowLogoutModal(false);\r\n  };\r\n\r\n  const [jobData, setJobData] = useState({\r\n    hot: false,\r\n    image: \"\",\r\n    logo: \"\",\r\n    company: \"\",\r\n    position: \"\",\r\n    location: \"\",\r\n    workTime: \"Full Time\",\r\n    salary: \"\",\r\n    postedTime: \"Just now\",\r\n    views: \"0\"\r\n  });\r\n\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [logoPreview, setLogoPreview] = useState(null);\r\n  const [activeTab, setActiveTab] = useState('newJob');\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n  const [activeSidebarItem, setActiveSidebarItem] = useState(0);\r\n  const [activeAccordion, setActiveAccordion] = useState('dashboard');\r\n  const [showLogoutModal, setShowLogoutModal] = useState(false);\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setJobData({\r\n      ...jobData,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    });\r\n  };\r\n\r\n  const handleImageUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        setImagePreview(reader.result);\r\n        setJobData({ ...jobData, image: reader.result });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleLogoUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        setLogoPreview(reader.result);\r\n        setJobData({ ...jobData, logo: reader.result });\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    onJobPost(jobData);\r\n    \r\n    // Reset form\r\n    setJobData({\r\n      hot: false,\r\n      image: \"\",\r\n      logo: \"\",\r\n      company: \"\",\r\n      position: \"\",\r\n      location: \"\",\r\n      workTime: \"Full Time\",\r\n      salary: \"\",\r\n      postedTime: \"Just now\",\r\n      views: \"0\"\r\n    });\r\n    setImagePreview(null);\r\n    setLogoPreview(null);\r\n  };\r\n\r\n  const handleSidebarItemClick = (index) => {\r\n    setActiveSidebarItem(index);\r\n    setIsSidebarOpen(false); // Close sidebar on mobile after item selection\r\n  };\r\n\r\n  const toggleAccordion = (accordionName) => {\r\n    setActiveAccordion(activeAccordion === accordionName ? null : accordionName);\r\n  };\r\n\r\n  // Function to filter sidebar items based on user role\r\n  const getFilteredSidebarStructure = () => {\r\n    const fullSidebarStructure = [\r\n      {\r\n        category: \"Main\",\r\n        items: [\r\n          { id: 0, title: \"Analytics\", icon: faChartPie },\r\n        ]\r\n      },\r\n      {\r\n        category: \"Job Management\",\r\n        items: [\r\n          { id: 1, title: \"Job Listings\", icon: faBriefcase },\r\n          { id: 8, title: \"Sub Topics\", icon: faTags, roles: ['Admin', 'Superadmin'] },\r\n          { id: 6, title: \"Companies\", icon: faBuilding },\r\n          { id: 7, title: \"CV Management\", icon: faIdCard },\r\n        ]\r\n      },\r\n      {\r\n        category: \"Content\",\r\n        items: [\r\n          { id: 2, title: \"Blog\", icon: faFileAlt },\r\n        ]\r\n      },\r\n      {\r\n        category: \"Administration\",\r\n        items: [\r\n          { id: 4, title: \"User Accounts\", icon: faUserCircle },\r\n          { id: 5, title: \"Logout\", icon: faSignOutAlt },\r\n        ]\r\n      }\r\n    ];\r\n\r\n    // Helper function to filter items based on user role\r\n    const filterItemsByRole = (items) => {\r\n      return items.filter(item => {\r\n        // If item has roles restriction, check if current user role is allowed\r\n        if (item.roles && Array.isArray(item.roles)) {\r\n          return item.roles.includes(currentUser.role);\r\n        }\r\n        // If no roles restriction, item is available to all roles\r\n        return true;\r\n      });\r\n    };\r\n\r\n    // Filter sidebar items based on user role\r\n    if (currentUser.role === 'Editor') {\r\n      return [\r\n        {\r\n          category: \"Job Management\",\r\n          items: filterItemsByRole([\r\n            { id: 1, title: \"Job Listings\", icon: faBriefcase },\r\n            { id: 8, title: \"Sub Topics\", icon: faTags, roles: ['Admin', 'Superadmin'] },\r\n            { id: 6, title: \"Companies\", icon: faBuilding },\r\n          ])\r\n        },\r\n        {\r\n          category: \"Content\",\r\n          items: [\r\n            { id: 2, title: \"Blog\", icon: faFileAlt },\r\n          ]\r\n        },\r\n        {\r\n          category: \"Account\",\r\n          items: [\r\n            { id: 5, title: \"Logout\", icon: faSignOutAlt },\r\n          ]\r\n        }\r\n      ];\r\n    }\r\n\r\n    // For Admin and Superadmin roles, filter the full structure\r\n    return fullSidebarStructure.map(category => ({\r\n      ...category,\r\n      items: filterItemsByRole(category.items)\r\n    }));\r\n  };\r\n\r\n  // Get filtered sidebar structure based on user role\r\n  const sidebarStructure = getFilteredSidebarStructure();\r\n\r\n  // Render content based on selected sidebar item\r\n  const renderContent = () => {\r\n    switch (activeSidebarItem) {\r\n      case 0: // Analytics\r\n        return <AnalyticsDashboard />;\r\n      case 1: // Jobs\r\n        return <JobsAdmin />;\r\n      case 2: // Blog\r\n        return <BlogAdmin />;\r\n      case 4: // User Accounts\r\n        return <AdminUsers />;\r\n      // case 5: // Logout - This was causing the double popup\r\n      //  handleLogout();\r\n      //  return null;\r\n      case 6: // Companies\r\n        return <CompaniesAdmin />;\r\n      case 7: // CV Management\r\n        return <CvAdmin />;\r\n      case 8: // Sub Topics\r\n        return <SubTopicsAdmin />;\r\n      default:\r\n        return <div>Select an option from the sidebar</div>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"admin-container\">\r\n      {/* Header */}\r\n      <header className=\"admin-header\">\r\n        <div className=\"header-left\">\r\n          <button className=\"menu-button mobile-only\" onClick={() => setIsSidebarOpen(!isSidebarOpen)}>\r\n            <FontAwesomeIcon icon={faBars} />\r\n          </button>\r\n          <h1>Job Page Dashboard</h1>\r\n        </div>\r\n        \r\n        <div className=\"header-controls\">\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"admin-content\">\r\n        {/* Sidebar */}\r\n        <aside className={`admin-sidebar ${isSidebarOpen ? 'open' : ''}`}>\r\n          {/* Logo */}\r\n          <div className=\"logo-container\">\r\n            <div className=\"logo\">\r\n              <FontAwesomeIcon icon={faBriefcase} />\r\n              <span>Job Page </span>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Navigation sections - will stretch to fill available space */}\r\n          <div className=\"sidebar-nav-container\">\r\n            {sidebarStructure.map((section, sectionIndex) => (\r\n              <div key={sectionIndex} className=\"nav-group\">\r\n                <h3 className=\"nav-group-title\">{section.category}</h3>\r\n                {section.items.map((item) => (\r\n                  <div\r\n                    key={item.id}\r\n                    className={`nav-link ${activeSidebarItem === item.id ? 'active' : ''}`}\r\n                    onClick={() => item.id === 5 ? handleLogout() : handleSidebarItemClick(item.id)}\r\n                  >\r\n                    <FontAwesomeIcon \r\n                      icon={item.icon} \r\n                      className={`nav-icon ${activeSidebarItem === item.id ? 'active' : ''}`} \r\n                    />\r\n                    <span>{item.title}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ))}\r\n          </div>\r\n          \r\n          {/* User profile - will stay at the bottom */}\r\n          <div className=\"user-profile\">\r\n            <div className=\"user-avatar\">\r\n              <FontAwesomeIcon icon={faUserCircle} size=\"lg\" />\r\n            </div>\r\n            <div className=\"user-info\">\r\n              <span className=\"user-name\">{currentUser.name}</span>\r\n              <span className=\"user-role\">{currentUser.role}</span>\r\n            </div>\r\n          </div>\r\n        </aside>\r\n\r\n        {/* Main Content */}\r\n        <main className=\"admin-main\">\r\n          {/* Render content based on sidebar selection */}\r\n          {renderContent()}\r\n        </main>\r\n      </div>\r\n            \r\n      {/* Mobile Bottom Navigation */}\r\n      <div className=\"mobile-bottom-nav\">\r\n        <div \r\n          className={`mobile-nav-item ${activeSidebarItem === 0 ? 'active' : ''}`}\r\n          onClick={() => handleSidebarItemClick(0)}\r\n        >\r\n          <div className=\"mobile-nav-icon\">\r\n            <FontAwesomeIcon icon={faChartPie} />\r\n          </div>\r\n          <span>Analytics</span>\r\n        </div>\r\n        <div \r\n          className={`mobile-nav-item ${activeSidebarItem === 1 ? 'active' : ''}`}\r\n          onClick={() => handleSidebarItemClick(1)}\r\n        >\r\n          <div className=\"mobile-nav-icon\">\r\n            <FontAwesomeIcon icon={faBriefcase} />\r\n          </div>\r\n          <span>Jobs</span>\r\n        </div>\r\n        <div \r\n          className={`mobile-nav-item ${activeSidebarItem === 2 ? 'active' : ''}`}\r\n          onClick={() => handleSidebarItemClick(2)}\r\n        >\r\n          <div className=\"mobile-nav-icon\">\r\n            <FontAwesomeIcon icon={faFileAlt} />\r\n          </div>\r\n          <span>Blog</span>\r\n        </div>\r\n        <div \r\n          className={`mobile-nav-item ${activeSidebarItem === 7 ? 'active' : ''}`}\r\n          onClick={() => handleSidebarItemClick(7)}\r\n        >\r\n          <div className=\"mobile-nav-icon\">\r\n            <FontAwesomeIcon icon={faIdCard} />\r\n          </div>\r\n          <span>CVs</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Logout Confirmation Modal */}\r\n      {showLogoutModal && (\r\n        <div className=\"modal-overlay\">\r\n          <div className=\"logout-modal\">\r\n            <div className=\"modal-header\">\r\n              <FontAwesomeIcon icon={faSignOutAlt} className=\"modal-icon\" />\r\n              <h3>Confirm Logout</h3>\r\n            </div>\r\n            <div className=\"modal-body\">\r\n              <p>Are you sure you want to logout? You will need to sign in again to access the admin panel.</p>\r\n            </div>\r\n            <div className=\"modal-footer\">\r\n              <button className=\"btn-cancel\" onClick={cancelLogout}>\r\n                Cancel\r\n              </button>\r\n              <button className=\"btn-confirm\" onClick={confirmLogout}>\r\n                <FontAwesomeIcon icon={faSignOutAlt} />\r\n                Logout\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminPanel;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,OAAO,0BAA0B;AACjC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,UAAU,EACVC,SAAS,EACTC,YAAY,EACZC,UAAU,EACVC,QAAQ,EACRC,MAAM,QACD,mCAAmC;AAC1C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC;IAAEkC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAgB,CAAC,CAAC;;EAE7F;EACAlC,SAAS,CAAC,MAAM;IACd,MAAMmC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC7C,IAAIF,QAAQ,EAAE;MACZ,IAAI;QACF,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;QACjCH,cAAc,CAAC;UACbC,IAAI,EAAEK,IAAI,CAACG,cAAc,IAAIH,IAAI,CAACL,IAAI,IAAIK,IAAI,CAACI,QAAQ,IAAI,YAAY;UACvER,IAAI,EAAEI,IAAI,CAACJ,IAAI,IAAI;QACrB,CAAC,CAAC;QACF;QACA;QACAS,oBAAoB,CAACL,IAAI,CAACJ,IAAI,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;MACtD,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5C,SAAS,CAAC,MAAM;IACd,MAAM8C,kBAAkB,GAAGA,CAAA,KAAM;MAC/B;MACAV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;MAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;MACnC;MACA,IAAIC,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;QACzCd,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;QAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;MACjC;IACF,CAAC;;IAED;IACAI,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEN,kBAAkB,CAAC;IAC3DG,QAAQ,CAACG,gBAAgB,CAAC,kBAAkB,EAAEJ,sBAAsB,CAAC;;IAErE;IACA,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEP,kBAAkB,CAAC;MAC9DG,QAAQ,CAACI,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACApB,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;IAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IAC/BjB,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzBF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC;IACrC6D,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+E,iBAAiB,EAAEnC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACgF,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,WAAW,CAAC;EACnE,MAAM,CAACkF,eAAe,EAAE1B,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMmF,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAElD,IAAI;MAAEmD,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IAC/C5B,UAAU,CAAC;MACT,GAAGD,OAAO;MACV,CAACzB,IAAI,GAAGoD,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,iBAAiB,GAAIL,CAAC,IAAK;IAC/B,MAAMM,IAAI,GAAGN,CAAC,CAACI,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpBtB,eAAe,CAACoB,MAAM,CAACG,MAAM,CAAC;QAC9BnC,UAAU,CAAC;UAAE,GAAGD,OAAO;UAAEG,KAAK,EAAE8B,MAAM,CAACG;QAAO,CAAC,CAAC;MAClD,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,gBAAgB,GAAIb,CAAC,IAAK;IAC9B,MAAMM,IAAI,GAAGN,CAAC,CAACI,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpBpB,cAAc,CAACkB,MAAM,CAACG,MAAM,CAAC;QAC7BnC,UAAU,CAAC;UAAE,GAAGD,OAAO;UAAEI,IAAI,EAAE6B,MAAM,CAACG;QAAO,CAAC,CAAC;MACjD,CAAC;MACDH,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMQ,YAAY,GAAId,CAAC,IAAK;IAC1BA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClBtE,SAAS,CAAC8B,OAAO,CAAC;;IAElB;IACAC,UAAU,CAAC;MACTC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,WAAW;MACrBC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,UAAU;MACtBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFE,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM0B,sBAAsB,GAAIC,KAAK,IAAK;IACxCzD,oBAAoB,CAACyD,KAAK,CAAC;IAC3BvB,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMwB,eAAe,GAAIC,aAAa,IAAK;IACzCtB,kBAAkB,CAACD,eAAe,KAAKuB,aAAa,GAAG,IAAI,GAAGA,aAAa,CAAC;EAC9E,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAGA,CAAA,KAAM;IACxC,MAAMC,oBAAoB,GAAG,CAC3B;MACEC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,CACL;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAElG;MAAW,CAAC;IAEnD,CAAC,EACD;MACE8F,QAAQ,EAAE,gBAAgB;MAC1BC,KAAK,EAAE,CACL;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,cAAc;QAAEC,IAAI,EAAEpG;MAAY,CAAC,EACnD;QAAEkG,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,YAAY;QAAEC,IAAI,EAAE7F,MAAM;QAAE8F,KAAK,EAAE,CAAC,OAAO,EAAE,YAAY;MAAE,CAAC,EAC5E;QAAEH,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE/F;MAAW,CAAC,EAC/C;QAAE6F,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE9F;MAAS,CAAC;IAErD,CAAC,EACD;MACE0F,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,CACL;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAEjG;MAAU,CAAC;IAE7C,CAAC,EACD;MACE6F,QAAQ,EAAE,gBAAgB;MAC1BC,KAAK,EAAE,CACL;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAEnG;MAAa,CAAC,EACrD;QAAEiG,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAEhG;MAAa,CAAC;IAElD,CAAC,CACF;;IAED;IACA,MAAMkG,iBAAiB,GAAIL,KAAK,IAAK;MACnC,OAAOA,KAAK,CAACM,MAAM,CAACC,IAAI,IAAI;QAC1B;QACA,IAAIA,IAAI,CAACH,KAAK,IAAII,KAAK,CAACC,OAAO,CAACF,IAAI,CAACH,KAAK,CAAC,EAAE;UAC3C,OAAOG,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACrF,WAAW,CAACG,IAAI,CAAC;QAC9C;QACA;QACA,OAAO,IAAI;MACb,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAIH,WAAW,CAACG,IAAI,KAAK,QAAQ,EAAE;MACjC,OAAO,CACL;QACEuE,QAAQ,EAAE,gBAAgB;QAC1BC,KAAK,EAAEK,iBAAiB,CAAC,CACvB;UAAEJ,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAEpG;QAAY,CAAC,EACnD;UAAEkG,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,YAAY;UAAEC,IAAI,EAAE7F,MAAM;UAAE8F,KAAK,EAAE,CAAC,OAAO,EAAE,YAAY;QAAE,CAAC,EAC5E;UAAEH,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,WAAW;UAAEC,IAAI,EAAE/F;QAAW,CAAC,CAChD;MACH,CAAC,EACD;QACE2F,QAAQ,EAAE,SAAS;QACnBC,KAAK,EAAE,CACL;UAAEC,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,MAAM;UAAEC,IAAI,EAAEjG;QAAU,CAAC;MAE7C,CAAC,EACD;QACE6F,QAAQ,EAAE,SAAS;QACnBC,KAAK,EAAE,CACL;UAAEC,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,QAAQ;UAAEC,IAAI,EAAEhG;QAAa,CAAC;MAElD,CAAC,CACF;IACH;;IAEA;IACA,OAAO2F,oBAAoB,CAACa,GAAG,CAACZ,QAAQ,KAAK;MAC3C,GAAGA,QAAQ;MACXC,KAAK,EAAEK,iBAAiB,CAACN,QAAQ,CAACC,KAAK;IACzC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAGf,2BAA2B,CAAC,CAAC;;EAEtD;EACA,MAAMgB,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQzC,iBAAiB;MACvB,KAAK,CAAC;QAAE;QACN,oBAAOpD,OAAA,CAACR,kBAAkB;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACT,SAAS;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACP,SAAS;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACJ,UAAU;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvB;MACA;MACA;MACA,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACN,cAAc;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACL,OAAO;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB,KAAK,CAAC;QAAE;QACN,oBAAOjG,OAAA,CAACH,cAAc;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B;QACE,oBAAOjG,OAAA;UAAAkG,QAAA,EAAK;QAAiC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACvD;EACF,CAAC;EAED,oBACEjG,OAAA;IAAKmG,SAAS,EAAC,iBAAiB;IAAAD,QAAA,gBAE9BlG,OAAA;MAAQmG,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAC9BlG,OAAA;QAAKmG,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1BlG,OAAA;UAAQmG,SAAS,EAAC,yBAAyB;UAACC,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC,CAACD,aAAa,CAAE;UAAAgD,QAAA,eAC1FlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAEvG;UAAO;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACTjG,OAAA;UAAAkG,QAAA,EAAI;QAAkB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAENjG,OAAA;QAAKmG,SAAS,EAAC;MAAiB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETjG,OAAA;MAAKmG,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAE5BlG,OAAA;QAAOmG,SAAS,EAAE,iBAAiBjD,aAAa,GAAG,MAAM,GAAG,EAAE,EAAG;QAAAgD,QAAA,gBAE/DlG,OAAA;UAAKmG,SAAS,EAAC,gBAAgB;UAAAD,QAAA,eAC7BlG,OAAA;YAAKmG,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBlG,OAAA,CAACzB,eAAe;cAAC4G,IAAI,EAAEpG;YAAY;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCjG,OAAA;cAAAkG,QAAA,EAAM;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjG,OAAA;UAAKmG,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EACnCN,gBAAgB,CAACD,GAAG,CAAC,CAACU,OAAO,EAAEC,YAAY,kBAC1CtG,OAAA;YAAwBmG,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAC3ClG,OAAA;cAAImG,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAAEG,OAAO,CAACtB;YAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACtDI,OAAO,CAACrB,KAAK,CAACW,GAAG,CAAEJ,IAAI,iBACtBvF,OAAA;cAEEmG,SAAS,EAAE,YAAY/C,iBAAiB,KAAKmC,IAAI,CAACN,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;cACvEmB,OAAO,EAAEA,CAAA,KAAMb,IAAI,CAACN,EAAE,KAAK,CAAC,GAAGrD,YAAY,CAAC,CAAC,GAAG6C,sBAAsB,CAACc,IAAI,CAACN,EAAE,CAAE;cAAAiB,QAAA,gBAEhFlG,OAAA,CAACzB,eAAe;gBACd4G,IAAI,EAAEI,IAAI,CAACJ,IAAK;gBAChBgB,SAAS,EAAE,YAAY/C,iBAAiB,KAAKmC,IAAI,CAACN,EAAE,GAAG,QAAQ,GAAG,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACFjG,OAAA;gBAAAkG,QAAA,EAAOX,IAAI,CAACL;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GARpBV,IAAI,CAACN,EAAE;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAST,CACN,CAAC;UAAA,GAdMK,YAAY;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAejB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjG,OAAA;UAAKmG,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BlG,OAAA;YAAKmG,SAAS,EAAC,aAAa;YAAAD,QAAA,eAC1BlG,OAAA,CAACzB,eAAe;cAAC4G,IAAI,EAAEnG,YAAa;cAACuH,IAAI,EAAC;YAAI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNjG,OAAA;YAAKmG,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBlG,OAAA;cAAMmG,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE7F,WAAW,CAACE;YAAI;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDjG,OAAA;cAAMmG,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE7F,WAAW,CAACG;YAAI;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRjG,OAAA;QAAMmG,SAAS,EAAC,YAAY;QAAAD,QAAA,EAEzBL,aAAa,CAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjG,OAAA;MAAKmG,SAAS,EAAC,mBAAmB;MAAAD,QAAA,gBAChClG,OAAA;QACEmG,SAAS,EAAE,mBAAmB/C,iBAAiB,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxEgD,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC,CAAC,CAAE;QAAAyB,QAAA,gBAEzClG,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAElG;UAAW;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNjG,OAAA;UAAAkG,QAAA,EAAM;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACNjG,OAAA;QACEmG,SAAS,EAAE,mBAAmB/C,iBAAiB,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxEgD,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC,CAAC,CAAE;QAAAyB,QAAA,gBAEzClG,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAEpG;UAAY;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACNjG,OAAA;UAAAkG,QAAA,EAAM;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACNjG,OAAA;QACEmG,SAAS,EAAE,mBAAmB/C,iBAAiB,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxEgD,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC,CAAC,CAAE;QAAAyB,QAAA,gBAEzClG,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAEjG;UAAU;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNjG,OAAA;UAAAkG,QAAA,EAAM;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACNjG,OAAA;QACEmG,SAAS,EAAE,mBAAmB/C,iBAAiB,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QACxEgD,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC,CAAC,CAAE;QAAAyB,QAAA,gBAEzClG,OAAA;UAAKmG,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9BlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAE9F;UAAS;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACNjG,OAAA;UAAAkG,QAAA,EAAM;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1C,eAAe,iBACdvD,OAAA;MAAKmG,SAAS,EAAC,eAAe;MAAAD,QAAA,eAC5BlG,OAAA;QAAKmG,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BlG,OAAA;UAAKmG,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BlG,OAAA,CAACzB,eAAe;YAAC4G,IAAI,EAAEhG,YAAa;YAACgH,SAAS,EAAC;UAAY;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DjG,OAAA;YAAAkG,QAAA,EAAI;UAAc;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACNjG,OAAA;UAAKmG,SAAS,EAAC,YAAY;UAAAD,QAAA,eACzBlG,OAAA;YAAAkG,QAAA,EAAG;UAA0F;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eACNjG,OAAA;UAAKmG,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3BlG,OAAA;YAAQmG,SAAS,EAAC,YAAY;YAACC,OAAO,EAAErE,YAAa;YAAAmE,QAAA,EAAC;UAEtD;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjG,OAAA;YAAQmG,SAAS,EAAC,aAAa;YAACC,OAAO,EAAEtE,aAAc;YAAAoE,QAAA,gBACrDlG,OAAA,CAACzB,eAAe;cAAC4G,IAAI,EAAEhG;YAAa;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9F,EAAA,CArYIF,UAAU;EAAA,QACGH,WAAW;AAAA;AAAA0G,EAAA,GADxBvG,UAAU;AAuYhB,eAAeA,UAAU;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}