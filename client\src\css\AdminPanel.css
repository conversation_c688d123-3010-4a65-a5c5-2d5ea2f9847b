/* Base styles and layout */
.admin-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

/* Header styles */
.admin-header {
  position: sticky;
  top: 0;
  z-index: 30;
  display: flex;
  align-items: center;
  height: 64px;
  border-bottom: 1px solid #e2e8f0;
  background-color: white;
  padding: 0 16px;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .admin-header {
    padding: 0 24px;
  }
}

.admin-header h1 {
  font-size: 1rem;
  font-weight: 700;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (min-width: 768px) {
  .admin-header h1 {
    font-size: 1.25rem;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  position: relative;
  display: none;
  align-items: center;
}

@media (min-width: 768px) {
  .search-container {
    display: flex;
  }
  
  .search-input {
    width: 300px;
  }
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 14px;
  z-index: 1;
  pointer-events: none;
}

.search-input {
  width: 240px;
  padding: 8px 8px 8px 38px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  text-indent: 5px;
}

.notification-button {
  position: relative;
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #8057ff;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-button {
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
}

/* Content area */
.admin-content {
  display: flex;
  flex: 1;
  position: relative;
  height: calc(100vh - 64px);
  overflow: hidden;
}

@media (min-width: 768px) {
  .admin-content {
    display: flex;
  }
}

/* Sidebar - Base structure only, styling is now inline */
.admin-sidebar {
  width: 250px;
  display: none;
  flex-direction: column;
  min-height: 100%;
  height: 100%;
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  background-color: #1e293b;
  color: #94a3b8;
}

@media (min-width: 768px) {
  .admin-sidebar {
    display: flex;
    position: relative;
  }
}

.admin-sidebar.open {
  display: flex;
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  z-index: 20;
  width: 250px;
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
  height: calc(100vh - 64px);
}

/* Main content area */
.admin-main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  width: 100%;
}

@media (min-width: 768px) {
  .admin-main {
    padding: 24px;
    width: calc(100% - 250px);
    margin-left: 0;
  }
}

/* Sidebar Elements */
.logo-container {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #334155;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-group {
  padding: 1rem 0;
  border-bottom: 1px solid #334155;
}

.nav-group-title {
  font-size: 0.75rem;
  font-weight: semibold;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 1.5rem;
  margin-bottom: 0.75rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #94a3b8;
  border-left: 4px solid transparent;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: normal;
}

.nav-link.active {
  color: #ffffff;
  border-left: 4px solid #8057ff;
  background-color: #334155;
  font-weight: 500;
}

.nav-icon {
  width: 1.25rem;
  margin-right: 0.75rem;
  color: #64748b;
}

.nav-icon.active {
  color: #8057ff;
}

.sidebar-nav-container {
  flex: 1 1 auto;
  overflow-y: auto;
}

/* User Profile Section */
.user-profile {
  padding: 1rem 1.5rem;
  border-top: 1px solid #334155;
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #334155;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
}

.user-role {
  color: #64748b;
  font-size: 0.75rem;
}

/* Logout button under user profile */
.user-profile .logout-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 0.9rem;
  cursor: pointer;
  margin-top: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: background 0.2s, color 0.2s;
}

.user-profile .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 20;
  height: 60px;
}

@media (min-width: 768px) {
  .mobile-bottom-nav {
    display: none;
  }
}

.mobile-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: color 0.2s;
  cursor: pointer;
  font-size: 12px;
  padding: 8px 0;
}

.mobile-nav-item.active {
  color: #8057ff;
}

.mobile-nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

/* Adjust main content padding for mobile bottom nav */
@media (max-width: 767px) {
  .admin-main {
    padding-bottom: 76px; /* 16px default padding + 60px nav height */
  }
}

/* Placeholder content styles for sidebar items */
.blog-placeholder,
.applicants-placeholder,
.calendar-placeholder,
.messages-placeholder,
.settings-placeholder,
.reports-placeholder,
.user-accounts-placeholder {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
  text-align: center;
  margin-bottom: 24px;
}

.blog-placeholder h3,
.applicants-placeholder h3,
.calendar-placeholder h3,
.messages-placeholder h3,
.settings-placeholder h3,
.reports-placeholder h3,
.user-accounts-placeholder h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.blog-placeholder p,
.applicants-placeholder p,
.calendar-placeholder p,
.messages-placeholder p,
.settings-placeholder p,
.reports-placeholder p,
.user-accounts-placeholder p {
  color: #64748b;
  font-size: 16px;
}

/* Mobile view */
@media (max-width: 767px) {
  .admin-header {
    padding: 0 12px;
  }
  
  .header-left h1 {
    font-size: 0.9rem;
  }
  
  .header-controls {
    gap: 8px;
  }
  
  .search-container {
    display: none;
  }
}

/* Tablet and larger */
@media (min-width: 768px) and (max-width: 1023px) {
  .search-input {
    width: 200px;
  }
}

/* Desktop view */
@media (min-width: 1024px) {
  .search-input {
    width: 300px;
  }
}

/* Logout Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.logout-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  margin: 20px;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.modal-header {
  padding: 24px 24px 16px 24px;
  text-align: center;
  border-bottom: 1px solid #f1f5f9;
}

.modal-icon {
  font-size: 48px;
  color: #ef4444;
  margin-bottom: 12px;
  display: block;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.modal-body {
  padding: 16px 24px 24px 24px;
  text-align: center;
}

.modal-body p {
  margin: 0;
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
}

.modal-footer {
  padding: 16px 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-cancel {
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-cancel:hover {
  background-color: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
}

.btn-confirm {
  background-color: #ef4444;
  color: white;
}

.btn-confirm:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-confirm:active,
.btn-cancel:active {
  transform: translateY(0);
}

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile responsiveness for modal */
@media (max-width: 480px) {
  .logout-modal {
    width: 95%;
    margin: 10px;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-confirm {
    width: 100%;
  }
}

/* Access Denied Styles */
.access-denied {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 48px 24px;
  text-align: center;
  margin: 24px auto;
  max-width: 500px;
  border: 1px solid #fee2e2;
}

.access-denied h3 {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.access-denied h3::before {
  content: "🚫";
  font-size: 28px;
}

.access-denied p {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}