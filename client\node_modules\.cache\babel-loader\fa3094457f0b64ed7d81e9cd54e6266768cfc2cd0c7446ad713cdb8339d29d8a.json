{"ast": null, "code": "var _jsxFileName = \"D:\\\\Thirmaa Office\\\\job_page\\\\client\\\\src\\\\components\\\\Admin\\\\JobsAdmin.jsx\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\n/* eslint-disable no-unused-vars */\n/* eslint-disable no-useless-escape */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faPlus, faEdit, faTrash, faSearch, faExclamationCircle, faCheckCircle, faEye, faFire, faTimes, faBuilding, faImage, faSpinner, faChevronDown, faExclamationTriangle, faSync } from '@fortawesome/free-solid-svg-icons';\nimport '../../css/JobsAdmin.css';\nimport '../../css/JobFormModal.css';\nimport '../../css/shared-delete-dialog.css';\nimport ApiService from '../../services/apiService';\n// Import search-fix.css last to ensure it takes precedence\nimport '../../css/search-fix.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EXPERIENCE_OPTIONS = [\"No Experience Required\", \"Entry Level\", \"Mid Level\", \"Senior Level\", \"Manager\", \"Executive\"];\nconst JobsAdmin = () => {\n  _s();\n  const [jobs, setJobs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [companies, setCompanies] = useState([]);\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\n\n  // Delete confirmation dialog state\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [jobToDelete, setJobToDelete] = useState(null);\n\n  // New job form state\n  // Get today's date in YYYY-MM-DD format for the date inputs\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\n\n  // Format date for display in table\n  const formatDisplayDate = dateString => {\n    if (!dateString || dateString === '-') return '-';\n    try {\n      const date = new Date(dateString);\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    } catch (err) {\n      console.error(\"Error formatting display date:\", err);\n      return dateString;\n    }\n  };\n  const [newJobForm, setNewJobForm] = useState({\n    job_title: '',\n    year: new Date().getFullYear(),\n    start_date: getTodayFormatted(),\n    end_date: '',\n    send_cv_email: '',\n    main_topics: 'Government Jobs',\n    // Set a default valid value from the enum\n    sub_topics: [],\n    job_type: 'Full Time Jobs',\n    // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n    experience_level: 'Entry Level',\n    // New field for experience level\n    min_salary: '',\n    max_salary: '',\n    job_description: '',\n    job_post_image: null,\n    job_post_thumbnail: null,\n    company_name: '',\n    company_logo: null,\n    // UI-only fields (not in database)\n    status: 'Active',\n    hot: false\n  });\n\n  // Image preview states\n  const [jobImagePreview, setJobImagePreview] = useState(null);\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\n  const [logoPreview, setLogoPreview] = useState(null);\n  // State for managing sub-topics\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\n\n  // Predefined list of sub-topics for checkbox selection (fallback)\n  const defaultSubTopics = ['IT-software/DB/QA/WEB/GRAPHICS/GIS', 'IT-SOFTWARE/NETWORKS/SYSTEMS', 'ACCOUNTING/AUDITING/FINANCE', 'BANKING & FINANCE/INSURANCE', 'SALES/MARKETING/MERCHANDISING', 'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS', 'LOGISTICS', 'ENG-MECH/AUTO/ELE', 'MANUFACTURING', 'MEDIA/ADVERT/COMMUNICATION', 'SECURITY', 'EDUCATION', 'SUPERVISION', 'APPAREL/CLOTHING', 'TICKETING/AIRLINE', 'R&D/SCIENCE/RESEARCH', 'AGRICULTURE/ENVIRONMENT'];\n\n  // Initialize available sub-topics\n  useEffect(() => {\n    // For now, use the default sub-topics\n    // In a real implementation, you might fetch these from an API\n    setAvailableSubTopics(defaultSubTopics);\n  }, []);\n\n  // Fetch jobs from backend\n  useEffect(() => {\n    const fetchJobs = async () => {\n      try {\n        setLoading(true);\n        const response = await ApiService.jobs.getAll();\n\n        // Transform backend data to match the format used in frontend\n        // Adding default values for fields that don't exist in the database\n        const formattedJobs = response.data.map(job => {\n          // Determine the best date to use\n          let dateToUse;\n          if (job.created_at) {\n            // Prefer created_at if available\n            dateToUse = job.created_at;\n          } else if (job.start_date) {\n            // Otherwise use start_date\n            dateToUse = job.start_date;\n          } else if (job.posted_date) {\n            // Fall back to posted_date\n            dateToUse = job.posted_date;\n          } else {\n            // If no dates are available, use current date\n            dateToUse = new Date().toISOString();\n          }\n\n          // Compose salary display string\n          let salaryDisplay = '';\n          if (job.min_salary && job.max_salary) {\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n          } else if (job.min_salary) {\n            salaryDisplay = `${job.min_salary}`;\n          } else if (job.max_salary) {\n            salaryDisplay = `${job.max_salary}`;\n          } else {\n            salaryDisplay = '';\n          }\n          return {\n            id: job.job_id,\n            hot: job.hot || false,\n            // Use actual hot value from database\n            company: job.company_name,\n            position: job.job_title,\n            location: job.main_topics,\n            workTime: job.job_type,\n            experience_level: job.experience_level,\n            cv_email: job.send_cv_email,\n            salary: salaryDisplay,\n            status: 'Active',\n            // Default since it doesn't exist in DB\n            datePosted: dateToUse,\n            rawDatePosted: job.start_date || job.posted_date || job.created_at,\n            // Store raw date for debugging\n            views: job.view_count || 0,\n            // Use actual view count if available\n            end_date: job.end_date // Add end_date for auto-delete functionality\n          };\n        });\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n        setError(null);\n      } catch (err) {\n        console.error(\"Error fetching jobs:\", err);\n        setError(\"Failed to load jobs from server\");\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchJobs();\n  }, []);\n\n  // Auto-delete expired jobs (2 weeks after end date)\n  useEffect(() => {\n    const checkAndDeleteExpiredJobs = async () => {\n      try {\n        const currentDate = new Date();\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\n\n        // Get all jobs to check for expired ones\n        const response = await ApiService.jobs.getAll();\n        const allJobs = response.data;\n        const expiredJobs = allJobs.filter(job => {\n          if (!job.end_date) return false; // Skip jobs without end date\n\n          const endDate = new Date(job.end_date);\n          const timeDifference = currentDate.getTime() - endDate.getTime();\n\n          // Check if job is expired by more than 2 weeks\n          return timeDifference > twoWeeksInMs;\n        });\n\n        // Delete expired jobs\n        for (const expiredJob of expiredJobs) {\n          try {\n            await ApiService.jobs.delete(expiredJob.job_id);\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\n          } catch (deleteErr) {\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\n          }\n        }\n\n        // If any jobs were deleted, refresh the jobs list\n        if (expiredJobs.length > 0) {\n          // Refresh jobs list by fetching again\n          const updatedResponse = await ApiService.jobs.getAll();\n          const formattedJobs = updatedResponse.data.map(job => {\n            let dateToUse;\n            if (job.created_at) {\n              dateToUse = job.created_at;\n            } else if (job.start_date) {\n              dateToUse = job.start_date;\n            } else if (job.posted_date) {\n              dateToUse = job.posted_date;\n            } else {\n              dateToUse = new Date().toISOString();\n            }\n            let salaryDisplay = '';\n            if (job.min_salary && job.max_salary) {\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n            } else if (job.min_salary) {\n              salaryDisplay = `${job.min_salary}`;\n            } else if (job.max_salary) {\n              salaryDisplay = `${job.max_salary}`;\n            } else {\n              salaryDisplay = '';\n            }\n            return {\n              id: job.job_id,\n              hot: job.hot || false,\n              company: job.company_name,\n              position: job.job_title,\n              location: job.main_topics,\n              workTime: job.job_type,\n              experience_level: job.experience_level,\n              cv_email: job.send_cv_email,\n              salary: salaryDisplay,\n              status: 'Active',\n              datePosted: dateToUse,\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\n              views: job.view_count || 0,\n              end_date: job.end_date\n            };\n          });\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n          setJobs(sortedJobs);\n        }\n      } catch (err) {\n        console.error(\"Error checking for expired jobs:\", err);\n        // Don't set error state here to avoid disrupting normal operation\n      }\n    };\n\n    // Run the check immediately when component mounts\n    checkAndDeleteExpiredJobs();\n\n    // Set up interval to check every hour (3600000 ms)\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\n\n    // Cleanup interval on component unmount\n    return () => clearInterval(intervalId);\n  }, []);\n\n  // Fetch companies when modal is opened\n  useEffect(() => {\n    if (isModalOpen) {\n      console.log(\"Modal opened, fetching companies...\");\n      fetchCompanies();\n    }\n  }, [isModalOpen]);\n  const fetchCompanies = async () => {\n    try {\n      console.log(\"Fetching companies from API...\");\n      const response = await ApiService.companies.getAll();\n      const companiesData = response.data.data || [];\n      console.log(\"Companies fetched:\", companiesData);\n      setCompanies(companiesData);\n\n      // If we're editing a job and have a company name, select the matching company\n      if (selectedJob && newJobForm.company_name) {\n        const matchingCompany = companiesData.find(company => company.company_name === newJobForm.company_name);\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\n          setLogoPreview(matchingCompany.company_logo_url);\n        }\n      }\n    } catch (err) {\n      console.error('Error fetching companies:', err);\n      // Don't set error state here to avoid disrupting the job form\n    }\n  };\n  const handleSearchChange = e => {\n    setSearchTerm(e.target.value);\n  };\n  const handleEditJob = async job => {\n    try {\n      setLoading(true);\n      // Fetch full job details from backend\n      const response = await ApiService.jobs.getById(job.id);\n      const fullJobDetails = response.data;\n      console.log(\"Job details from API:\", fullJobDetails);\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\n      setSelectedJob(job);\n      // Initialize form with job data - only using fields from the actual database schema\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\n      const formatDate = dateString => {\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\n\n        try {\n          // Parse the date and ensure it's valid\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) {\n            console.warn(\"Invalid date detected:\", dateString);\n            return getTodayFormatted(); // Default to today for invalid dates\n          }\n\n          // Format as YYYY-MM-DD for date input\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n          return `${year}-${month}-${day}`;\n        } catch (err) {\n          console.error(\"Error formatting date:\", err);\n          return getTodayFormatted();\n        }\n      };\n\n      // Set image previews\n      if (fullJobDetails.job_post_image) {\n        setJobImagePreview(fullJobDetails.job_post_image);\n      }\n      if (fullJobDetails.job_post_thumbnail) {\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\n      }\n\n      // Handle company logo preview\n      let companyLogoUrl = null;\n      if (fullJobDetails.company_logo) {\n        setLogoPreview(fullJobDetails.company_logo);\n        companyLogoUrl = fullJobDetails.company_logo;\n      } else if (fullJobDetails.company_logo_url) {\n        setLogoPreview(fullJobDetails.company_logo_url);\n        companyLogoUrl = fullJobDetails.company_logo_url;\n      } else {\n        // Fetch companies to find the logo\n        try {\n          const companiesResponse = await ApiService.companies.getAll();\n          const companiesData = companiesResponse.data.data || [];\n          const matchingCompany = companiesData.find(company => company.company_name === fullJobDetails.company_name);\n          if (matchingCompany && matchingCompany.company_logo_url) {\n            setLogoPreview(matchingCompany.company_logo_url);\n            companyLogoUrl = matchingCompany.company_logo_url;\n          }\n        } catch (err) {\n          console.error(\"Error finding company logo:\", err);\n        }\n      }\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\n      let expLevel = fullJobDetails.experience_level;\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\n        expLevel = \"No Experience Required\";\n      }\n      setNewJobForm({\n        job_title: fullJobDetails.job_title,\n        company_name: fullJobDetails.company_name,\n        year: fullJobDetails.year || new Date().getFullYear(),\n        start_date: formatDate(fullJobDetails.start_date),\n        end_date: formatDate(fullJobDetails.end_date),\n        job_type: fullJobDetails.job_type,\n        experience_level: expLevel,\n        min_salary: fullJobDetails.min_salary || '',\n        max_salary: fullJobDetails.max_salary || '',\n        send_cv_email: fullJobDetails.send_cv_email || '',\n        main_topics: fullJobDetails.main_topics,\n        sub_topics: (() => {\n          try {\n            // Try to parse sub_topics as JSON if it's a string\n            if (!fullJobDetails.sub_topics) return [];\n            if (typeof fullJobDetails.sub_topics === 'string') {\n              // Check if it starts with [ which would indicate a likely JSON array\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\n                return JSON.parse(fullJobDetails.sub_topics);\n              } else {\n                // If it's not a JSON array, treat it as a single item\n                return [fullJobDetails.sub_topics];\n              }\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\n              return fullJobDetails.sub_topics;\n            } else {\n              return [];\n            }\n          } catch (err) {\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\n          }\n        })(),\n        job_description: fullJobDetails.job_description || '',\n        job_post_image: fullJobDetails.job_post_image,\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\n        company_logo: fullJobDetails.company_logo,\n        company_logo_url: companyLogoUrl,\n        // Store the company logo URL\n        // Use default values for UI-only fields that don't exist in the database\n        status: 'Active',\n        hot: fullJobDetails.hot || false // Use actual hot value from database\n      });\n      setIsModalOpen(true);\n      setError(null);\n    } catch (err) {\n      console.error(\"Error fetching job details:\", err);\n      setError(`Failed to load job details for ${job.position}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteJob = async jobId => {\n    try {\n      setLoading(true);\n      await ApiService.jobs.delete(jobId);\n      setJobs(jobs.filter(job => job.id !== jobId));\n      setError(null);\n      setShowDeleteConfirm(false);\n      setJobToDelete(null);\n    } catch (err) {\n      console.error(\"Error deleting job:\", err);\n      setError(\"Failed to delete job\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to show delete confirmation\n  const confirmDelete = job => {\n    setJobToDelete(job);\n    setShowDeleteConfirm(true);\n  };\n\n  // Function to cancel delete\n  const cancelDelete = () => {\n    setShowDeleteConfirm(false);\n    setJobToDelete(null);\n  };\n  const handleToggleStatus = async jobId => {\n    try {\n      setLoading(true);\n      const jobToUpdate = jobs.find(job => job.id === jobId);\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\n\n      // Since we don't have a status column in the database,\n      // we'll just update the local state without sending to the backend\n      // In a real application, you would add the status column to the database\n\n      // Update local state only\n      setJobs(jobs.map(job => job.id === jobId ? {\n        ...job,\n        status: newStatus\n      } : job));\n      setError(null);\n    } catch (err) {\n      console.error(\"Error updating job status:\", err);\n      setError(\"Failed to update job status\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const openNewJobModal = () => {\n    setIsModalOpen(true);\n    setSelectedJob(null);\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setSelectedJob(null);\n    // Reset form\n    setNewJobForm({\n      job_title: '',\n      year: new Date().getFullYear(),\n      start_date: getTodayFormatted(),\n      end_date: '',\n      send_cv_email: '',\n      main_topics: 'Government Jobs',\n      // Ensure we always have a valid value\n      sub_topics: [],\n      job_type: 'Full Time Jobs',\n      // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\n      experience_level: 'Entry Level',\n      min_salary: '',\n      max_salary: '',\n      job_description: '',\n      job_post_image: null,\n      job_post_thumbnail: null,\n      company_name: '',\n      company_logo: null,\n      // UI-only fields (not in database)\n      status: 'Active',\n      hot: false\n    });\n    // Reset previews\n    setJobImagePreview(null);\n    setThumbnailPreview(null);\n    setLogoPreview(null);\n    setShowCompanyDropdown(false);\n  };\n\n  // Basic client-side text sanitization\n  const sanitizeInputText = text => {\n    if (!text) return '';\n    // Modified to preserve Unicode characters in the job title\n    // Only remove control characters and potentially problematic chars\n    return text.replace(/[--]/g, '') // Remove control characters\n    .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\n    .trim();\n  };\n  const handleFormChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n\n    // Apply sanitization to text fields that might contain problematic characters\n    if (name === 'job_title' || name === 'job_description') {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\n      });\n    } else {\n      setNewJobForm({\n        ...newJobForm,\n        [name]: type === 'checkbox' ? checked : value\n      });\n    }\n  };\n  const handleImageUpload = (e, imageType) => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (imageType === 'job_post_image') {\n          setJobImagePreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_image: file\n          });\n        } else if (imageType === 'job_post_thumbnail') {\n          setThumbnailPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            job_post_thumbnail: file\n          });\n        } else if (imageType === 'company_logo') {\n          setLogoPreview(reader.result);\n          setNewJobForm({\n            ...newJobForm,\n            company_logo: file\n          });\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleSelectCompany = company => {\n    console.log(\"Company selected:\", company);\n\n    // Validate company has a name\n    if (!company || !company.company_name) {\n      console.error(\"Invalid company selected\");\n      return;\n    }\n\n    // Set company name in the form\n    setNewJobForm({\n      ...newJobForm,\n      company_name: company.company_name,\n      company_logo: null,\n      // Reset the file input since we're using an existing logo\n      company_logo_url: company.company_logo_url // Store the logo URL\n    });\n\n    // Set logo preview\n    if (company.company_logo_url) {\n      setLogoPreview(company.company_logo_url);\n    } else {\n      setLogoPreview(null);\n    }\n\n    // Close the dropdown\n    setShowCompanyDropdown(false);\n  };\n\n  // Add function to remove logo\n  const handleRemoveLogo = () => {\n    setLogoPreview(null);\n    // If there was a logo file in the form, clear it\n    if (newJobForm.company_logo) {\n      setNewJobForm({\n        ...newJobForm,\n        company_logo: null\n      });\n    }\n  };\n  const handleFormSubmit = async e => {\n    e.preventDefault();\n\n    // Debug the form state\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\n    console.log(\"Original form state:\", newJobForm);\n    console.log(\"Selected job:\", selectedJob);\n\n    // Client-side validation for required fields\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\n      setError(\"Job title is required\");\n      return;\n    }\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\n      setError(\"Company name is required\");\n      return;\n    }\n\n    // Validate email/URL field if provided\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\n      const value = newJobForm.send_cv_email.trim();\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\n        setError(\"Please enter a valid email address or website URL\");\n        return;\n      }\n    }\n\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\n    let experienceLevelToSend = newJobForm.experience_level;\n    if (!experienceLevelToSend) {\n      experienceLevelToSend = \"No Experience Required\";\n    }\n    try {\n      setLoading(true);\n\n      // Create a sanitized copy of the form data to send to the server\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\n      const sanitizedForm = {\n        ...newJobForm,\n        job_title: sanitizedTitle,\n        job_description: sanitizeInputText(newJobForm.job_description),\n        experience_level: experienceLevelToSend,\n        // For new jobs, set today's date as the start_date if not provided\n        start_date: selectedJob ? newJobForm.start_date : newJobForm.start_date || getTodayFormatted()\n      };\n      console.log(\"Sanitized form data:\", sanitizedForm);\n\n      // Prepare form data for API - create a completely fresh FormData object\n      const formData = new FormData();\n      // Add only fields that exist in the database schema, ensuring each field is only added once\n      const dbFields = ['job_title', 'company_name', 'year', 'start_date', 'end_date', 'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level', 'min_salary', 'max_salary', 'job_description', 'hot'];\n\n      // Add text fields one by one to avoid duplicates\n      dbFields.forEach(key => {\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\n          // Special handling for sub_topics\n          if (key === 'sub_topics') {\n            try {\n              if (Array.isArray(sanitizedForm[key])) {\n                // Ensure we're sending a clean array without unexpected characters\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\n                formData.append(key, JSON.stringify(cleanSubTopics));\n              } else {\n                // If it's a string or something else, convert to array\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\n              }\n            } catch (err) {\n              console.error(\"Error formatting sub_topics:\", err);\n              formData.append(key, '[]'); // Fallback to empty array\n            }\n          } else {\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\n            formData.append(key, sanitizedForm[key]);\n          }\n        }\n      });\n\n      // Add file fields if they exist\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\n        formData.append('job_post_image', sanitizedForm.job_post_image);\n      }\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\n      }\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\n        formData.append('company_logo', sanitizedForm.company_logo);\n      }\n\n      // If we have a logo preview from an existing company but no file, add the URL\n      if (logoPreview && !sanitizedForm.company_logo) {\n        formData.append('existing_company_logo_url', logoPreview);\n      }\n\n      // Add company_logo_url if it exists\n      if (sanitizedForm.company_logo_url) {\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\n      }\n\n      // Debug: Log what's being sent in the FormData\n      console.log(\"FormData contents:\");\n      for (let pair of formData.entries()) {\n        console.log(pair[0] + ': ' + pair[1]);\n      }\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\n\n      // Prepare form data for submission\n      if (selectedJob) {\n        // Update existing job\n        try {\n          await ApiService.jobs.update(selectedJob.id, formData);\n          // Successfully updated job\n        } catch (apiError) {\n          // Handle update error silently\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      } else {\n        // Create new job\n        try {\n          await ApiService.jobs.create(formData);\n          // Successfully created job\n        } catch (apiError) {\n          var _apiError$response, _apiError$response2, _apiError$response3;\n          // Log detailed error information\n          console.error(\"API Error Details:\", {\n            message: apiError.message,\n            status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n            statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n            data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data\n          });\n          throw apiError; // Re-throw to be caught by the outer try/catch\n        }\n\n        // Refresh jobs list\n        const jobsResponse = await ApiService.jobs.getAll();\n        const formattedJobs = jobsResponse.data.map(job => ({\n          id: job.job_id,\n          hot: job.hot || false,\n          // Use actual hot value from database\n          company: job.company_name,\n          position: job.job_title,\n          location: job.main_topics,\n          workTime: job.job_type,\n          experience_level: job.experience_level,\n          cv_email: job.send_cv_email,\n          salary: job.min_salary && job.max_salary ? `${job.min_salary} - ${job.max_salary}` : job.min_salary ? `${job.min_salary}` : job.max_salary ? `${job.max_salary}` : '',\n          status: 'Active',\n          // Default since it doesn't exist in DB\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\n          views: job.view_count || 0,\n          // Use actual view count if available\n          end_date: job.end_date // Ensure end_date is included\n        }));\n\n        // Sort jobs by datePosted descending (newest first)\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n        setJobs(sortedJobs);\n      }\n      setError(null);\n      // Close modal\n      closeModal();\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      console.error(\"Error saving job:\", err);\n      // Show more detailed error message if available\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filteredJobs = jobs.filter(job => {\n    const matchesSearch = job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase()) || job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesSearch;\n  });\n\n  // Add this useEffect to handle closing the dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      const dropdownElement = document.querySelector('.company-select-dropdown');\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\n        setShowCompanyDropdown(false);\n      }\n    };\n    if (showCompanyDropdown) {\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [showCompanyDropdown]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"jobs-admin-container\",\n    children: [showDeleteConfirm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"delete-confirm-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"delete-confirm-dialog\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-header\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faExclamationTriangle,\n            className: \"delete-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Confirm Deletion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Are you sure you want to delete this job posting?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: jobToDelete === null || jobToDelete === void 0 ? void 0 : jobToDelete.position\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"This action cannot be undone.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"delete-confirm-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancel-delete-btn\",\n            onClick: cancelDelete,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"confirm-delete-btn\",\n            onClick: () => handleDeleteJob(jobToDelete.id),\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"jobs-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"jobs-title\",\n        children: \"Job Listings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"refresh-button\",\n          onClick: () => {\n            setLoading(true);\n            const fetchJobs = async () => {\n              try {\n                const response = await ApiService.jobs.getAll();\n\n                // Transform backend data to match the format used in frontend\n                const formattedJobs = response.data.map(job => {\n                  // Determine the best date to use\n                  let dateToUse;\n                  if (job.created_at) {\n                    dateToUse = job.created_at;\n                  } else if (job.start_date) {\n                    dateToUse = job.start_date;\n                  } else if (job.posted_date) {\n                    dateToUse = job.posted_date;\n                  } else {\n                    dateToUse = new Date().toISOString();\n                  }\n\n                  // Compose salary display string\n                  let salaryDisplay = '';\n                  if (job.min_salary && job.max_salary) {\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\n                  } else if (job.min_salary) {\n                    salaryDisplay = `${job.min_salary}`;\n                  } else if (job.max_salary) {\n                    salaryDisplay = `${job.max_salary}`;\n                  } else {\n                    salaryDisplay = '';\n                  }\n                  return {\n                    id: job.job_id,\n                    hot: job.hot || false,\n                    company: job.company_name,\n                    position: job.job_title,\n                    location: job.main_topics,\n                    workTime: job.job_type,\n                    experience_level: job.experience_level,\n                    cv_email: job.send_cv_email,\n                    salary: salaryDisplay,\n                    status: 'Active',\n                    datePosted: dateToUse,\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\n                    views: job.view_count || 0,\n                    end_date: job.end_date\n                  };\n                });\n\n                // Sort jobs by datePosted descending (newest first)\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\n                setJobs(sortedJobs);\n                setError(null);\n              } catch (err) {\n                console.error(\"Error fetching jobs:\", err);\n                setError(\"Failed to load jobs from server\");\n              } finally {\n                setLoading(false);\n              }\n            };\n            fetchJobs();\n          },\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSync\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 920,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-job-button\",\n          onClick: openNewJobModal,\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faPlus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Add New Job\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 919,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n        icon: faExclamationCircle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 995,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 996,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 994,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: faSearch,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this), \"          \", /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search by company or position...\",\n            value: searchTerm,\n            onChange: handleSearchChange,\n            className: \"search-input\",\n            style: {\n              paddingLeft: '40px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 82\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1001,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1000,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faSpinner,\n          spin: true,\n          size: \"2x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading jobs...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"jobs-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1028,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Experience Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email / Web URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Salary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Expire Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Posted Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Views\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.length > 0 ? filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [job.hot && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"hot-badge\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faFire,\n                    style: {\n                      marginRight: '4px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1045,\n                    columnNumber: 27\n                  }, this), \"URGENT\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-position\",\n                  title: job.position,\n                  children: job.position && job.position.length > 35 ? job.position.slice(0, 32) + '...' : job.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"job-company\",\n                  children: job.company\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.workTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"experience-level\",\n                  title: job.experience_level,\n                  children: job.experience_level || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"cv-email\",\n                  title: job.cv_email,\n                  children: job.cv_email ? /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: `mailto:${job.cv_email}`,\n                    className: \"email-link\",\n                    children: job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 27\n                  }, this) : 'Not provided'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.salary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.end_date)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDisplayDate(job.datePosted)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"views-container\",\n                  children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                    icon: faEye,\n                    className: \"views-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1078,\n                    columnNumber: 25\n                  }, this), job.views]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button edit-button\",\n                    onClick: () => handleEditJob(job),\n                    title: \"Edit job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faEdit\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1090,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1084,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-button delete-button\",\n                    onClick: () => confirmDelete(job),\n                    title: \"Delete job\",\n                    disabled: loading,\n                    children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faTrash\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"9\",\n                className: \"no-jobs-message\",\n                children: searchTerm ? \"No jobs match your search criteria\" : \"No jobs available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1024,\n          columnNumber: 11\n        }, this)\n      }, void 0, false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1016,\n      columnNumber: 7\n    }, this), isModalOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: e => {\n        // Close modal when clicking outside\n        if (e.target.className === 'modal-overlay') {\n          closeModal();\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: selectedJob ? 'Edit Job' : 'Add New Job'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: closeModal,\n            children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n              icon: faTimes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1130,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleFormSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_title\",\n                  children: [\"Job Title \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1140,\n                    columnNumber: 58\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"job_title\",\n                  name: \"job_title\",\n                  value: newJobForm.job_title,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter job title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1141,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [\"Company \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1152,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1152,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"company-selector\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"company-select-dropdown\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"company-select-button\",\n                      onClick: () => setShowCompanyDropdown(!showCompanyDropdown),\n                      children: [newJobForm.company_name ? newJobForm.company_name : 'Select a company', /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faChevronDown\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 25\n                    }, this), showCompanyDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"company-list-dropdown\",\n                      children: [companies && companies.length > 0 ? companies.map(company => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"company-item\",\n                        onClick: () => {\n                          handleSelectCompany(company);\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"company-item-logo\",\n                          children: company.company_logo_url ? /*#__PURE__*/_jsxDEV(\"img\", {\n                            src: company.company_logo_url,\n                            alt: company.company_name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1177,\n                            columnNumber: 39\n                          }, this) : /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                            icon: faBuilding\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1179,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1175,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: company.company_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1182,\n                          columnNumber: 35\n                        }, this)]\n                      }, company.company_id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1168,\n                        columnNumber: 33\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-companies\",\n                        children: \"No companies available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1186,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"manual-company-entry\",\n                        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                          children: \"Or enter company name manually:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1189,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                          type: \"text\",\n                          placeholder: \"Enter company name\",\n                          value: newJobForm.company_name || '',\n                          onChange: e => {\n                            setNewJobForm({\n                              ...newJobForm,\n                              company_name: e.target.value\n                            });\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1190,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          type: \"button\",\n                          className: \"apply-company-btn\",\n                          onClick: () => setShowCompanyDropdown(false),\n                          children: \"Apply\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1201,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1188,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1165,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1154,\n                    columnNumber: 23\n                  }, this), logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"logo-preview-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: logoPreview,\n                      alt: \"Company logo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1216,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"remove-logo-button\",\n                      onClick: handleRemoveLogo,\n                      children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faTimes\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1222,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1217,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 25\n                  }, this), !logoPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"file\",\n                      id: \"company_logo\",\n                      name: \"company_logo\",\n                      onChange: e => handleImageUpload(e, 'company_logo'),\n                      accept: \"image/*\",\n                      style: {\n                        display: 'none'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1230,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      className: \"file-upload-label\",\n                      onClick: () => document.getElementById('company_logo').click(),\n                      children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                        icon: faImage\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1243,\n                        columnNumber: 29\n                      }, this), \"Upload Company Logo\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1238,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1229,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"year\",\n                  children: \"Year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"year\",\n                  name: \"year\",\n                  value: newJobForm.year,\n                  onChange: handleFormChange,\n                  required: true,\n                  min: \"2000\",\n                  max: \"2100\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 19\n              }, this), \"                  \", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_type\",\n                  children: \"Job Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"job_type\",\n                  name: \"job_type\",\n                  value: newJobForm.job_type,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Full Time Jobs\",\n                    children: \"Full Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Part Time Jobs\",\n                    children: \"Part Time Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Remote Jobs\",\n                    children: \"Remote Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Freelance\",\n                    children: \"Freelance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Temporary\",\n                    children: \"Temporary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1278,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1267,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 43\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"experience_level\",\n                  children: \"Experience Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1282,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"experience_level\",\n                  name: \"experience_level\",\n                  value: newJobForm.experience_level,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: EXPERIENCE_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: option,\n                    children: option\n                  }, option, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1283,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"start_date\",\n                  children: \"Start Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1299,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"start_date\",\n                  name: \"start_date\",\n                  value: newJobForm.start_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1300,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"end_date\",\n                  children: \"End Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  id: \"end_date\",\n                  name: \"end_date\",\n                  value: newJobForm.end_date,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1309,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"send_cv_email\",\n                  children: \"Email/WebURL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"send_cv_email\",\n                  name: \"send_cv_email\",\n                  value: newJobForm.send_cv_email,\n                  onChange: handleFormChange,\n                  placeholder: \"Enter email or website URL for CV submissions (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"min_salary\",\n                  children: \"Minimum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"min_salary\",\n                  name: \"min_salary\",\n                  value: newJobForm.min_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Minimum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"max_salary\",\n                  children: \"Maximum Salary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  id: \"max_salary\",\n                  name: \"max_salary\",\n                  value: newJobForm.max_salary,\n                  onChange: handleFormChange,\n                  placeholder: \"Maximum salary\",\n                  min: \"0\",\n                  step: \"any\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group checkbox-group\",\n                style: {\n                  textAlign: 'left'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"checkbox-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    name: \"hot\",\n                    checked: newJobForm.hot,\n                    onChange: handleFormChange\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1366,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"checkbox-text\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faFire,\n                      className: \"hot-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1373,\n                      columnNumber: 25\n                    }, this), \"Mark as URGENT Job\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1372,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1365,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1364,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"main_topics\",\n                  children: \"Main Topic/Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"main_topics\",\n                  name: \"main_topics\",\n                  value: newJobForm.main_topics,\n                  onChange: handleFormChange,\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Government Jobs\",\n                    children: \"Government Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1390,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Private Jobs\",\n                    children: \"Private Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Foreign Jobs\",\n                    children: \"Foreign Jobs\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1392,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Internships\",\n                    children: \"Internships\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1393,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1380,\n              columnNumber: 17\n            }, this), \"                \", /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sub Topics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1399,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"small-text\",\n                    children: [newJobForm.sub_topics.length, \" selected\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1400,\n                    columnNumber: 3\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    style: {\n                      marginLeft: '16px',\n                      padding: '2px 8px',\n                      fontSize: '0.9em'\n                    },\n                    onClick: () => setNewJobForm(prev => ({\n                      ...prev,\n                      sub_topics: []\n                    })),\n                    children: \"Clear All\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1401,\n                    columnNumber: 3\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1398,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-management-info\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    style: {\n                      color: '#6c757d',\n                      fontStyle: 'italic'\n                    },\n                    children: \"\\uD83D\\uDCA1 Tip: You can manage available sub-topics from the \\\"Sub Topics\\\" section in the sidebar\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1410,\n                    columnNumber: 3\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1409,\n                  columnNumber: 1\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtopics-checkbox-container\",\n                  children: availableSubTopics.map((topic, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `subtopic-${index}`,\n                      checked: newJobForm.sub_topics.includes(topic),\n                      onChange: e => {\n                        const checked = e.target.checked;\n                        setNewJobForm(prev => {\n                          const set = new Set(prev.sub_topics);\n                          if (checked) {\n                            set.add(topic);\n                          } else {\n                            set.delete(topic);\n                          }\n                          return {\n                            ...prev,\n                            sub_topics: Array.from(set)\n                          };\n                        });\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1420,\n                      columnNumber: 1\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `subtopic-${index}`,\n                      children: topic\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1440,\n                      columnNumber: 27\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1416,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1414,\n                  columnNumber: 1\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1397,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1396,\n              columnNumber: 39\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group full-width\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"job_description\",\n                  children: \"Job Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"job_description\",\n                  name: \"job_description\",\n                  rows: \"5\",\n                  value: newJobForm.job_description,\n                  onChange: handleFormChange,\n                  required: true,\n                  placeholder: \"Enter detailed job description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1452,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"form-help-text\",\n                  children: \"Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1461,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1449,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_image\",\n                    onChange: e => handleImageUpload(e, 'job_post_image'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_image\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1479,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1480,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 21\n                }, this), jobImagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: jobImagePreview,\n                    alt: \"Job Post\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1485,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1484,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Job Post Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"file-upload-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    id: \"job_post_thumbnail\",\n                    onChange: e => handleImageUpload(e, 'job_post_thumbnail'),\n                    accept: \"image/*\",\n                    className: \"file-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1492,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"job_post_thumbnail\",\n                    className: \"file-upload-label\",\n                    children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                      icon: faImage\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1500,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Choose Thumbnail\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1501,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1499,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1491,\n                  columnNumber: 21\n                }, this), thumbnailPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"thumbnail-preview\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: thumbnailPreview,\n                    alt: \"Thumbnail\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1506,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1505,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1467,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"cancel-button\",\n                onClick: closeModal,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-button\",\n                children: selectedJob ? 'Update Job' : 'Create Job'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1518,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1129,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1123,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 885,\n    columnNumber: 5\n  }, this);\n};\n_s(JobsAdmin, \"2+q47AM3xfyC1OJ5T9j1NTxN2NE=\");\n_c = JobsAdmin;\nexport default JobsAdmin;\nvar _c;\n$RefreshReg$(_c, \"JobsAdmin\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FontAwesomeIcon", "faPlus", "faEdit", "faTrash", "faSearch", "faExclamationCircle", "faCheckCircle", "faEye", "faFire", "faTimes", "faBuilding", "faImage", "faSpinner", "faChevronDown", "faExclamationTriangle", "faSync", "ApiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EXPERIENCE_OPTIONS", "JobsAdmin", "_s", "jobs", "setJobs", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "isModalOpen", "setIsModalOpen", "companies", "setCompanies", "showCompanyDropdown", "setShowCompanyDropdown", "showDeleteConfirm", "setShowDeleteConfirm", "jobToDelete", "setJobToDelete", "getTodayFormatted", "Date", "toISOString", "split", "formatDisplayDate", "dateString", "date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "err", "console", "newJobForm", "setNewJobForm", "job_title", "getFullYear", "start_date", "end_date", "send_cv_email", "main_topics", "sub_topics", "job_type", "experience_level", "min_salary", "max_salary", "job_description", "job_post_image", "job_post_thumbnail", "company_name", "company_logo", "status", "hot", "jobImagePreview", "setJobImagePreview", "thumbnailPreview", "setThumbnailPreview", "logoPreview", "setLogoPreview", "availableSubTopics", "setAvailableSubTopics", "defaultSubTopics", "fetchJobs", "response", "getAll", "formattedJobs", "data", "map", "job", "dateToUse", "created_at", "posted_date", "salaryDisplay", "id", "job_id", "company", "position", "location", "workTime", "cv_email", "salary", "datePosted", "rawDatePosted", "views", "view_count", "sortedJobs", "sort", "a", "b", "checkAndDeleteExpiredJobs", "currentDate", "twoWeeksInMs", "allJobs", "expiredJobs", "filter", "endDate", "timeDifference", "<PERSON><PERSON><PERSON>", "delete", "log", "deleteErr", "length", "updatedResponse", "intervalId", "setInterval", "clearInterval", "fetchCompanies", "companiesData", "matchingCompany", "find", "company_logo_url", "handleSearchChange", "e", "target", "value", "handleEditJob", "getById", "fullJobDetails", "formatDate", "warn", "String", "getMonth", "padStart", "getDate", "companyLogoUrl", "companiesResponse", "expLevel", "includes", "trim", "startsWith", "JSON", "parse", "Array", "isArray", "handleDeleteJob", "jobId", "confirmDelete", "cancelDelete", "handleToggleStatus", "jobToUpdate", "newStatus", "openNewJobModal", "closeModal", "sanitizeInputText", "text", "replace", "handleFormChange", "name", "type", "checked", "handleImageUpload", "imageType", "file", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSelectCompany", "handleRemoveLogo", "handleFormSubmit", "preventDefault", "emailRegex", "urlRegex", "test", "experienceLevelToSend", "sanitizedTitle", "sanitizedForm", "formData", "FormData", "dbFields", "for<PERSON>ach", "key", "undefined", "cleanSubTopics", "topic", "append", "stringify", "File", "pair", "entries", "get", "update", "apiError", "jobsResponse", "create", "_apiError$response", "_apiError$response2", "_apiError$response3", "message", "statusText", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "errorMessage", "filteredJobs", "matchesSearch", "toLowerCase", "handleClickOutside", "event", "dropdownElement", "document", "querySelector", "contains", "addEventListener", "removeEventListener", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "style", "paddingLeft", "spin", "size", "marginRight", "title", "slice", "href", "display", "colSpan", "onSubmit", "htmlFor", "color", "required", "src", "alt", "company_id", "accept", "getElementById", "click", "min", "max", "option", "step", "textAlign", "marginLeft", "padding", "fontSize", "prev", "fontStyle", "index", "set", "Set", "add", "from", "rows", "_c", "$RefreshReg$"], "sources": ["D:/Thirmaa Office/job_page/client/src/components/Admin/JobsAdmin.jsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n/* eslint-disable no-unused-vars */\r\n/* eslint-disable no-useless-escape */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n  faPlus,\r\n  faEdit,\r\n  faTrash,\r\n  faSearch,\r\n  faExclamationCircle,\r\n  faCheckCircle,\r\n  faEye,\r\n  faFire,\r\n  faTimes,\r\n  faBuilding,\r\n  faImage,\r\n  faSpinner,\r\n  faChevronDown,\r\n  faExclamationTriangle,\r\n  faSync\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport '../../css/JobsAdmin.css';\r\nimport '../../css/JobFormModal.css';\r\nimport '../../css/shared-delete-dialog.css';\r\nimport ApiService from '../../services/apiService';\r\n// Import search-fix.css last to ensure it takes precedence\r\nimport '../../css/search-fix.css';\r\n\r\nconst EXPERIENCE_OPTIONS = [\r\n  \"No Experience Required\",\r\n  \"Entry Level\",\r\n  \"Mid Level\",\r\n  \"Senior Level\",\r\n  \"Manager\",\r\n  \"Executive\"\r\n];\r\n\r\nconst JobsAdmin = () => {\r\n  const [jobs, setJobs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedJob, setSelectedJob] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);\r\n  \r\n  // Delete confirmation dialog state\r\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\r\n  const [jobToDelete, setJobToDelete] = useState(null);\r\n  \r\n  // New job form state\r\n  // Get today's date in YYYY-MM-DD format for the date inputs\r\n  const getTodayFormatted = () => new Date().toISOString().split('T')[0];\r\n  \r\n  // Format date for display in table\r\n  const formatDisplayDate = (dateString) => {\r\n    if (!dateString || dateString === '-') return '-';\r\n    try {\r\n      const date = new Date(dateString);\r\n      if (isNaN(date.getTime())) return dateString; // If invalid date, return as is\r\n      return date.toLocaleDateString('en-US', { \r\n        year: 'numeric', \r\n        month: 'short', \r\n        day: 'numeric' \r\n      });\r\n    } catch (err) {\r\n      console.error(\"Error formatting display date:\", err);\r\n      return dateString;\r\n    }\r\n  };\r\n    const [newJobForm, setNewJobForm] = useState({\r\n    job_title: '',\r\n    year: new Date().getFullYear(),\r\n    start_date: getTodayFormatted(),\r\n    end_date: '',\r\n    send_cv_email: '',\r\n    main_topics: 'Government Jobs', // Set a default valid value from the enum\r\n    sub_topics: [],\r\n    job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n    experience_level: 'Entry Level', // New field for experience level\r\n    min_salary: '',\r\n    max_salary: '',\r\n    job_description: '',\r\n    job_post_image: null,\r\n    job_post_thumbnail: null,\r\n    company_name: '',\r\n    company_logo: null,\r\n    // UI-only fields (not in database)\r\n    status: 'Active',\r\n    hot: false\r\n  });\r\n  \r\n  // Image preview states\r\n  const [jobImagePreview, setJobImagePreview] = useState(null);\r\n  const [thumbnailPreview, setThumbnailPreview] = useState(null);\r\n  const [logoPreview, setLogoPreview] = useState(null);\r\n  // State for managing sub-topics\r\n  const [availableSubTopics, setAvailableSubTopics] = useState([]);\r\n\r\n  // Predefined list of sub-topics for checkbox selection (fallback)\r\n  const defaultSubTopics = [\r\n    'IT-software/DB/QA/WEB/GRAPHICS/GIS',\r\n    'IT-SOFTWARE/NETWORKS/SYSTEMS',\r\n    'ACCOUNTING/AUDITING/FINANCE',\r\n    'BANKING & FINANCE/INSURANCE',\r\n    'SALES/MARKETING/MERCHANDISING',\r\n    'TELECOMS-CUSTOMER RELATIONS/PUBLIC RELATIONS',\r\n    'LOGISTICS',\r\n    'ENG-MECH/AUTO/ELE',\r\n    'MANUFACTURING',\r\n    'MEDIA/ADVERT/COMMUNICATION',\r\n    'SECURITY',\r\n    'EDUCATION',\r\n    'SUPERVISION',\r\n    'APPAREL/CLOTHING',\r\n    'TICKETING/AIRLINE',\r\n    'R&D/SCIENCE/RESEARCH',\r\n    'AGRICULTURE/ENVIRONMENT'\r\n  ];\r\n\r\n  // Initialize available sub-topics\r\n  useEffect(() => {\r\n    // For now, use the default sub-topics\r\n    // In a real implementation, you might fetch these from an API\r\n    setAvailableSubTopics(defaultSubTopics);\r\n  }, []);\r\n\r\n  // Fetch jobs from backend\r\n  useEffect(() => {\r\n    const fetchJobs = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await ApiService.jobs.getAll();\r\n        \r\n        // Transform backend data to match the format used in frontend\r\n        // Adding default values for fields that don't exist in the database\r\n        const formattedJobs = response.data.map(job => {\r\n          // Determine the best date to use\r\n          let dateToUse;\r\n          if (job.created_at) {\r\n            // Prefer created_at if available\r\n            dateToUse = job.created_at;\r\n          } else if (job.start_date) {\r\n            // Otherwise use start_date\r\n            dateToUse = job.start_date;\r\n          } else if (job.posted_date) {\r\n            // Fall back to posted_date\r\n            dateToUse = job.posted_date;\r\n          } else {\r\n            // If no dates are available, use current date\r\n            dateToUse = new Date().toISOString();\r\n          }\r\n\r\n          // Compose salary display string\r\n          let salaryDisplay = '';\r\n          if (job.min_salary && job.max_salary) {\r\n            salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n          } else if (job.min_salary) {\r\n            salaryDisplay = `${job.min_salary}`;\r\n          } else if (job.max_salary) {\r\n            salaryDisplay = `${job.max_salary}`;\r\n          } else {\r\n            salaryDisplay = '';\r\n          }\r\n            return {\r\n            id: job.job_id,\r\n            hot: job.hot || false, // Use actual hot value from database\r\n            company: job.company_name,\r\n            position: job.job_title,\r\n            location: job.main_topics,\r\n            workTime: job.job_type,\r\n            experience_level: job.experience_level,\r\n            cv_email: job.send_cv_email,\r\n            salary: salaryDisplay,\r\n            status: 'Active', // Default since it doesn't exist in DB\r\n            datePosted: dateToUse,\r\n            rawDatePosted: job.start_date || job.posted_date || job.created_at, // Store raw date for debugging\r\n            views: job.view_count || 0, // Use actual view count if available\r\n            end_date: job.end_date // Add end_date for auto-delete functionality\r\n          };\r\n        });\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error(\"Error fetching jobs:\", err);\r\n        setError(\"Failed to load jobs from server\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchJobs();\r\n  }, []);\r\n\r\n  // Auto-delete expired jobs (2 weeks after end date)\r\n  useEffect(() => {\r\n    const checkAndDeleteExpiredJobs = async () => {\r\n      try {\r\n        const currentDate = new Date();\r\n        const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds\r\n        \r\n        // Get all jobs to check for expired ones\r\n        const response = await ApiService.jobs.getAll();\r\n        const allJobs = response.data;\r\n        \r\n        const expiredJobs = allJobs.filter(job => {\r\n          if (!job.end_date) return false; // Skip jobs without end date\r\n          \r\n          const endDate = new Date(job.end_date);\r\n          const timeDifference = currentDate.getTime() - endDate.getTime();\r\n          \r\n          // Check if job is expired by more than 2 weeks\r\n          return timeDifference > twoWeeksInMs;\r\n        });\r\n        \r\n        // Delete expired jobs\r\n        for (const expiredJob of expiredJobs) {\r\n          try {\r\n            await ApiService.jobs.delete(expiredJob.job_id);\r\n            console.log(`Auto-deleted expired job: ${expiredJob.job_title} (ID: ${expiredJob.job_id})`);\r\n          } catch (deleteErr) {\r\n            console.error(`Failed to auto-delete job ${expiredJob.job_id}:`, deleteErr);\r\n          }\r\n        }\r\n        \r\n        // If any jobs were deleted, refresh the jobs list\r\n        if (expiredJobs.length > 0) {\r\n          // Refresh jobs list by fetching again\r\n          const updatedResponse = await ApiService.jobs.getAll();\r\n          const formattedJobs = updatedResponse.data.map(job => {\r\n            let dateToUse;\r\n            if (job.created_at) {\r\n              dateToUse = job.created_at;\r\n            } else if (job.start_date) {\r\n              dateToUse = job.start_date;\r\n            } else if (job.posted_date) {\r\n              dateToUse = job.posted_date;\r\n            } else {\r\n              dateToUse = new Date().toISOString();\r\n            }\r\n\r\n            let salaryDisplay = '';\r\n            if (job.min_salary && job.max_salary) {\r\n              salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n            } else if (job.min_salary) {\r\n              salaryDisplay = `${job.min_salary}`;\r\n            } else if (job.max_salary) {\r\n              salaryDisplay = `${job.max_salary}`;\r\n            } else {\r\n              salaryDisplay = '';\r\n            }\r\n            \r\n            return {\r\n              id: job.job_id,\r\n              hot: job.hot || false,\r\n              company: job.company_name,\r\n              position: job.job_title,\r\n              location: job.main_topics,\r\n              workTime: job.job_type,\r\n              experience_level: job.experience_level,\r\n              cv_email: job.send_cv_email,\r\n              salary: salaryDisplay,\r\n              status: 'Active',\r\n              datePosted: dateToUse,\r\n              rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n              views: job.view_count || 0,\r\n              end_date: job.end_date\r\n            };\r\n          });\r\n          \r\n          const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n          setJobs(sortedJobs);\r\n        }\r\n        \r\n      } catch (err) {\r\n        console.error(\"Error checking for expired jobs:\", err);\r\n        // Don't set error state here to avoid disrupting normal operation\r\n      }\r\n    };\r\n\r\n    // Run the check immediately when component mounts\r\n    checkAndDeleteExpiredJobs();\r\n    \r\n    // Set up interval to check every hour (3600000 ms)\r\n    const intervalId = setInterval(checkAndDeleteExpiredJobs, 3600000);\r\n    \r\n    // Cleanup interval on component unmount\r\n    return () => clearInterval(intervalId);\r\n  }, []);\r\n\r\n  // Fetch companies when modal is opened\r\n  useEffect(() => {\r\n    if (isModalOpen) {\r\n      console.log(\"Modal opened, fetching companies...\");\r\n      fetchCompanies();\r\n    }\r\n  }, [isModalOpen]);\r\n\r\n  const fetchCompanies = async () => {\r\n    try {\r\n      console.log(\"Fetching companies from API...\");\r\n      const response = await ApiService.companies.getAll();\r\n      const companiesData = response.data.data || [];\r\n      console.log(\"Companies fetched:\", companiesData);\r\n      setCompanies(companiesData);\r\n      \r\n      // If we're editing a job and have a company name, select the matching company\r\n      if (selectedJob && newJobForm.company_name) {\r\n        const matchingCompany = companiesData.find(\r\n          company => company.company_name === newJobForm.company_name\r\n        );\r\n        \r\n        if (matchingCompany && matchingCompany.company_logo_url && !logoPreview) {\r\n          console.log(\"Setting logo preview for matching company:\", matchingCompany.company_name);\r\n          setLogoPreview(matchingCompany.company_logo_url);\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching companies:', err);\r\n      // Don't set error state here to avoid disrupting the job form\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  const handleEditJob = async (job) => {\r\n    try {\r\n      setLoading(true);\r\n      // Fetch full job details from backend\r\n      const response = await ApiService.jobs.getById(job.id);\r\n      const fullJobDetails = response.data;\r\n      \r\n      console.log(\"Job details from API:\", fullJobDetails);\r\n      console.log(\"Job title from API:\", fullJobDetails.job_title);\r\n      \r\n      setSelectedJob(job);\r\n      // Initialize form with job data - only using fields from the actual database schema\r\n      // Format dates for the form (YYYY-MM-DD format required by date inputs)\r\n      const formatDate = (dateString) => {\r\n        if (!dateString) return getTodayFormatted(); // Default to today if no date provided\r\n        \r\n        try {\r\n          // Parse the date and ensure it's valid\r\n          const date = new Date(dateString);\r\n          if (isNaN(date.getTime())) {\r\n            console.warn(\"Invalid date detected:\", dateString);\r\n            return getTodayFormatted(); // Default to today for invalid dates\r\n          }\r\n          \r\n          // Format as YYYY-MM-DD for date input\r\n          const year = date.getFullYear();\r\n          const month = String(date.getMonth() + 1).padStart(2, '0');\r\n          const day = String(date.getDate()).padStart(2, '0');\r\n          return `${year}-${month}-${day}`;\r\n        } catch (err) {\r\n          console.error(\"Error formatting date:\", err);\r\n          return getTodayFormatted();\r\n        }\r\n      };\r\n      \r\n      // Set image previews\r\n      if (fullJobDetails.job_post_image) {\r\n        setJobImagePreview(fullJobDetails.job_post_image);\r\n      }\r\n      \r\n      if (fullJobDetails.job_post_thumbnail) {\r\n        setThumbnailPreview(fullJobDetails.job_post_thumbnail);\r\n      }\r\n      \r\n      // Handle company logo preview\r\n      let companyLogoUrl = null;\r\n      if (fullJobDetails.company_logo) {\r\n        setLogoPreview(fullJobDetails.company_logo);\r\n        companyLogoUrl = fullJobDetails.company_logo;\r\n      } else if (fullJobDetails.company_logo_url) {\r\n        setLogoPreview(fullJobDetails.company_logo_url);\r\n        companyLogoUrl = fullJobDetails.company_logo_url;\r\n      } else {\r\n        // Fetch companies to find the logo\r\n        try {\r\n          const companiesResponse = await ApiService.companies.getAll();\r\n          const companiesData = companiesResponse.data.data || [];\r\n          \r\n          const matchingCompany = companiesData.find(\r\n            company => company.company_name === fullJobDetails.company_name\r\n          );\r\n          \r\n          if (matchingCompany && matchingCompany.company_logo_url) {\r\n            setLogoPreview(matchingCompany.company_logo_url);\r\n            companyLogoUrl = matchingCompany.company_logo_url;\r\n          }\r\n        } catch (err) {\r\n          console.error(\"Error finding company logo:\", err);\r\n        }\r\n      }\r\n      // Fix: If experience_level is not in EXPERIENCE_OPTIONS, set to \"No Experience Required\"\r\n      let expLevel = fullJobDetails.experience_level;\r\n      if (!EXPERIENCE_OPTIONS.includes(expLevel)) {\r\n        expLevel = \"No Experience Required\";\r\n      }\r\n      setNewJobForm({\r\n        job_title: fullJobDetails.job_title,\r\n        company_name: fullJobDetails.company_name,\r\n        year: fullJobDetails.year || new Date().getFullYear(),\r\n        start_date: formatDate(fullJobDetails.start_date),\r\n        end_date: formatDate(fullJobDetails.end_date),\r\n        job_type: fullJobDetails.job_type,\r\n        experience_level: expLevel,\r\n        min_salary: fullJobDetails.min_salary || '',\r\n        max_salary: fullJobDetails.max_salary || '',\r\n        send_cv_email: fullJobDetails.send_cv_email || '',\r\n        main_topics: fullJobDetails.main_topics,\r\n        sub_topics: (() => {\r\n          try {\r\n            // Try to parse sub_topics as JSON if it's a string\r\n            if (!fullJobDetails.sub_topics) return [];\r\n            \r\n            if (typeof fullJobDetails.sub_topics === 'string') {\r\n              // Check if it starts with [ which would indicate a likely JSON array\r\n              if (fullJobDetails.sub_topics.trim().startsWith('[')) {\r\n                return JSON.parse(fullJobDetails.sub_topics);\r\n              } else {\r\n                // If it's not a JSON array, treat it as a single item\r\n                return [fullJobDetails.sub_topics];\r\n              }\r\n            } else if (Array.isArray(fullJobDetails.sub_topics)) {\r\n              return fullJobDetails.sub_topics;\r\n            } else {\r\n              return [];\r\n            }\r\n          } catch (err) {\r\n            console.warn(\"Error parsing sub_topics, using as single item:\", err);\r\n            return fullJobDetails.sub_topics ? [fullJobDetails.sub_topics] : [];\r\n          }\r\n        })(),\r\n        job_description: fullJobDetails.job_description || '',\r\n        job_post_image: fullJobDetails.job_post_image,\r\n        job_post_thumbnail: fullJobDetails.job_post_thumbnail,\r\n        company_logo: fullJobDetails.company_logo,\r\n        company_logo_url: companyLogoUrl, // Store the company logo URL\r\n        // Use default values for UI-only fields that don't exist in the database\r\n        status: 'Active',\r\n        hot: fullJobDetails.hot || false // Use actual hot value from database\r\n      });\r\n      \r\n      setIsModalOpen(true);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error fetching job details:\", err);\r\n      setError(`Failed to load job details for ${job.position}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteJob = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      await ApiService.jobs.delete(jobId);\r\n      setJobs(jobs.filter(job => job.id !== jobId));\r\n      setError(null);\r\n      setShowDeleteConfirm(false);\r\n      setJobToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting job:\", err);\r\n      setError(\"Failed to delete job\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to show delete confirmation\r\n  const confirmDelete = (job) => {\r\n    setJobToDelete(job);\r\n    setShowDeleteConfirm(true);\r\n  };\r\n\r\n  // Function to cancel delete\r\n  const cancelDelete = () => {\r\n    setShowDeleteConfirm(false);\r\n    setJobToDelete(null);\r\n  };\r\n\r\n  const handleToggleStatus = async (jobId) => {\r\n    try {\r\n      setLoading(true);\r\n      const jobToUpdate = jobs.find(job => job.id === jobId);\r\n      const newStatus = jobToUpdate.status === 'Active' ? 'Inactive' : 'Active';\r\n      \r\n      // Since we don't have a status column in the database,\r\n      // we'll just update the local state without sending to the backend\r\n      // In a real application, you would add the status column to the database\r\n      \r\n      // Update local state only\r\n      setJobs(jobs.map(job => \r\n        job.id === jobId \r\n          ? {...job, status: newStatus} \r\n          : job\r\n      ));\r\n      \r\n      setError(null);\r\n    } catch (err) {\r\n      console.error(\"Error updating job status:\", err);\r\n      setError(\"Failed to update job status\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n    const openNewJobModal = () => {\r\n    setIsModalOpen(true);\r\n    setSelectedJob(null);\r\n  };  \r\n    const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setSelectedJob(null);\r\n    // Reset form\r\n    setNewJobForm({\r\n      job_title: '',\r\n      year: new Date().getFullYear(),\r\n      start_date: getTodayFormatted(),\r\n      end_date: '',\r\n      send_cv_email: '',\r\n      main_topics: 'Government Jobs', // Ensure we always have a valid value\r\n      sub_topics: [],\r\n      job_type: 'Full Time Jobs', // Enum: 'Full Time Jobs','Part Time Jobs','Remote Jobs','Freelance','Temporary'\r\n      experience_level: 'Entry Level',\r\n      min_salary: '',\r\n      max_salary: '',\r\n      job_description: '',\r\n      job_post_image: null,\r\n      job_post_thumbnail: null,\r\n      company_name: '',\r\n      company_logo: null,\r\n      // UI-only fields (not in database)\r\n      status: 'Active',\r\n      hot: false\r\n    });\r\n    // Reset previews\r\n    setJobImagePreview(null);\r\n    setThumbnailPreview(null);\r\n    setLogoPreview(null);\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Basic client-side text sanitization\r\n  const sanitizeInputText = (text) => {\r\n    if (!text) return '';\r\n    // Modified to preserve Unicode characters in the job title\r\n    // Only remove control characters and potentially problematic chars\r\n    return text\r\n      .replace(/[--]/g, '') // Remove control characters\r\n      .replace(/[&<>\"'`=\\/]/g, '') // Remove potentially harmful characters\r\n      .trim();\r\n  };\r\n  \r\n  const handleFormChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    \r\n    // Apply sanitization to text fields that might contain problematic characters\r\n    if (name === 'job_title' || name === 'job_description') {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        [name]: type === 'checkbox' ? checked : value // Don't sanitize on input to preserve user experience\r\n      });\r\n    } else {\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    });\r\n    }\r\n  };\r\n  \r\n  const handleImageUpload = (e, imageType) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        if (imageType === 'job_post_image') {\r\n          setJobImagePreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_image: file });\r\n        } else if (imageType === 'job_post_thumbnail') {\r\n          setThumbnailPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, job_post_thumbnail: file });\r\n        } else if (imageType === 'company_logo') {\r\n          setLogoPreview(reader.result);\r\n          setNewJobForm({ ...newJobForm, company_logo: file });\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleSelectCompany = (company) => {\r\n    console.log(\"Company selected:\", company);\r\n    \r\n    // Validate company has a name\r\n    if (!company || !company.company_name) {\r\n      console.error(\"Invalid company selected\");\r\n      return;\r\n    }\r\n    \r\n    // Set company name in the form\r\n    setNewJobForm({\r\n      ...newJobForm,\r\n      company_name: company.company_name,\r\n      company_logo: null, // Reset the file input since we're using an existing logo\r\n      company_logo_url: company.company_logo_url // Store the logo URL\r\n    });\r\n    \r\n    // Set logo preview\r\n    if (company.company_logo_url) {\r\n      setLogoPreview(company.company_logo_url);\r\n    } else {\r\n      setLogoPreview(null);\r\n    }\r\n    \r\n    // Close the dropdown\r\n    setShowCompanyDropdown(false);\r\n  };\r\n  \r\n  // Add function to remove logo\r\n  const handleRemoveLogo = () => {\r\n    setLogoPreview(null);\r\n    // If there was a logo file in the form, clear it\r\n    if (newJobForm.company_logo) {\r\n      setNewJobForm({\r\n        ...newJobForm,\r\n        company_logo: null\r\n      });\r\n    }\r\n  };\r\n  \r\n  const handleFormSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Debug the form state\r\n    console.log(\"=== DEBUG: FORM SUBMISSION START ===\");\r\n    console.log(\"Original form state:\", newJobForm);\r\n    console.log(\"Selected job:\", selectedJob);\r\n    \r\n    // Client-side validation for required fields\r\n    if (!newJobForm.job_title || !newJobForm.job_title.trim()) {\r\n      setError(\"Job title is required\");\r\n      return;\r\n    }\r\n    \r\n    if (!newJobForm.company_name || !newJobForm.company_name.trim()) {\r\n      setError(\"Company name is required\");\r\n      return;\r\n    }\r\n\r\n    // Validate email/URL field if provided\r\n    if (newJobForm.send_cv_email && newJobForm.send_cv_email.trim()) {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      const urlRegex = /^(https?:\\/\\/)?(www\\.)?[a-zA-Z0-9-]+(\\.[a-zA-Z]{2,})+(\\/.*)?$/;\r\n      \r\n      const value = newJobForm.send_cv_email.trim();\r\n      if (!emailRegex.test(value) && !urlRegex.test(value)) {\r\n        setError(\"Please enter a valid email address or website URL\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Fix: If experience_level is empty, set to \"No Experience Required\"\r\n    let experienceLevelToSend = newJobForm.experience_level;\r\n    if (!experienceLevelToSend) {\r\n      experienceLevelToSend = \"No Experience Required\";\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Create a sanitized copy of the form data to send to the server\r\n      console.log(\"Job title before sanitization:\", newJobForm.job_title);\r\n      const sanitizedTitle = sanitizeInputText(newJobForm.job_title);\r\n      console.log(\"Job title after sanitization:\", sanitizedTitle);\r\n      \r\n      const sanitizedForm = {\r\n        ...newJobForm,\r\n        job_title: sanitizedTitle,\r\n        job_description: sanitizeInputText(newJobForm.job_description),\r\n        experience_level: experienceLevelToSend,\r\n        // For new jobs, set today's date as the start_date if not provided\r\n        start_date: selectedJob ? newJobForm.start_date : (newJobForm.start_date || getTodayFormatted())\r\n      };\r\n      \r\n      console.log(\"Sanitized form data:\", sanitizedForm);\r\n      \r\n      // Prepare form data for API - create a completely fresh FormData object\r\n      const formData = new FormData();\r\n        // Add only fields that exist in the database schema, ensuring each field is only added once\r\n      const dbFields = [\r\n        'job_title', 'company_name', 'year', 'start_date', 'end_date',\r\n        'send_cv_email', 'main_topics', 'sub_topics', 'job_type', 'experience_level',\r\n        'min_salary', 'max_salary', 'job_description', 'hot'\r\n      ];\r\n      \r\n      // Add text fields one by one to avoid duplicates\r\n      dbFields.forEach(key => {\r\n        if (sanitizedForm[key] !== undefined && sanitizedForm[key] !== null) {\r\n          // Special handling for sub_topics\r\n          if (key === 'sub_topics') {\r\n            try {\r\n              if (Array.isArray(sanitizedForm[key])) {\r\n                // Ensure we're sending a clean array without unexpected characters\r\n                const cleanSubTopics = sanitizedForm[key].map(topic => String(topic).trim());\r\n                formData.append(key, JSON.stringify(cleanSubTopics));\r\n              } else {\r\n                // If it's a string or something else, convert to array\r\n                formData.append(key, JSON.stringify([String(sanitizedForm[key])]));\r\n              }\r\n            } catch (err) {\r\n              console.error(\"Error formatting sub_topics:\", err);\r\n              formData.append(key, '[]'); // Fallback to empty array\r\n            }\r\n          } else {\r\n            console.log(`Adding form field: ${key} = ${sanitizedForm[key]}`);\r\n            formData.append(key, sanitizedForm[key]);\r\n          }\r\n        }\r\n      });\r\n      \r\n      // Add file fields if they exist\r\n      if (sanitizedForm.job_post_image && sanitizedForm.job_post_image instanceof File) {\r\n        formData.append('job_post_image', sanitizedForm.job_post_image);\r\n      }\r\n      \r\n      if (sanitizedForm.job_post_thumbnail && sanitizedForm.job_post_thumbnail instanceof File) {\r\n        formData.append('job_post_thumbnail', sanitizedForm.job_post_thumbnail);\r\n      }      \r\n      \r\n      if (sanitizedForm.company_logo && sanitizedForm.company_logo instanceof File) {\r\n        formData.append('company_logo', sanitizedForm.company_logo);\r\n      }\r\n      \r\n      // If we have a logo preview from an existing company but no file, add the URL\r\n      if (logoPreview && !sanitizedForm.company_logo) {\r\n        formData.append('existing_company_logo_url', logoPreview);\r\n      }\r\n      \r\n      // Add company_logo_url if it exists\r\n      if (sanitizedForm.company_logo_url) {\r\n        formData.append('company_logo_url', sanitizedForm.company_logo_url);\r\n      }\r\n      \r\n      // Debug: Log what's being sent in the FormData\r\n      console.log(\"FormData contents:\");\r\n      for (let pair of formData.entries()) {\r\n        console.log(pair[0] + ': ' + pair[1]);\r\n      }\r\n      console.log(\"Job title being submitted:\", formData.get('job_title'));\r\n      \r\n      // Prepare form data for submission\r\n      if (selectedJob) {\r\n        // Update existing job\r\n        try {\r\n          await ApiService.jobs.update(selectedJob.id, formData);\r\n          // Successfully updated job\r\n        } catch (apiError) {\r\n          // Handle update error silently\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      } else {\r\n        // Create new job\r\n        try {\r\n          await ApiService.jobs.create(formData);\r\n          // Successfully created job\r\n        } catch (apiError) {\r\n          // Log detailed error information\r\n          console.error(\"API Error Details:\", {\r\n            message: apiError.message,\r\n            status: apiError.response?.status,\r\n            statusText: apiError.response?.statusText,\r\n            data: apiError.response?.data\r\n          });\r\n          throw apiError; // Re-throw to be caught by the outer try/catch\r\n        }\r\n        \r\n        // Refresh jobs list\r\n        const jobsResponse = await ApiService.jobs.getAll();\r\n        const formattedJobs = jobsResponse.data.map(job => ({\r\n          id: job.job_id,\r\n          hot: job.hot || false, // Use actual hot value from database\r\n          company: job.company_name,\r\n          position: job.job_title,\r\n          location: job.main_topics,\r\n          workTime: job.job_type,\r\n          experience_level: job.experience_level,\r\n          cv_email: job.send_cv_email,\r\n          salary: (job.min_salary && job.max_salary)\r\n            ? `${job.min_salary} - ${job.max_salary}`\r\n            : job.min_salary\r\n              ? `${job.min_salary}`\r\n              : job.max_salary\r\n                ? `${job.max_salary}`\r\n                : '',\r\n          status: 'Active', // Default since it doesn't exist in DB\r\n          datePosted: job.start_date ? new Date(job.start_date).toISOString().split('T')[0] : \r\n                    job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : '-',\r\n          views: job.view_count || 0, // Use actual view count if available\r\n          end_date: job.end_date // Ensure end_date is included\r\n        }));\r\n        \r\n        // Sort jobs by datePosted descending (newest first)\r\n        const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n        setJobs(sortedJobs);\r\n      }\r\n      \r\n      setError(null);\r\n      // Close modal\r\n      closeModal();\r\n    } catch (err) {\r\n      console.error(\"Error saving job:\", err);\r\n      // Show more detailed error message if available\r\n      const errorMessage = err.response?.data?.message || \r\n                          err.response?.data?.error || \r\n                          (selectedJob ? \"Failed to update job\" : \"Failed to create job\");\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const filteredJobs = jobs.filter(job => {\r\n    const matchesSearch = (job.company && job.company.toLowerCase().includes(searchTerm.toLowerCase())) || \r\n                          (job.position && job.position.toLowerCase().includes(searchTerm.toLowerCase()));\r\n    \r\n    return matchesSearch;\r\n  });\r\n\r\n  // Add this useEffect to handle closing the dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      const dropdownElement = document.querySelector('.company-select-dropdown');\r\n      if (dropdownElement && !dropdownElement.contains(event.target)) {\r\n        setShowCompanyDropdown(false);\r\n      }\r\n    };\r\n\r\n    if (showCompanyDropdown) {\r\n      document.addEventListener('mousedown', handleClickOutside);\r\n      return () => {\r\n        document.removeEventListener('mousedown', handleClickOutside);\r\n      };\r\n    }\r\n  }, [showCompanyDropdown]);\r\n\r\n  return (\r\n    <div className=\"jobs-admin-container\">\r\n      {/* Delete Confirmation Dialog */}\r\n      {showDeleteConfirm && (\r\n        <div className=\"delete-confirm-overlay\">\r\n          <div className=\"delete-confirm-dialog\">\r\n            <div className=\"delete-confirm-header\">\r\n              <FontAwesomeIcon icon={faExclamationTriangle} className=\"delete-icon\" />\r\n              <h3>Confirm Deletion</h3>\r\n            </div>\r\n            <div className=\"delete-confirm-content\">\r\n              <p>Are you sure you want to delete this job posting?</p>\r\n              <p><strong>{jobToDelete?.position}</strong></p>\r\n              <p>This action cannot be undone.</p>\r\n            </div>\r\n            <div className=\"delete-confirm-actions\">\r\n              <button \r\n                className=\"cancel-delete-btn\" \r\n                onClick={cancelDelete}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button \r\n                className=\"confirm-delete-btn\" \r\n                onClick={() => handleDeleteJob(jobToDelete.id)}\r\n              >\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"jobs-header\">\r\n        <h1 className=\"jobs-title\">Job Listings</h1>\r\n        <div className=\"job-header-actions\">\r\n          <button className=\"refresh-button\" onClick={() => {\r\n            setLoading(true);\r\n            const fetchJobs = async () => {\r\n              try {\r\n                const response = await ApiService.jobs.getAll();\r\n                \r\n                // Transform backend data to match the format used in frontend\r\n                const formattedJobs = response.data.map(job => {\r\n                  // Determine the best date to use\r\n                  let dateToUse;\r\n                  if (job.created_at) {\r\n                    dateToUse = job.created_at;\r\n                  } else if (job.start_date) {\r\n                    dateToUse = job.start_date;\r\n                  } else if (job.posted_date) {\r\n                    dateToUse = job.posted_date;\r\n                  } else {\r\n                    dateToUse = new Date().toISOString();\r\n                  }\r\n\r\n                  // Compose salary display string\r\n                  let salaryDisplay = '';\r\n                  if (job.min_salary && job.max_salary) {\r\n                    salaryDisplay = `${job.min_salary} - ${job.max_salary}`;\r\n                  } else if (job.min_salary) {\r\n                    salaryDisplay = `${job.min_salary}`;\r\n                  } else if (job.max_salary) {\r\n                    salaryDisplay = `${job.max_salary}`;\r\n                  } else {\r\n                    salaryDisplay = '';\r\n                  }\r\n                    return {\r\n                    id: job.job_id,\r\n                    hot: job.hot || false,\r\n                    company: job.company_name,\r\n                    position: job.job_title,\r\n                    location: job.main_topics,\r\n                    workTime: job.job_type,\r\n                    experience_level: job.experience_level,\r\n                    cv_email: job.send_cv_email,\r\n                    salary: salaryDisplay,\r\n                    status: 'Active',\r\n                    datePosted: dateToUse,\r\n                    rawDatePosted: job.start_date || job.posted_date || job.created_at,\r\n                    views: job.view_count || 0,\r\n                    end_date: job.end_date\r\n                  };\r\n                });\r\n                \r\n                // Sort jobs by datePosted descending (newest first)\r\n                const sortedJobs = [...formattedJobs].sort((a, b) => new Date(b.datePosted) - new Date(a.datePosted));\r\n                setJobs(sortedJobs);\r\n                setError(null);\r\n              } catch (err) {\r\n                console.error(\"Error fetching jobs:\", err);\r\n                setError(\"Failed to load jobs from server\");\r\n              } finally {\r\n                setLoading(false);\r\n              }\r\n            };\r\n\r\n            fetchJobs();\r\n          }} disabled={loading}>\r\n            <FontAwesomeIcon icon={faSync} />\r\n            <span>Refresh</span>\r\n          </button>\r\n          <button className=\"add-job-button\" onClick={openNewJobModal} disabled={loading}>\r\n            <FontAwesomeIcon icon={faPlus} />\r\n            <span>Add New Job</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"error-message\">\r\n          <FontAwesomeIcon icon={faExclamationCircle} />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"filters-container\">\r\n        <div className=\"search-container\">\r\n          <div className=\"search-input-wrapper\">\r\n            <FontAwesomeIcon icon={faSearch} className=\"search-icon\" />          <input\r\n            type=\"text\"\r\n            placeholder=\"Search by company or position...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n            className=\"search-input\"\r\n            style={{ paddingLeft: '40px' }}\r\n          />\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div className=\"table-container\">\r\n        {loading ? (\r\n          <div className=\"loading-container\">\r\n            <FontAwesomeIcon icon={faSpinner} spin size=\"2x\" />\r\n            <span>Loading jobs...</span>\r\n          </div>\r\n        ) : (\r\n          <>\r\n          <table className=\"jobs-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Job</th>\r\n                <th>Type</th>\r\n                <th>Experience Level</th>\r\n                <th>Email / Web URL</th>\r\n                <th>Salary</th>\r\n                <th>Expire Date</th>\r\n                <th>Posted Date</th>\r\n                <th>Views</th>\r\n                <th>Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              {filteredJobs.length > 0 ? (\r\n                filteredJobs.map(job => (\r\n                  <tr key={job.id}>\r\n                    <td>\r\n                      {job.hot && (\r\n                        <span className=\"hot-badge\">\r\n                          <FontAwesomeIcon icon={faFire} style={{ marginRight: '4px' }} />\r\n                          URGENT\r\n                        </span>\r\n                      )}\r\n<div className=\"job-position\" title={job.position}>\r\n  {job.position && job.position.length > 35\r\n    ? job.position.slice(0, 32) + '...'\r\n    : job.position}\r\n</div>\r\n                      <div className=\"job-company\">{job.company}</div>\r\n                    </td>\r\n                    <td>{job.workTime}</td>\r\n                    <td>\r\n                      <div className=\"experience-level\" title={job.experience_level}>\r\n                        {job.experience_level || 'Not specified'}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div className=\"cv-email\" title={job.cv_email}>\r\n                        {job.cv_email ? (\r\n                          <a href={`mailto:${job.cv_email}`} className=\"email-link\">\r\n                            {job.cv_email.length > 20 ? job.cv_email.slice(0, 17) + '...' : job.cv_email}\r\n                          </a>\r\n                        ) : (\r\n                          'Not provided'\r\n                        )}\r\n                      </div>\r\n                    </td>\r\n                    <td>{job.salary}</td>\r\n                    <td>{formatDisplayDate(job.end_date)}</td>\r\n                      <td>{formatDisplayDate(job.datePosted)}</td>\r\n                    <td>\r\n                      <div className=\"views-container\">\r\n                        <FontAwesomeIcon icon={faEye} className=\"views-icon\" />\r\n                        {job.views}\r\n                      </div>\r\n                    </td>\r\n                    <td>\r\n                      <div style={{ display: 'flex' }}>\r\n                        <button \r\n                          className=\"action-button edit-button\" \r\n                          onClick={() => handleEditJob(job)}\r\n                          title=\"Edit job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faEdit} />\r\n                        </button>\r\n                        {/* Removed Inactive/Active toggle button */}\r\n                        <button \r\n                          className=\"action-button delete-button\" \r\n                          onClick={() => confirmDelete(job)}\r\n                          title=\"Delete job\"\r\n                          disabled={loading}\r\n                        >\r\n                          <FontAwesomeIcon icon={faTrash} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td colSpan=\"9\" className=\"no-jobs-message\">\r\n                    {searchTerm ? \r\n                      \"No jobs match your search criteria\" : \r\n                      \"No jobs available\"\r\n                    }\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      {/* New Job Modal */}\r\n      {isModalOpen && (\r\n        <div className=\"modal-overlay\" onClick={(e) => {\r\n          // Close modal when clicking outside\r\n          if (e.target.className === 'modal-overlay') {\r\n            closeModal();\r\n          }\r\n        }}>\r\n          <div className=\"job-modal\">\r\n            <div className=\"modal-header\">\r\n              <h2>{selectedJob ? 'Edit Job' : 'Add New Job'}</h2>\r\n              <button className=\"close-button\" onClick={closeModal}>\r\n                <FontAwesomeIcon icon={faTimes} />\r\n              </button>\r\n            </div>\r\n            <div className=\"modal-content\">\r\n              <form onSubmit={handleFormSubmit}>\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_title\">Job Title <span style={{ color: 'red' }}>*</span></label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"job_title\"\r\n                      name=\"job_title\"\r\n                      value={newJobForm.job_title}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter job title\"\r\n                    />\r\n                    </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Company <span style={{ color: 'red' }}>*</span></label>\r\n                    <div className=\"company-selector\">\r\n                      <div className=\"company-select-dropdown\">\r\n                        <button \r\n                          type=\"button\"\r\n                          className=\"company-select-button\"\r\n                          onClick={() => setShowCompanyDropdown(!showCompanyDropdown)}\r\n                        >\r\n                          {newJobForm.company_name ? newJobForm.company_name : 'Select a company'}\r\n                          <FontAwesomeIcon icon={faChevronDown} />\r\n                        </button>\r\n                        \r\n                        {showCompanyDropdown && (\r\n                          <div className=\"company-list-dropdown\">\r\n                            {companies && companies.length > 0 ? (\r\n                              companies.map(company => (\r\n                                <div \r\n                                  key={company.company_id}\r\n                                  className=\"company-item\"\r\n                                  onClick={() => {\r\n                                    handleSelectCompany(company);\r\n                                  }}\r\n                                >\r\n                                  <div className=\"company-item-logo\">\r\n                                    {company.company_logo_url ? (\r\n                                      <img src={company.company_logo_url} alt={company.company_name} />\r\n                                    ) : (\r\n                                      <FontAwesomeIcon icon={faBuilding} />\r\n                                    )}\r\n                                  </div>\r\n                                  <span>{company.company_name}</span>\r\n                                </div>\r\n                              ))\r\n                            ) : (\r\n                              <div className=\"no-companies\">No companies available</div>\r\n                            )}\r\n                            <div className=\"manual-company-entry\">\r\n                              <label>Or enter company name manually:</label>\r\n                              <input\r\n                                type=\"text\"\r\n                                placeholder=\"Enter company name\"\r\n                                value={newJobForm.company_name || ''}\r\n                                onChange={(e) => {\r\n                                  setNewJobForm({\r\n                                    ...newJobForm,\r\n                                    company_name: e.target.value\r\n                                  });\r\n                                }}\r\n                              />\r\n                              <button\r\n                                type=\"button\"\r\n                                className=\"apply-company-btn\"\r\n                                onClick={() => setShowCompanyDropdown(false)}\r\n                              >\r\n                                Apply\r\n                              </button>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      \r\n                      {/* Display logo preview if available */}\r\n                      {logoPreview && (\r\n                        <div className=\"logo-preview-container\">\r\n                          <img src={logoPreview} alt=\"Company logo\" />\r\n                          <button \r\n                            type=\"button\" \r\n                            className=\"remove-logo-button\"\r\n                            onClick={handleRemoveLogo}\r\n                          >\r\n                            <FontAwesomeIcon icon={faTimes} />\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                      \r\n                      {/* Allow uploading a new logo if no existing one is selected */}\r\n                      {!logoPreview && (\r\n                        <div>\r\n                          <input\r\n                            type=\"file\"\r\n                            id=\"company_logo\"\r\n                            name=\"company_logo\"\r\n                            onChange={(e) => handleImageUpload(e, 'company_logo')}\r\n                            accept=\"image/*\"\r\n                            style={{ display: 'none' }}\r\n                          />\r\n                          <button\r\n                            type=\"button\"\r\n                            className=\"file-upload-label\"\r\n                            onClick={() => document.getElementById('company_logo').click()}\r\n                          >\r\n                            <FontAwesomeIcon icon={faImage} />\r\n                            Upload Company Logo\r\n                          </button>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"year\">Year</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"year\"\r\n                      name=\"year\"\r\n                      value={newJobForm.year}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      min=\"2000\"\r\n                      max=\"2100\"\r\n                    />\r\n                  </div>                  <div className=\"form-group\">\r\n                    <label htmlFor=\"job_type\">Job Type</label>\r\n                    <select\r\n                      id=\"job_type\"\r\n                      name=\"job_type\"\r\n                      value={newJobForm.job_type}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Full Time Jobs\">Full Time Jobs</option>\r\n                      <option value=\"Part Time Jobs\">Part Time Jobs</option>\r\n                      <option value=\"Remote Jobs\">Remote Jobs</option>\r\n                      <option value=\"Freelance\">Freelance</option>\r\n                      <option value=\"Temporary\">Temporary</option>\r\n                    </select>\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"experience_level\">Experience Level</label>\r\n                    <select\r\n                      id=\"experience_level\"\r\n                      name=\"experience_level\"\r\n                      value={newJobForm.experience_level}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      {EXPERIENCE_OPTIONS.map(option => (\r\n                        <option key={option} value={option}>{option}</option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"start_date\">Start Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"start_date\"\r\n                      name=\"start_date\"\r\n                      value={newJobForm.start_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"end_date\">End Date</label>\r\n                    <input\r\n                      type=\"date\"\r\n                      id=\"end_date\"\r\n                      name=\"end_date\"\r\n                      value={newJobForm.end_date}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"send_cv_email\">Email/WebURL</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"send_cv_email\"\r\n                      name=\"send_cv_email\"\r\n                      value={newJobForm.send_cv_email}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Enter email or website URL for CV submissions (Optional)\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"min_salary\">Minimum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"min_salary\"\r\n                      name=\"min_salary\"\r\n                      value={newJobForm.min_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Minimum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"max_salary\">Maximum Salary</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      id=\"max_salary\"\r\n                      name=\"max_salary\"\r\n                      value={newJobForm.max_salary}\r\n                      onChange={handleFormChange}\r\n                      placeholder=\"Maximum salary\"\r\n                      min=\"0\"\r\n                      step=\"any\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Mark as URGENT Job - Moved to middle */}\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group checkbox-group\" style={{textAlign: 'left'}}>\r\n                    <label className=\"checkbox-container\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        name=\"hot\"\r\n                        checked={newJobForm.hot}\r\n                        onChange={handleFormChange}\r\n                      />\r\n                      <span className=\"checkbox-text\">\r\n                        <FontAwesomeIcon icon={faFire} className=\"hot-icon\" />\r\n                        Mark as URGENT Job\r\n                      </span>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"main_topics\">Main Topic/Category</label>\r\n                    <select\r\n                      id=\"main_topics\"\r\n                      name=\"main_topics\"\r\n                      value={newJobForm.main_topics}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                    >\r\n                      <option value=\"Government Jobs\">Government Jobs</option>\r\n                      <option value=\"Private Jobs\">Private Jobs</option>\r\n                      <option value=\"Foreign Jobs\">Foreign Jobs</option>\r\n                      <option value=\"Internships\">Internships</option>\r\n                    </select>\r\n                  </div>\r\n                </div>                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n<label>\r\n  <span>Sub Topics</span>\r\n  <span className=\"small-text\">{newJobForm.sub_topics.length} selected</span>\r\n  <button\r\n    type=\"button\"\r\n    style={{ marginLeft: '16px', padding: '2px 8px', fontSize: '0.9em' }}\r\n    onClick={() => setNewJobForm(prev => ({ ...prev, sub_topics: [] }))}\r\n  >\r\n    Clear All\r\n  </button>\r\n</label>\r\n<div className=\"subtopics-management-info\">\r\n  <small style={{ color: '#6c757d', fontStyle: 'italic' }}>\r\n    💡 Tip: You can manage available sub-topics from the \"Sub Topics\" section in the sidebar\r\n  </small>\r\n</div>\r\n<div className=\"subtopics-checkbox-container\">\r\n  {availableSubTopics.map((topic, index) => (\r\n                        <div \r\n                          key={index} \r\n                          className={`subtopic-checkbox ${newJobForm.sub_topics.includes(topic) ? 'selected' : ''}`}\r\n                        >\r\n<input\r\n  type=\"checkbox\"\r\n  id={`subtopic-${index}`}\r\n  checked={newJobForm.sub_topics.includes(topic)}\r\n  onChange={e => {\r\n    const checked = e.target.checked;\r\n    setNewJobForm(prev => {\r\n      const set = new Set(prev.sub_topics);\r\n      if (checked) {\r\n        set.add(topic);\r\n      } else {\r\n        set.delete(topic);\r\n      }\r\n      return {\r\n        ...prev,\r\n        sub_topics: Array.from(set)\r\n      };\r\n    });\r\n  }}\r\n                          />\r\n                          <label htmlFor={`subtopic-${index}`}>\r\n                            {topic}\r\n                          </label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group full-width\">\r\n                    <label htmlFor=\"job_description\">Job Description</label>\r\n                    <textarea\r\n                      id=\"job_description\"\r\n                      name=\"job_description\"\r\n                      rows=\"5\"\r\n                      value={newJobForm.job_description}\r\n                      onChange={handleFormChange}\r\n                      required\r\n                      placeholder=\"Enter detailed job description\"\r\n                    ></textarea>\r\n                    <small className=\"form-help-text\">\r\n                      Note: Fancy text formatting, special characters, and emojis are not supported and will be removed when saved.\r\n                    </small>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-row\">\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Image</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_image\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_image')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_image\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Image</span>\r\n                      </label>\r\n                    </div>\r\n                    {jobImagePreview && (\r\n                      <div className=\"image-preview\">\r\n                        <img src={jobImagePreview} alt=\"Job Post\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"form-group\">\r\n                    <label>Job Post Thumbnail</label>\r\n                    <div className=\"file-upload-container\">\r\n                      <input\r\n                        type=\"file\"\r\n                        id=\"job_post_thumbnail\"\r\n                        onChange={(e) => handleImageUpload(e, 'job_post_thumbnail')}\r\n                        accept=\"image/*\"\r\n                        className=\"file-input\"\r\n                      />\r\n                      <label htmlFor=\"job_post_thumbnail\" className=\"file-upload-label\">\r\n                        <FontAwesomeIcon icon={faImage} />\r\n                        <span>Choose Thumbnail</span>\r\n                      </label>\r\n                    </div>\r\n                    {thumbnailPreview && (\r\n                      <div className=\"thumbnail-preview\">\r\n                        <img src={thumbnailPreview} alt=\"Thumbnail\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n\r\n\r\n                <div className=\"modal-footer\">\r\n                  <button type=\"button\" className=\"cancel-button\" onClick={closeModal}>\r\n                    Cancel\r\n                  </button>\r\n                  <button type=\"submit\" className=\"submit-button\">\r\n                    {selectedJob ? 'Update Job' : 'Create Job'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobsAdmin;\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,mBAAmB,EACnBC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,SAAS,EACTC,aAAa,EACbC,qBAAqB,EACrBC,MAAM,QACD,mCAAmC;AAC1C,OAAO,yBAAyB;AAChC,OAAO,4BAA4B;AACnC,OAAO,oCAAoC;AAC3C,OAAOC,UAAU,MAAM,2BAA2B;AAClD;AACA,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,kBAAkB,GAAG,CACzB,wBAAwB,EACxB,aAAa,EACb,WAAW,EACX,cAAc,EACd,SAAS,EACT,WAAW,CACZ;AAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC2C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA;EACA,MAAM+C,iBAAiB,GAAGA,CAAA,KAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEtE;EACA,MAAMC,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,GAAG,EAAE,OAAO,GAAG;IACjD,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;MACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOH,UAAU,CAAC,CAAC;MAC9C,OAAOC,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,gCAAgC,EAAE6B,GAAG,CAAC;MACpD,OAAOR,UAAU;IACnB;EACF,CAAC;EACC,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC;IAC7CgE,SAAS,EAAE,EAAE;IACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;IAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;IAC/BoB,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,iBAAiB;IAAE;IAChCC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,gBAAgB;IAAE;IAC5BC,gBAAgB,EAAE,aAAa;IAAE;IACjCC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,eAAe,EAAE,EAAE;IACnBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,IAAI;IAClB;IACAC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACpD;EACA,MAAM,CAACwF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM0F,gBAAgB,GAAG,CACvB,oCAAoC,EACpC,8BAA8B,EAC9B,6BAA6B,EAC7B,6BAA6B,EAC7B,+BAA+B,EAC/B,8CAA8C,EAC9C,WAAW,EACX,mBAAmB,EACnB,eAAe,EACf,4BAA4B,EAC5B,UAAU,EACV,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,sBAAsB,EACtB,yBAAyB,CAC1B;;EAED;EACAzF,SAAS,CAAC,MAAM;IACd;IACA;IACAwF,qBAAqB,CAACC,gBAAgB,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzF,SAAS,CAAC,MAAM;IACd,MAAM0F,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF7D,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM8D,QAAQ,GAAG,MAAMzE,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;;QAE/C;QACA;QACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;UAC7C;UACA,IAAIC,SAAS;UACb,IAAID,GAAG,CAACE,UAAU,EAAE;YAClB;YACAD,SAAS,GAAGD,GAAG,CAACE,UAAU;UAC5B,CAAC,MAAM,IAAIF,GAAG,CAAC/B,UAAU,EAAE;YACzB;YACAgC,SAAS,GAAGD,GAAG,CAAC/B,UAAU;UAC5B,CAAC,MAAM,IAAI+B,GAAG,CAACG,WAAW,EAAE;YAC1B;YACAF,SAAS,GAAGD,GAAG,CAACG,WAAW;UAC7B,CAAC,MAAM;YACL;YACAF,SAAS,GAAG,IAAIlD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACtC;;UAEA;UACA,IAAIoD,aAAa,GAAG,EAAE;UACtB,IAAIJ,GAAG,CAACxB,UAAU,IAAIwB,GAAG,CAACvB,UAAU,EAAE;YACpC2B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,MAAMwB,GAAG,CAACvB,UAAU,EAAE;UACzD,CAAC,MAAM,IAAIuB,GAAG,CAACxB,UAAU,EAAE;YACzB4B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,EAAE;UACrC,CAAC,MAAM,IAAIwB,GAAG,CAACvB,UAAU,EAAE;YACzB2B,aAAa,GAAG,GAAGJ,GAAG,CAACvB,UAAU,EAAE;UACrC,CAAC,MAAM;YACL2B,aAAa,GAAG,EAAE;UACpB;UACE,OAAO;YACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;YACdtB,GAAG,EAAEgB,GAAG,CAAChB,GAAG,IAAI,KAAK;YAAE;YACvBuB,OAAO,EAAEP,GAAG,CAACnB,YAAY;YACzB2B,QAAQ,EAAER,GAAG,CAACjC,SAAS;YACvB0C,QAAQ,EAAET,GAAG,CAAC5B,WAAW;YACzBsC,QAAQ,EAAEV,GAAG,CAAC1B,QAAQ;YACtBC,gBAAgB,EAAEyB,GAAG,CAACzB,gBAAgB;YACtCoC,QAAQ,EAAEX,GAAG,CAAC7B,aAAa;YAC3ByC,MAAM,EAAER,aAAa;YACrBrB,MAAM,EAAE,QAAQ;YAAE;YAClB8B,UAAU,EAAEZ,SAAS;YACrBa,aAAa,EAAEd,GAAG,CAAC/B,UAAU,IAAI+B,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;YAAE;YACpEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;YAAE;YAC5B9C,QAAQ,EAAE8B,GAAG,CAAC9B,QAAQ,CAAC;UACzB,CAAC;QACH,CAAC,CAAC;;QAEF;QACA,MAAM+C,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrE,IAAI,CAACqE,CAAC,CAACP,UAAU,CAAC,GAAG,IAAI9D,IAAI,CAACoE,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGlF,OAAO,CAACsF,UAAU,CAAC;QACnBlF,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;QAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED6D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1F,SAAS,CAAC,MAAM;IACd,MAAMqH,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,IAAI;QACF,MAAMC,WAAW,GAAG,IAAIvE,IAAI,CAAC,CAAC;QAC9B,MAAMwE,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;QAE/C;QACA,MAAM5B,QAAQ,GAAG,MAAMzE,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;QAC/C,MAAM4B,OAAO,GAAG7B,QAAQ,CAACG,IAAI;QAE7B,MAAM2B,WAAW,GAAGD,OAAO,CAACE,MAAM,CAAC1B,GAAG,IAAI;UACxC,IAAI,CAACA,GAAG,CAAC9B,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;;UAEjC,MAAMyD,OAAO,GAAG,IAAI5E,IAAI,CAACiD,GAAG,CAAC9B,QAAQ,CAAC;UACtC,MAAM0D,cAAc,GAAGN,WAAW,CAAChE,OAAO,CAAC,CAAC,GAAGqE,OAAO,CAACrE,OAAO,CAAC,CAAC;;UAEhE;UACA,OAAOsE,cAAc,GAAGL,YAAY;QACtC,CAAC,CAAC;;QAEF;QACA,KAAK,MAAMM,UAAU,IAAIJ,WAAW,EAAE;UACpC,IAAI;YACF,MAAMvG,UAAU,CAACQ,IAAI,CAACoG,MAAM,CAACD,UAAU,CAACvB,MAAM,CAAC;YAC/C1C,OAAO,CAACmE,GAAG,CAAC,6BAA6BF,UAAU,CAAC9D,SAAS,SAAS8D,UAAU,CAACvB,MAAM,GAAG,CAAC;UAC7F,CAAC,CAAC,OAAO0B,SAAS,EAAE;YAClBpE,OAAO,CAAC9B,KAAK,CAAC,6BAA6B+F,UAAU,CAACvB,MAAM,GAAG,EAAE0B,SAAS,CAAC;UAC7E;QACF;;QAEA;QACA,IAAIP,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMC,eAAe,GAAG,MAAMhH,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;UACtD,MAAMC,aAAa,GAAGqC,eAAe,CAACpC,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;YACpD,IAAIC,SAAS;YACb,IAAID,GAAG,CAACE,UAAU,EAAE;cAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;YAC5B,CAAC,MAAM,IAAIF,GAAG,CAAC/B,UAAU,EAAE;cACzBgC,SAAS,GAAGD,GAAG,CAAC/B,UAAU;YAC5B,CAAC,MAAM,IAAI+B,GAAG,CAACG,WAAW,EAAE;cAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;YAC7B,CAAC,MAAM;cACLF,SAAS,GAAG,IAAIlD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACtC;YAEA,IAAIoD,aAAa,GAAG,EAAE;YACtB,IAAIJ,GAAG,CAACxB,UAAU,IAAIwB,GAAG,CAACvB,UAAU,EAAE;cACpC2B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,MAAMwB,GAAG,CAACvB,UAAU,EAAE;YACzD,CAAC,MAAM,IAAIuB,GAAG,CAACxB,UAAU,EAAE;cACzB4B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,EAAE;YACrC,CAAC,MAAM,IAAIwB,GAAG,CAACvB,UAAU,EAAE;cACzB2B,aAAa,GAAG,GAAGJ,GAAG,CAACvB,UAAU,EAAE;YACrC,CAAC,MAAM;cACL2B,aAAa,GAAG,EAAE;YACpB;YAEA,OAAO;cACLC,EAAE,EAAEL,GAAG,CAACM,MAAM;cACdtB,GAAG,EAAEgB,GAAG,CAAChB,GAAG,IAAI,KAAK;cACrBuB,OAAO,EAAEP,GAAG,CAACnB,YAAY;cACzB2B,QAAQ,EAAER,GAAG,CAACjC,SAAS;cACvB0C,QAAQ,EAAET,GAAG,CAAC5B,WAAW;cACzBsC,QAAQ,EAAEV,GAAG,CAAC1B,QAAQ;cACtBC,gBAAgB,EAAEyB,GAAG,CAACzB,gBAAgB;cACtCoC,QAAQ,EAAEX,GAAG,CAAC7B,aAAa;cAC3ByC,MAAM,EAAER,aAAa;cACrBrB,MAAM,EAAE,QAAQ;cAChB8B,UAAU,EAAEZ,SAAS;cACrBa,aAAa,EAAEd,GAAG,CAAC/B,UAAU,IAAI+B,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;cAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;cAC1B9C,QAAQ,EAAE8B,GAAG,CAAC9B;YAChB,CAAC;UACH,CAAC,CAAC;UAEF,MAAM+C,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrE,IAAI,CAACqE,CAAC,CAACP,UAAU,CAAC,GAAG,IAAI9D,IAAI,CAACoE,CAAC,CAACN,UAAU,CAAC,CAAC;UACrGlF,OAAO,CAACsF,UAAU,CAAC;QACrB;MAEF,CAAC,CAAC,OAAOtD,GAAG,EAAE;QACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;QACtD;MACF;IACF,CAAC;;IAED;IACA0D,yBAAyB,CAAC,CAAC;;IAE3B;IACA,MAAMc,UAAU,GAAGC,WAAW,CAACf,yBAAyB,EAAE,OAAO,CAAC;;IAElE;IACA,OAAO,MAAMgB,aAAa,CAACF,UAAU,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnI,SAAS,CAAC,MAAM;IACd,IAAIoC,WAAW,EAAE;MACfwB,OAAO,CAACmE,GAAG,CAAC,qCAAqC,CAAC;MAClDO,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAClG,WAAW,CAAC,CAAC;EAEjB,MAAMkG,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF1E,OAAO,CAACmE,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMpC,QAAQ,GAAG,MAAMzE,UAAU,CAACoB,SAAS,CAACsD,MAAM,CAAC,CAAC;MACpD,MAAM2C,aAAa,GAAG5C,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE;MAC9ClC,OAAO,CAACmE,GAAG,CAAC,oBAAoB,EAAEQ,aAAa,CAAC;MAChDhG,YAAY,CAACgG,aAAa,CAAC;;MAE3B;MACA,IAAIrG,WAAW,IAAI2B,UAAU,CAACgB,YAAY,EAAE;QAC1C,MAAM2D,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxClC,OAAO,IAAIA,OAAO,CAAC1B,YAAY,KAAKhB,UAAU,CAACgB,YACjD,CAAC;QAED,IAAI2D,eAAe,IAAIA,eAAe,CAACE,gBAAgB,IAAI,CAACrD,WAAW,EAAE;UACvEzB,OAAO,CAACmE,GAAG,CAAC,4CAA4C,EAAES,eAAe,CAAC3D,YAAY,CAAC;UACvFS,cAAc,CAACkD,eAAe,CAACE,gBAAgB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAO/E,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAE6B,GAAG,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMgF,kBAAkB,GAAIC,CAAC,IAAK;IAChC3G,aAAa,CAAC2G,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAO/C,GAAG,IAAK;IACnC,IAAI;MACFnE,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM8D,QAAQ,GAAG,MAAMzE,UAAU,CAACQ,IAAI,CAACsH,OAAO,CAAChD,GAAG,CAACK,EAAE,CAAC;MACtD,MAAM4C,cAAc,GAAGtD,QAAQ,CAACG,IAAI;MAEpClC,OAAO,CAACmE,GAAG,CAAC,uBAAuB,EAAEkB,cAAc,CAAC;MACpDrF,OAAO,CAACmE,GAAG,CAAC,qBAAqB,EAAEkB,cAAc,CAAClF,SAAS,CAAC;MAE5D5B,cAAc,CAAC6D,GAAG,CAAC;MACnB;MACA;MACA,MAAMkD,UAAU,GAAI/F,UAAU,IAAK;QACjC,IAAI,CAACA,UAAU,EAAE,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;;QAE7C,IAAI;UACF;UACA,MAAMM,IAAI,GAAG,IAAIL,IAAI,CAACI,UAAU,CAAC;UACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;YACzBM,OAAO,CAACuF,IAAI,CAAC,wBAAwB,EAAEhG,UAAU,CAAC;YAClD,OAAOL,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC9B;;UAEA;UACA,MAAMU,IAAI,GAAGJ,IAAI,CAACY,WAAW,CAAC,CAAC;UAC/B,MAAMP,KAAK,GAAG2F,MAAM,CAAChG,IAAI,CAACiG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAC1D,MAAM5F,GAAG,GAAG0F,MAAM,CAAChG,IAAI,CAACmG,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACnD,OAAO,GAAG9F,IAAI,IAAIC,KAAK,IAAIC,GAAG,EAAE;QAClC,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,wBAAwB,EAAE6B,GAAG,CAAC;UAC5C,OAAOb,iBAAiB,CAAC,CAAC;QAC5B;MACF,CAAC;;MAED;MACA,IAAImG,cAAc,CAACtE,cAAc,EAAE;QACjCO,kBAAkB,CAAC+D,cAAc,CAACtE,cAAc,CAAC;MACnD;MAEA,IAAIsE,cAAc,CAACrE,kBAAkB,EAAE;QACrCQ,mBAAmB,CAAC6D,cAAc,CAACrE,kBAAkB,CAAC;MACxD;;MAEA;MACA,IAAI4E,cAAc,GAAG,IAAI;MACzB,IAAIP,cAAc,CAACnE,YAAY,EAAE;QAC/BQ,cAAc,CAAC2D,cAAc,CAACnE,YAAY,CAAC;QAC3C0E,cAAc,GAAGP,cAAc,CAACnE,YAAY;MAC9C,CAAC,MAAM,IAAImE,cAAc,CAACP,gBAAgB,EAAE;QAC1CpD,cAAc,CAAC2D,cAAc,CAACP,gBAAgB,CAAC;QAC/Cc,cAAc,GAAGP,cAAc,CAACP,gBAAgB;MAClD,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAMe,iBAAiB,GAAG,MAAMvI,UAAU,CAACoB,SAAS,CAACsD,MAAM,CAAC,CAAC;UAC7D,MAAM2C,aAAa,GAAGkB,iBAAiB,CAAC3D,IAAI,CAACA,IAAI,IAAI,EAAE;UAEvD,MAAM0C,eAAe,GAAGD,aAAa,CAACE,IAAI,CACxClC,OAAO,IAAIA,OAAO,CAAC1B,YAAY,KAAKoE,cAAc,CAACpE,YACrD,CAAC;UAED,IAAI2D,eAAe,IAAIA,eAAe,CAACE,gBAAgB,EAAE;YACvDpD,cAAc,CAACkD,eAAe,CAACE,gBAAgB,CAAC;YAChDc,cAAc,GAAGhB,eAAe,CAACE,gBAAgB;UACnD;QACF,CAAC,CAAC,OAAO/E,GAAG,EAAE;UACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;QACnD;MACF;MACA;MACA,IAAI+F,QAAQ,GAAGT,cAAc,CAAC1E,gBAAgB;MAC9C,IAAI,CAAChD,kBAAkB,CAACoI,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC1CA,QAAQ,GAAG,wBAAwB;MACrC;MACA5F,aAAa,CAAC;QACZC,SAAS,EAAEkF,cAAc,CAAClF,SAAS;QACnCc,YAAY,EAAEoE,cAAc,CAACpE,YAAY;QACzCrB,IAAI,EAAEyF,cAAc,CAACzF,IAAI,IAAI,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACrDC,UAAU,EAAEiF,UAAU,CAACD,cAAc,CAAChF,UAAU,CAAC;QACjDC,QAAQ,EAAEgF,UAAU,CAACD,cAAc,CAAC/E,QAAQ,CAAC;QAC7CI,QAAQ,EAAE2E,cAAc,CAAC3E,QAAQ;QACjCC,gBAAgB,EAAEmF,QAAQ;QAC1BlF,UAAU,EAAEyE,cAAc,CAACzE,UAAU,IAAI,EAAE;QAC3CC,UAAU,EAAEwE,cAAc,CAACxE,UAAU,IAAI,EAAE;QAC3CN,aAAa,EAAE8E,cAAc,CAAC9E,aAAa,IAAI,EAAE;QACjDC,WAAW,EAAE6E,cAAc,CAAC7E,WAAW;QACvCC,UAAU,EAAE,CAAC,MAAM;UACjB,IAAI;YACF;YACA,IAAI,CAAC4E,cAAc,CAAC5E,UAAU,EAAE,OAAO,EAAE;YAEzC,IAAI,OAAO4E,cAAc,CAAC5E,UAAU,KAAK,QAAQ,EAAE;cACjD;cACA,IAAI4E,cAAc,CAAC5E,UAAU,CAACuF,IAAI,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACpD,OAAOC,IAAI,CAACC,KAAK,CAACd,cAAc,CAAC5E,UAAU,CAAC;cAC9C,CAAC,MAAM;gBACL;gBACA,OAAO,CAAC4E,cAAc,CAAC5E,UAAU,CAAC;cACpC;YACF,CAAC,MAAM,IAAI2F,KAAK,CAACC,OAAO,CAAChB,cAAc,CAAC5E,UAAU,CAAC,EAAE;cACnD,OAAO4E,cAAc,CAAC5E,UAAU;YAClC,CAAC,MAAM;cACL,OAAO,EAAE;YACX;UACF,CAAC,CAAC,OAAOV,GAAG,EAAE;YACZC,OAAO,CAACuF,IAAI,CAAC,iDAAiD,EAAExF,GAAG,CAAC;YACpE,OAAOsF,cAAc,CAAC5E,UAAU,GAAG,CAAC4E,cAAc,CAAC5E,UAAU,CAAC,GAAG,EAAE;UACrE;QACF,CAAC,EAAE,CAAC;QACJK,eAAe,EAAEuE,cAAc,CAACvE,eAAe,IAAI,EAAE;QACrDC,cAAc,EAAEsE,cAAc,CAACtE,cAAc;QAC7CC,kBAAkB,EAAEqE,cAAc,CAACrE,kBAAkB;QACrDE,YAAY,EAAEmE,cAAc,CAACnE,YAAY;QACzC4D,gBAAgB,EAAEc,cAAc;QAAE;QAClC;QACAzE,MAAM,EAAE,QAAQ;QAChBC,GAAG,EAAEiE,cAAc,CAACjE,GAAG,IAAI,KAAK,CAAC;MACnC,CAAC,CAAC;MAEF3C,cAAc,CAAC,IAAI,CAAC;MACpBN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,6BAA6B,EAAE6B,GAAG,CAAC;MACjD5B,QAAQ,CAAC,kCAAkCiE,GAAG,CAACQ,QAAQ,EAAE,CAAC;IAC5D,CAAC,SAAS;MACR3E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqI,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI;MACFtI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMX,UAAU,CAACQ,IAAI,CAACoG,MAAM,CAACqC,KAAK,CAAC;MACnCxI,OAAO,CAACD,IAAI,CAACgG,MAAM,CAAC1B,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK8D,KAAK,CAAC,CAAC;MAC7CpI,QAAQ,CAAC,IAAI,CAAC;MACdY,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,qBAAqB,EAAE6B,GAAG,CAAC;MACzC5B,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuI,aAAa,GAAIpE,GAAG,IAAK;IAC7BnD,cAAc,CAACmD,GAAG,CAAC;IACnBrD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0H,YAAY,GAAGA,CAAA,KAAM;IACzB1H,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMyH,kBAAkB,GAAG,MAAOH,KAAK,IAAK;IAC1C,IAAI;MACFtI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0I,WAAW,GAAG7I,IAAI,CAAC+G,IAAI,CAACzC,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK8D,KAAK,CAAC;MACtD,MAAMK,SAAS,GAAGD,WAAW,CAACxF,MAAM,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;;MAEzE;MACA;MACA;;MAEA;MACApD,OAAO,CAACD,IAAI,CAACqE,GAAG,CAACC,GAAG,IAClBA,GAAG,CAACK,EAAE,KAAK8D,KAAK,GACZ;QAAC,GAAGnE,GAAG;QAAEjB,MAAM,EAAEyF;MAAS,CAAC,GAC3BxE,GACN,CAAC,CAAC;MAEFjE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,4BAA4B,EAAE6B,GAAG,CAAC;MAChD5B,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACC,MAAM4I,eAAe,GAAGA,CAAA,KAAM;IAC9BpI,cAAc,CAAC,IAAI,CAAC;IACpBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACC,MAAMuI,UAAU,GAAGA,CAAA,KAAM;IACzBrI,cAAc,CAAC,KAAK,CAAC;IACrBF,cAAc,CAAC,IAAI,CAAC;IACpB;IACA2B,aAAa,CAAC;MACZC,SAAS,EAAE,EAAE;MACbP,IAAI,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;MAC9BC,UAAU,EAAEnB,iBAAiB,CAAC,CAAC;MAC/BoB,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,iBAAiB;MAAE;MAChCC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,gBAAgB;MAAE;MAC5BC,gBAAgB,EAAE,aAAa;MAC/BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAE,IAAI;MACpBC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,IAAI;MAClB;MACAC,MAAM,EAAE,QAAQ;MAChBC,GAAG,EAAE;IACP,CAAC,CAAC;IACF;IACAE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,mBAAmB,CAAC,IAAI,CAAC;IACzBE,cAAc,CAAC,IAAI,CAAC;IACpB7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMkI,iBAAiB,GAAIC,IAAI,IAAK;IAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB;IACA;IACA,OAAOA,IAAI,CACRC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAAA,CAC5BjB,IAAI,CAAC,CAAC;EACX,CAAC;EAED,MAAMkB,gBAAgB,GAAIlC,CAAC,IAAK;IAC9B,MAAM;MAAEmC,IAAI;MAAEjC,KAAK;MAAEkC,IAAI;MAAEC;IAAQ,CAAC,GAAGrC,CAAC,CAACC,MAAM;;IAE/C;IACA,IAAIkC,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,iBAAiB,EAAE;MACtDjH,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAACkH,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGnC,KAAK,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM;MACPhF,aAAa,CAAC;QACZ,GAAGD,UAAU;QACb,CAACkH,IAAI,GAAGC,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGnC;MAC1C,CAAC,CAAC;IACF;EACF,CAAC;EAED,MAAMoC,iBAAiB,GAAGA,CAACtC,CAAC,EAAEuC,SAAS,KAAK;IAC1C,MAAMC,IAAI,GAAGxC,CAAC,CAACC,MAAM,CAACwC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;QACpB,IAAIL,SAAS,KAAK,gBAAgB,EAAE;UAClCjG,kBAAkB,CAACoG,MAAM,CAACG,MAAM,CAAC;UACjC3H,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEc,cAAc,EAAEyG;UAAK,CAAC,CAAC;QACxD,CAAC,MAAM,IAAID,SAAS,KAAK,oBAAoB,EAAE;UAC7C/F,mBAAmB,CAACkG,MAAM,CAACG,MAAM,CAAC;UAClC3H,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEe,kBAAkB,EAAEwG;UAAK,CAAC,CAAC;QAC5D,CAAC,MAAM,IAAID,SAAS,KAAK,cAAc,EAAE;UACvC7F,cAAc,CAACgG,MAAM,CAACG,MAAM,CAAC;UAC7B3H,aAAa,CAAC;YAAE,GAAGD,UAAU;YAAEiB,YAAY,EAAEsG;UAAK,CAAC,CAAC;QACtD;MACF,CAAC;MACDE,MAAM,CAACI,aAAa,CAACN,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAIpF,OAAO,IAAK;IACvC3C,OAAO,CAACmE,GAAG,CAAC,mBAAmB,EAAExB,OAAO,CAAC;;IAEzC;IACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC1B,YAAY,EAAE;MACrCjB,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;;IAEA;IACAgC,aAAa,CAAC;MACZ,GAAGD,UAAU;MACbgB,YAAY,EAAE0B,OAAO,CAAC1B,YAAY;MAClCC,YAAY,EAAE,IAAI;MAAE;MACpB4D,gBAAgB,EAAEnC,OAAO,CAACmC,gBAAgB,CAAC;IAC7C,CAAC,CAAC;;IAEF;IACA,IAAInC,OAAO,CAACmC,gBAAgB,EAAE;MAC5BpD,cAAc,CAACiB,OAAO,CAACmC,gBAAgB,CAAC;IAC1C,CAAC,MAAM;MACLpD,cAAc,CAAC,IAAI,CAAC;IACtB;;IAEA;IACA7C,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMmJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtG,cAAc,CAAC,IAAI,CAAC;IACpB;IACA,IAAIzB,UAAU,CAACiB,YAAY,EAAE;MAC3BhB,aAAa,CAAC;QACZ,GAAGD,UAAU;QACbiB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM+G,gBAAgB,GAAG,MAAOjD,CAAC,IAAK;IACpCA,CAAC,CAACkD,cAAc,CAAC,CAAC;;IAElB;IACAlI,OAAO,CAACmE,GAAG,CAAC,sCAAsC,CAAC;IACnDnE,OAAO,CAACmE,GAAG,CAAC,sBAAsB,EAAElE,UAAU,CAAC;IAC/CD,OAAO,CAACmE,GAAG,CAAC,eAAe,EAAE7F,WAAW,CAAC;;IAEzC;IACA,IAAI,CAAC2B,UAAU,CAACE,SAAS,IAAI,CAACF,UAAU,CAACE,SAAS,CAAC6F,IAAI,CAAC,CAAC,EAAE;MACzD7H,QAAQ,CAAC,uBAAuB,CAAC;MACjC;IACF;IAEA,IAAI,CAAC8B,UAAU,CAACgB,YAAY,IAAI,CAAChB,UAAU,CAACgB,YAAY,CAAC+E,IAAI,CAAC,CAAC,EAAE;MAC/D7H,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;;IAEA;IACA,IAAI8B,UAAU,CAACM,aAAa,IAAIN,UAAU,CAACM,aAAa,CAACyF,IAAI,CAAC,CAAC,EAAE;MAC/D,MAAMmC,UAAU,GAAG,4BAA4B;MAC/C,MAAMC,QAAQ,GAAG,+DAA+D;MAEhF,MAAMlD,KAAK,GAAGjF,UAAU,CAACM,aAAa,CAACyF,IAAI,CAAC,CAAC;MAC7C,IAAI,CAACmC,UAAU,CAACE,IAAI,CAACnD,KAAK,CAAC,IAAI,CAACkD,QAAQ,CAACC,IAAI,CAACnD,KAAK,CAAC,EAAE;QACpD/G,QAAQ,CAAC,mDAAmD,CAAC;QAC7D;MACF;IACF;;IAEA;IACA,IAAImK,qBAAqB,GAAGrI,UAAU,CAACU,gBAAgB;IACvD,IAAI,CAAC2H,qBAAqB,EAAE;MAC1BA,qBAAqB,GAAG,wBAAwB;IAClD;IAEA,IAAI;MACFrK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA+B,OAAO,CAACmE,GAAG,CAAC,gCAAgC,EAAElE,UAAU,CAACE,SAAS,CAAC;MACnE,MAAMoI,cAAc,GAAGxB,iBAAiB,CAAC9G,UAAU,CAACE,SAAS,CAAC;MAC9DH,OAAO,CAACmE,GAAG,CAAC,+BAA+B,EAAEoE,cAAc,CAAC;MAE5D,MAAMC,aAAa,GAAG;QACpB,GAAGvI,UAAU;QACbE,SAAS,EAAEoI,cAAc;QACzBzH,eAAe,EAAEiG,iBAAiB,CAAC9G,UAAU,CAACa,eAAe,CAAC;QAC9DH,gBAAgB,EAAE2H,qBAAqB;QACvC;QACAjI,UAAU,EAAE/B,WAAW,GAAG2B,UAAU,CAACI,UAAU,GAAIJ,UAAU,CAACI,UAAU,IAAInB,iBAAiB,CAAC;MAChG,CAAC;MAEDc,OAAO,CAACmE,GAAG,CAAC,sBAAsB,EAAEqE,aAAa,CAAC;;MAElD;MACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC7B;MACF,MAAMC,QAAQ,GAAG,CACf,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAC7D,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAC5E,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,KAAK,CACrD;;MAED;MACAA,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAI;QACtB,IAAIL,aAAa,CAACK,GAAG,CAAC,KAAKC,SAAS,IAAIN,aAAa,CAACK,GAAG,CAAC,KAAK,IAAI,EAAE;UACnE;UACA,IAAIA,GAAG,KAAK,YAAY,EAAE;YACxB,IAAI;cACF,IAAIzC,KAAK,CAACC,OAAO,CAACmC,aAAa,CAACK,GAAG,CAAC,CAAC,EAAE;gBACrC;gBACA,MAAME,cAAc,GAAGP,aAAa,CAACK,GAAG,CAAC,CAAC1G,GAAG,CAAC6G,KAAK,IAAIxD,MAAM,CAACwD,KAAK,CAAC,CAAChD,IAAI,CAAC,CAAC,CAAC;gBAC5EyC,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE3C,IAAI,CAACgD,SAAS,CAACH,cAAc,CAAC,CAAC;cACtD,CAAC,MAAM;gBACL;gBACAN,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE3C,IAAI,CAACgD,SAAS,CAAC,CAAC1D,MAAM,CAACgD,aAAa,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;cACpE;YACF,CAAC,CAAC,OAAO9I,GAAG,EAAE;cACZC,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,EAAE6B,GAAG,CAAC;cAClD0I,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9B;UACF,CAAC,MAAM;YACL7I,OAAO,CAACmE,GAAG,CAAC,sBAAsB0E,GAAG,MAAML,aAAa,CAACK,GAAG,CAAC,EAAE,CAAC;YAChEJ,QAAQ,CAACQ,MAAM,CAACJ,GAAG,EAAEL,aAAa,CAACK,GAAG,CAAC,CAAC;UAC1C;QACF;MACF,CAAC,CAAC;;MAEF;MACA,IAAIL,aAAa,CAACzH,cAAc,IAAIyH,aAAa,CAACzH,cAAc,YAAYoI,IAAI,EAAE;QAChFV,QAAQ,CAACQ,MAAM,CAAC,gBAAgB,EAAET,aAAa,CAACzH,cAAc,CAAC;MACjE;MAEA,IAAIyH,aAAa,CAACxH,kBAAkB,IAAIwH,aAAa,CAACxH,kBAAkB,YAAYmI,IAAI,EAAE;QACxFV,QAAQ,CAACQ,MAAM,CAAC,oBAAoB,EAAET,aAAa,CAACxH,kBAAkB,CAAC;MACzE;MAEA,IAAIwH,aAAa,CAACtH,YAAY,IAAIsH,aAAa,CAACtH,YAAY,YAAYiI,IAAI,EAAE;QAC5EV,QAAQ,CAACQ,MAAM,CAAC,cAAc,EAAET,aAAa,CAACtH,YAAY,CAAC;MAC7D;;MAEA;MACA,IAAIO,WAAW,IAAI,CAAC+G,aAAa,CAACtH,YAAY,EAAE;QAC9CuH,QAAQ,CAACQ,MAAM,CAAC,2BAA2B,EAAExH,WAAW,CAAC;MAC3D;;MAEA;MACA,IAAI+G,aAAa,CAAC1D,gBAAgB,EAAE;QAClC2D,QAAQ,CAACQ,MAAM,CAAC,kBAAkB,EAAET,aAAa,CAAC1D,gBAAgB,CAAC;MACrE;;MAEA;MACA9E,OAAO,CAACmE,GAAG,CAAC,oBAAoB,CAAC;MACjC,KAAK,IAAIiF,IAAI,IAAIX,QAAQ,CAACY,OAAO,CAAC,CAAC,EAAE;QACnCrJ,OAAO,CAACmE,GAAG,CAACiF,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC;MACApJ,OAAO,CAACmE,GAAG,CAAC,4BAA4B,EAAEsE,QAAQ,CAACa,GAAG,CAAC,WAAW,CAAC,CAAC;;MAEpE;MACA,IAAIhL,WAAW,EAAE;QACf;QACA,IAAI;UACF,MAAMhB,UAAU,CAACQ,IAAI,CAACyL,MAAM,CAACjL,WAAW,CAACmE,EAAE,EAAEgG,QAAQ,CAAC;UACtD;QACF,CAAC,CAAC,OAAOe,QAAQ,EAAE;UACjB;UACA,MAAMA,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAMnM,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGwH,YAAY,CAACvH,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACdtB,GAAG,EAAEgB,GAAG,CAAChB,GAAG,IAAI,KAAK;UAAE;UACvBuB,OAAO,EAAEP,GAAG,CAACnB,YAAY;UACzB2B,QAAQ,EAAER,GAAG,CAACjC,SAAS;UACvB0C,QAAQ,EAAET,GAAG,CAAC5B,WAAW;UACzBsC,QAAQ,EAAEV,GAAG,CAAC1B,QAAQ;UACtBC,gBAAgB,EAAEyB,GAAG,CAACzB,gBAAgB;UACtCoC,QAAQ,EAAEX,GAAG,CAAC7B,aAAa;UAC3ByC,MAAM,EAAGZ,GAAG,CAACxB,UAAU,IAAIwB,GAAG,CAACvB,UAAU,GACrC,GAAGuB,GAAG,CAACxB,UAAU,MAAMwB,GAAG,CAACvB,UAAU,EAAE,GACvCuB,GAAG,CAACxB,UAAU,GACZ,GAAGwB,GAAG,CAACxB,UAAU,EAAE,GACnBwB,GAAG,CAACvB,UAAU,GACZ,GAAGuB,GAAG,CAACvB,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClB8B,UAAU,EAAEb,GAAG,CAAC/B,UAAU,GAAG,IAAIlB,IAAI,CAACiD,GAAG,CAAC/B,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvE+C,GAAG,CAACG,WAAW,GAAG,IAAIpD,IAAI,CAACiD,GAAG,CAACG,WAAW,CAAC,CAACnD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvF8D,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5B9C,QAAQ,EAAE8B,GAAG,CAAC9B,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAM+C,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrE,IAAI,CAACqE,CAAC,CAACP,UAAU,CAAC,GAAG,IAAI9D,IAAI,CAACoE,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGlF,OAAO,CAACsF,UAAU,CAAC;MACrB,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAM/F,UAAU,CAACQ,IAAI,CAAC4L,MAAM,CAACjB,QAAQ,CAAC;UACtC;QACF,CAAC,CAAC,OAAOe,QAAQ,EAAE;UAAA,IAAAG,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;UACjB;UACA7J,OAAO,CAAC9B,KAAK,CAAC,oBAAoB,EAAE;YAClC4L,OAAO,EAAEN,QAAQ,CAACM,OAAO;YACzB3I,MAAM,GAAAwI,kBAAA,GAAEH,QAAQ,CAACzH,QAAQ,cAAA4H,kBAAA,uBAAjBA,kBAAA,CAAmBxI,MAAM;YACjC4I,UAAU,GAAAH,mBAAA,GAAEJ,QAAQ,CAACzH,QAAQ,cAAA6H,mBAAA,uBAAjBA,mBAAA,CAAmBG,UAAU;YACzC7H,IAAI,GAAA2H,mBAAA,GAAEL,QAAQ,CAACzH,QAAQ,cAAA8H,mBAAA,uBAAjBA,mBAAA,CAAmB3H;UAC3B,CAAC,CAAC;UACF,MAAMsH,QAAQ,CAAC,CAAC;QAClB;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAMnM,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;QACnD,MAAMC,aAAa,GAAGwH,YAAY,CAACvH,IAAI,CAACC,GAAG,CAACC,GAAG,KAAK;UAClDK,EAAE,EAAEL,GAAG,CAACM,MAAM;UACdtB,GAAG,EAAEgB,GAAG,CAAChB,GAAG,IAAI,KAAK;UAAE;UACvBuB,OAAO,EAAEP,GAAG,CAACnB,YAAY;UACzB2B,QAAQ,EAAER,GAAG,CAACjC,SAAS;UACvB0C,QAAQ,EAAET,GAAG,CAAC5B,WAAW;UACzBsC,QAAQ,EAAEV,GAAG,CAAC1B,QAAQ;UACtBC,gBAAgB,EAAEyB,GAAG,CAACzB,gBAAgB;UACtCoC,QAAQ,EAAEX,GAAG,CAAC7B,aAAa;UAC3ByC,MAAM,EAAGZ,GAAG,CAACxB,UAAU,IAAIwB,GAAG,CAACvB,UAAU,GACrC,GAAGuB,GAAG,CAACxB,UAAU,MAAMwB,GAAG,CAACvB,UAAU,EAAE,GACvCuB,GAAG,CAACxB,UAAU,GACZ,GAAGwB,GAAG,CAACxB,UAAU,EAAE,GACnBwB,GAAG,CAACvB,UAAU,GACZ,GAAGuB,GAAG,CAACvB,UAAU,EAAE,GACnB,EAAE;UACVM,MAAM,EAAE,QAAQ;UAAE;UAClB8B,UAAU,EAAEb,GAAG,CAAC/B,UAAU,GAAG,IAAIlB,IAAI,CAACiD,GAAG,CAAC/B,UAAU,CAAC,CAACjB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACvE+C,GAAG,CAACG,WAAW,GAAG,IAAIpD,IAAI,CAACiD,GAAG,CAACG,WAAW,CAAC,CAACnD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;UACvF8D,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;UAAE;UAC5B9C,QAAQ,EAAE8B,GAAG,CAAC9B,QAAQ,CAAC;QACzB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAM+C,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrE,IAAI,CAACqE,CAAC,CAACP,UAAU,CAAC,GAAG,IAAI9D,IAAI,CAACoE,CAAC,CAACN,UAAU,CAAC,CAAC;QACrGlF,OAAO,CAACsF,UAAU,CAAC;MACrB;MAEAlF,QAAQ,CAAC,IAAI,CAAC;MACd;MACA2I,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAO/G,GAAG,EAAE;MAAA,IAAAiK,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZnK,OAAO,CAAC9B,KAAK,CAAC,mBAAmB,EAAE6B,GAAG,CAAC;MACvC;MACA,MAAMqK,YAAY,GAAG,EAAAJ,aAAA,GAAAjK,GAAG,CAACgC,QAAQ,cAAAiI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAc9H,IAAI,cAAA+H,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,OAAAI,cAAA,GAC5BnK,GAAG,CAACgC,QAAQ,cAAAmI,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchI,IAAI,cAAAiI,mBAAA,uBAAlBA,mBAAA,CAAoBjM,KAAK,MACxBI,WAAW,GAAG,sBAAsB,GAAG,sBAAsB,CAAC;MACnFH,QAAQ,CAACiM,YAAY,CAAC;IACxB,CAAC,SAAS;MACRnM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoM,YAAY,GAAGvM,IAAI,CAACgG,MAAM,CAAC1B,GAAG,IAAI;IACtC,MAAMkI,aAAa,GAAIlI,GAAG,CAACO,OAAO,IAAIP,GAAG,CAACO,OAAO,CAAC4H,WAAW,CAAC,CAAC,CAACxE,QAAQ,CAAC3H,UAAU,CAACmM,WAAW,CAAC,CAAC,CAAC,IAC3EnI,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAAC2H,WAAW,CAAC,CAAC,CAACxE,QAAQ,CAAC3H,UAAU,CAACmM,WAAW,CAAC,CAAC,CAAE;IAErG,OAAOD,aAAa;EACtB,CAAC,CAAC;;EAEF;EACAlO,SAAS,CAAC,MAAM;IACd,MAAMoO,kBAAkB,GAAIC,KAAK,IAAK;MACpC,MAAMC,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;MAC1E,IAAIF,eAAe,IAAI,CAACA,eAAe,CAACG,QAAQ,CAACJ,KAAK,CAACxF,MAAM,CAAC,EAAE;QAC9DpG,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF,CAAC;IAED,IAAID,mBAAmB,EAAE;MACvB+L,QAAQ,CAACG,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXG,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAAC5L,mBAAmB,CAAC,CAAC;EAEzB,oBACEpB,OAAA;IAAKwN,SAAS,EAAC,sBAAsB;IAAAC,QAAA,GAElCnM,iBAAiB,iBAChBtB,OAAA;MAAKwN,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCzN,OAAA;QAAKwN,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCzN,OAAA;UAAKwN,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCzN,OAAA,CAAClB,eAAe;YAAC4O,IAAI,EAAE9N,qBAAsB;YAAC4N,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxE9N,OAAA;YAAAyN,QAAA,EAAI;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACN9N,OAAA;UAAKwN,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCzN,OAAA;YAAAyN,QAAA,EAAG;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxD9N,OAAA;YAAAyN,QAAA,eAAGzN,OAAA;cAAAyN,QAAA,EAASjM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4D;YAAQ;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/C9N,OAAA;YAAAyN,QAAA,EAAG;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACN9N,OAAA;UAAKwN,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCzN,OAAA;YACEwN,SAAS,EAAC,mBAAmB;YAC7BO,OAAO,EAAE9E,YAAa;YAAAwE,QAAA,EACvB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9N,OAAA;YACEwN,SAAS,EAAC,oBAAoB;YAC9BO,OAAO,EAAEA,CAAA,KAAMjF,eAAe,CAACtH,WAAW,CAACyD,EAAE,CAAE;YAAAwI,QAAA,EAChD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAED9N,OAAA;MAAKwN,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzN,OAAA;QAAIwN,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5C9N,OAAA;QAAKwN,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCzN,OAAA;UAAQwN,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAEA,CAAA,KAAM;YAChDtN,UAAU,CAAC,IAAI,CAAC;YAChB,MAAM6D,SAAS,GAAG,MAAAA,CAAA,KAAY;cAC5B,IAAI;gBACF,MAAMC,QAAQ,GAAG,MAAMzE,UAAU,CAACQ,IAAI,CAACkE,MAAM,CAAC,CAAC;;gBAE/C;gBACA,MAAMC,aAAa,GAAGF,QAAQ,CAACG,IAAI,CAACC,GAAG,CAACC,GAAG,IAAI;kBAC7C;kBACA,IAAIC,SAAS;kBACb,IAAID,GAAG,CAACE,UAAU,EAAE;oBAClBD,SAAS,GAAGD,GAAG,CAACE,UAAU;kBAC5B,CAAC,MAAM,IAAIF,GAAG,CAAC/B,UAAU,EAAE;oBACzBgC,SAAS,GAAGD,GAAG,CAAC/B,UAAU;kBAC5B,CAAC,MAAM,IAAI+B,GAAG,CAACG,WAAW,EAAE;oBAC1BF,SAAS,GAAGD,GAAG,CAACG,WAAW;kBAC7B,CAAC,MAAM;oBACLF,SAAS,GAAG,IAAIlD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;kBACtC;;kBAEA;kBACA,IAAIoD,aAAa,GAAG,EAAE;kBACtB,IAAIJ,GAAG,CAACxB,UAAU,IAAIwB,GAAG,CAACvB,UAAU,EAAE;oBACpC2B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,MAAMwB,GAAG,CAACvB,UAAU,EAAE;kBACzD,CAAC,MAAM,IAAIuB,GAAG,CAACxB,UAAU,EAAE;oBACzB4B,aAAa,GAAG,GAAGJ,GAAG,CAACxB,UAAU,EAAE;kBACrC,CAAC,MAAM,IAAIwB,GAAG,CAACvB,UAAU,EAAE;oBACzB2B,aAAa,GAAG,GAAGJ,GAAG,CAACvB,UAAU,EAAE;kBACrC,CAAC,MAAM;oBACL2B,aAAa,GAAG,EAAE;kBACpB;kBACE,OAAO;oBACPC,EAAE,EAAEL,GAAG,CAACM,MAAM;oBACdtB,GAAG,EAAEgB,GAAG,CAAChB,GAAG,IAAI,KAAK;oBACrBuB,OAAO,EAAEP,GAAG,CAACnB,YAAY;oBACzB2B,QAAQ,EAAER,GAAG,CAACjC,SAAS;oBACvB0C,QAAQ,EAAET,GAAG,CAAC5B,WAAW;oBACzBsC,QAAQ,EAAEV,GAAG,CAAC1B,QAAQ;oBACtBC,gBAAgB,EAAEyB,GAAG,CAACzB,gBAAgB;oBACtCoC,QAAQ,EAAEX,GAAG,CAAC7B,aAAa;oBAC3ByC,MAAM,EAAER,aAAa;oBACrBrB,MAAM,EAAE,QAAQ;oBAChB8B,UAAU,EAAEZ,SAAS;oBACrBa,aAAa,EAAEd,GAAG,CAAC/B,UAAU,IAAI+B,GAAG,CAACG,WAAW,IAAIH,GAAG,CAACE,UAAU;oBAClEa,KAAK,EAAEf,GAAG,CAACgB,UAAU,IAAI,CAAC;oBAC1B9C,QAAQ,EAAE8B,GAAG,CAAC9B;kBAChB,CAAC;gBACH,CAAC,CAAC;;gBAEF;gBACA,MAAM+C,UAAU,GAAG,CAAC,GAAGpB,aAAa,CAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrE,IAAI,CAACqE,CAAC,CAACP,UAAU,CAAC,GAAG,IAAI9D,IAAI,CAACoE,CAAC,CAACN,UAAU,CAAC,CAAC;gBACrGlF,OAAO,CAACsF,UAAU,CAAC;gBACnBlF,QAAQ,CAAC,IAAI,CAAC;cAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;gBACZC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAE6B,GAAG,CAAC;gBAC1C5B,QAAQ,CAAC,iCAAiC,CAAC;cAC7C,CAAC,SAAS;gBACRF,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC;YAED6D,SAAS,CAAC,CAAC;UACb,CAAE;UAAC0J,QAAQ,EAAExN,OAAQ;UAAAiN,QAAA,gBACnBzN,OAAA,CAAClB,eAAe;YAAC4O,IAAI,EAAE7N;UAAO;YAAA8N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC9N,OAAA;YAAAyN,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACT9N,OAAA;UAAQwN,SAAS,EAAC,gBAAgB;UAACO,OAAO,EAAE1E,eAAgB;UAAC2E,QAAQ,EAAExN,OAAQ;UAAAiN,QAAA,gBAC7EzN,OAAA,CAAClB,eAAe;YAAC4O,IAAI,EAAE3O;UAAO;YAAA4O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC9N,OAAA;YAAAyN,QAAA,EAAM;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELpN,KAAK,iBACJV,OAAA;MAAKwN,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzN,OAAA,CAAClB,eAAe;QAAC4O,IAAI,EAAEvO;MAAoB;QAAAwO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9C9N,OAAA;QAAAyN,QAAA,EAAO/M;MAAK;QAAAiN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAED9N,OAAA;MAAKwN,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCzN,OAAA;QAAKwN,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BzN,OAAA;UAAKwN,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCzN,OAAA,CAAClB,eAAe;YAAC4O,IAAI,EAAExO,QAAS;YAACsO,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAAU,eAAA9N,OAAA;YACrE4J,IAAI,EAAC,MAAM;YACXqE,WAAW,EAAC,kCAAkC;YAC9CvG,KAAK,EAAE9G,UAAW;YAClBsN,QAAQ,EAAE3G,kBAAmB;YAC7BiG,SAAS,EAAC,cAAc;YACxBW,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAEN9N,OAAA;MAAKwN,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BjN,OAAO,gBACNR,OAAA;QAAKwN,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzN,OAAA,CAAClB,eAAe;UAAC4O,IAAI,EAAEhO,SAAU;UAAC2O,IAAI;UAACC,IAAI,EAAC;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnD9N,OAAA;UAAAyN,QAAA,EAAM;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,gBAEN9N,OAAA,CAAAE,SAAA;QAAAuN,QAAA,eACAzN,OAAA;UAAOwN,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC3BzN,OAAA;YAAAyN,QAAA,eACEzN,OAAA;cAAAyN,QAAA,gBACEzN,OAAA;gBAAAyN,QAAA,EAAI;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACZ9N,OAAA;gBAAAyN,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb9N,OAAA;gBAAAyN,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB9N,OAAA;gBAAAyN,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxB9N,OAAA;gBAAAyN,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf9N,OAAA;gBAAAyN,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9N,OAAA;gBAAAyN,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB9N,OAAA;gBAAAyN,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd9N,OAAA;gBAAAyN,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9N,OAAA;YAAAyN,QAAA,EACGZ,YAAY,CAAChG,MAAM,GAAG,CAAC,GACtBgG,YAAY,CAAClI,GAAG,CAACC,GAAG,iBAClB5E,OAAA;cAAAyN,QAAA,gBACEzN,OAAA;gBAAAyN,QAAA,GACG7I,GAAG,CAAChB,GAAG,iBACN5D,OAAA;kBAAMwN,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACzBzN,OAAA,CAAClB,eAAe;oBAAC4O,IAAI,EAAEpO,MAAO;oBAAC6O,KAAK,EAAE;sBAAEI,WAAW,EAAE;oBAAM;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAElE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP,eACvB9N,OAAA;kBAAKwN,SAAS,EAAC,cAAc;kBAACgB,KAAK,EAAE5J,GAAG,CAACQ,QAAS;kBAAAqI,QAAA,EAC/C7I,GAAG,CAACQ,QAAQ,IAAIR,GAAG,CAACQ,QAAQ,CAACyB,MAAM,GAAG,EAAE,GACrCjC,GAAG,CAACQ,QAAQ,CAACqJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GACjC7J,GAAG,CAACQ;gBAAQ;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACgB9N,OAAA;kBAAKwN,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE7I,GAAG,CAACO;gBAAO;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACL9N,OAAA;gBAAAyN,QAAA,EAAK7I,GAAG,CAACU;cAAQ;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvB9N,OAAA;gBAAAyN,QAAA,eACEzN,OAAA;kBAAKwN,SAAS,EAAC,kBAAkB;kBAACgB,KAAK,EAAE5J,GAAG,CAACzB,gBAAiB;kBAAAsK,QAAA,EAC3D7I,GAAG,CAACzB,gBAAgB,IAAI;gBAAe;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9N,OAAA;gBAAAyN,QAAA,eACEzN,OAAA;kBAAKwN,SAAS,EAAC,UAAU;kBAACgB,KAAK,EAAE5J,GAAG,CAACW,QAAS;kBAAAkI,QAAA,EAC3C7I,GAAG,CAACW,QAAQ,gBACXvF,OAAA;oBAAG0O,IAAI,EAAE,UAAU9J,GAAG,CAACW,QAAQ,EAAG;oBAACiI,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACtD7I,GAAG,CAACW,QAAQ,CAACsB,MAAM,GAAG,EAAE,GAAGjC,GAAG,CAACW,QAAQ,CAACkJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG7J,GAAG,CAACW;kBAAQ;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,GAEJ;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9N,OAAA;gBAAAyN,QAAA,EAAK7I,GAAG,CAACY;cAAM;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrB9N,OAAA;gBAAAyN,QAAA,EAAK3L,iBAAiB,CAAC8C,GAAG,CAAC9B,QAAQ;cAAC;gBAAA6K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxC9N,OAAA;gBAAAyN,QAAA,EAAK3L,iBAAiB,CAAC8C,GAAG,CAACa,UAAU;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9C9N,OAAA;gBAAAyN,QAAA,eACEzN,OAAA;kBAAKwN,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BzN,OAAA,CAAClB,eAAe;oBAAC4O,IAAI,EAAErO,KAAM;oBAACmO,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtDlJ,GAAG,CAACe,KAAK;gBAAA;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9N,OAAA;gBAAAyN,QAAA,eACEzN,OAAA;kBAAKmO,KAAK,EAAE;oBAAEQ,OAAO,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,gBAC9BzN,OAAA;oBACEwN,SAAS,EAAC,2BAA2B;oBACrCO,OAAO,EAAEA,CAAA,KAAMpG,aAAa,CAAC/C,GAAG,CAAE;oBAClC4J,KAAK,EAAC,UAAU;oBAChBR,QAAQ,EAAExN,OAAQ;oBAAAiN,QAAA,eAElBzN,OAAA,CAAClB,eAAe;sBAAC4O,IAAI,EAAE1O;oBAAO;sBAAA2O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eAET9N,OAAA;oBACEwN,SAAS,EAAC,6BAA6B;oBACvCO,OAAO,EAAEA,CAAA,KAAM/E,aAAa,CAACpE,GAAG,CAAE;oBAClC4J,KAAK,EAAC,YAAY;oBAClBR,QAAQ,EAAExN,OAAQ;oBAAAiN,QAAA,eAElBzN,OAAA,CAAClB,eAAe;sBAAC4O,IAAI,EAAEzO;oBAAQ;sBAAA0O,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA7DElJ,GAAG,CAACK,EAAE;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8DX,CACL,CAAC,gBAEF9N,OAAA;cAAAyN,QAAA,eACEzN,OAAA;gBAAI4O,OAAO,EAAC,GAAG;gBAACpB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EACxC7M,UAAU,GACT,oCAAoC,GACpC;cAAmB;gBAAA+M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,gBACN;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL9M,WAAW,iBACVhB,OAAA;MAAKwN,SAAS,EAAC,eAAe;MAACO,OAAO,EAAGvG,CAAC,IAAK;QAC7C;QACA,IAAIA,CAAC,CAACC,MAAM,CAAC+F,SAAS,KAAK,eAAe,EAAE;UAC1ClE,UAAU,CAAC,CAAC;QACd;MACF,CAAE;MAAAmE,QAAA,eACAzN,OAAA;QAAKwN,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzN,OAAA;UAAKwN,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzN,OAAA;YAAAyN,QAAA,EAAK3M,WAAW,GAAG,UAAU,GAAG;UAAa;YAAA6M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnD9N,OAAA;YAAQwN,SAAS,EAAC,cAAc;YAACO,OAAO,EAAEzE,UAAW;YAAAmE,QAAA,eACnDzN,OAAA,CAAClB,eAAe;cAAC4O,IAAI,EAAEnO;YAAQ;cAAAoO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9N,OAAA;UAAKwN,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BzN,OAAA;YAAM6O,QAAQ,EAAEpE,gBAAiB;YAAAgD,QAAA,gBAC/BzN,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,WAAW;kBAAArB,QAAA,GAAC,YAAU,eAAAzN,OAAA;oBAAMmO,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpF9N,OAAA;kBACE4J,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,WAAW;kBACd0E,IAAI,EAAC,WAAW;kBAChBjC,KAAK,EAAEjF,UAAU,CAACE,SAAU;kBAC5BuL,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBACRf,WAAW,EAAC;gBAAiB;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACR9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAAyN,QAAA,GAAO,UAAQ,eAAAzN,OAAA;oBAAMmO,KAAK,EAAE;sBAAEY,KAAK,EAAE;oBAAM,CAAE;oBAAAtB,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9D9N,OAAA;kBAAKwN,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BzN,OAAA;oBAAKwN,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCzN,OAAA;sBACE4J,IAAI,EAAC,QAAQ;sBACb4D,SAAS,EAAC,uBAAuB;sBACjCO,OAAO,EAAEA,CAAA,KAAM1M,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;sBAAAqM,QAAA,GAE3DhL,UAAU,CAACgB,YAAY,GAAGhB,UAAU,CAACgB,YAAY,GAAG,kBAAkB,eACvEzD,OAAA,CAAClB,eAAe;wBAAC4O,IAAI,EAAE/N;sBAAc;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,EAER1M,mBAAmB,iBAClBpB,OAAA;sBAAKwN,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACnCvM,SAAS,IAAIA,SAAS,CAAC2F,MAAM,GAAG,CAAC,GAChC3F,SAAS,CAACyD,GAAG,CAACQ,OAAO,iBACnBnF,OAAA;wBAEEwN,SAAS,EAAC,cAAc;wBACxBO,OAAO,EAAEA,CAAA,KAAM;0BACbxD,mBAAmB,CAACpF,OAAO,CAAC;wBAC9B,CAAE;wBAAAsI,QAAA,gBAEFzN,OAAA;0BAAKwN,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAC/BtI,OAAO,CAACmC,gBAAgB,gBACvBtH,OAAA;4BAAKiP,GAAG,EAAE9J,OAAO,CAACmC,gBAAiB;4BAAC4H,GAAG,EAAE/J,OAAO,CAAC1B;0BAAa;4BAAAkK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAEjE9N,OAAA,CAAClB,eAAe;4BAAC4O,IAAI,EAAElO;0BAAW;4BAAAmO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACrC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN9N,OAAA;0BAAAyN,QAAA,EAAOtI,OAAO,CAAC1B;wBAAY;0BAAAkK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAb9B3I,OAAO,CAACgK,UAAU;wBAAAxB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAcpB,CACN,CAAC,gBAEF9N,OAAA;wBAAKwN,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAC1D,eACD9N,OAAA;wBAAKwN,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,gBACnCzN,OAAA;0BAAAyN,QAAA,EAAO;wBAA+B;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eAC9C9N,OAAA;0BACE4J,IAAI,EAAC,MAAM;0BACXqE,WAAW,EAAC,oBAAoB;0BAChCvG,KAAK,EAAEjF,UAAU,CAACgB,YAAY,IAAI,EAAG;0BACrCyK,QAAQ,EAAG1G,CAAC,IAAK;4BACf9E,aAAa,CAAC;8BACZ,GAAGD,UAAU;8BACbgB,YAAY,EAAE+D,CAAC,CAACC,MAAM,CAACC;4BACzB,CAAC,CAAC;0BACJ;wBAAE;0BAAAiG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACF9N,OAAA;0BACE4J,IAAI,EAAC,QAAQ;0BACb4D,SAAS,EAAC,mBAAmB;0BAC7BO,OAAO,EAAEA,CAAA,KAAM1M,sBAAsB,CAAC,KAAK,CAAE;0BAAAoM,QAAA,EAC9C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAGL7J,WAAW,iBACVjE,OAAA;oBAAKwN,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCzN,OAAA;sBAAKiP,GAAG,EAAEhL,WAAY;sBAACiL,GAAG,EAAC;oBAAc;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5C9N,OAAA;sBACE4J,IAAI,EAAC,QAAQ;sBACb4D,SAAS,EAAC,oBAAoB;sBAC9BO,OAAO,EAAEvD,gBAAiB;sBAAAiD,QAAA,eAE1BzN,OAAA,CAAClB,eAAe;wBAAC4O,IAAI,EAAEnO;sBAAQ;wBAAAoO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN,EAGA,CAAC7J,WAAW,iBACXjE,OAAA;oBAAAyN,QAAA,gBACEzN,OAAA;sBACE4J,IAAI,EAAC,MAAM;sBACX3E,EAAE,EAAC,cAAc;sBACjB0E,IAAI,EAAC,cAAc;sBACnBuE,QAAQ,EAAG1G,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,cAAc,CAAE;sBACtD4H,MAAM,EAAC,SAAS;sBAChBjB,KAAK,EAAE;wBAAEQ,OAAO,EAAE;sBAAO;oBAAE;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACF9N,OAAA;sBACE4J,IAAI,EAAC,QAAQ;sBACb4D,SAAS,EAAC,mBAAmB;sBAC7BO,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAACkC,cAAc,CAAC,cAAc,CAAC,CAACC,KAAK,CAAC,CAAE;sBAAA7B,QAAA,gBAE/DzN,OAAA,CAAClB,eAAe;wBAAC4O,IAAI,EAAEjO;sBAAQ;wBAAAkO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,uBAEpC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,MAAM;kBAAArB,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClC9N,OAAA;kBACE4J,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,MAAM;kBACT0E,IAAI,EAAC,MAAM;kBACXjC,KAAK,EAAEjF,UAAU,CAACL,IAAK;kBACvB8L,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBACRO,GAAG,EAAC,MAAM;kBACVC,GAAG,EAAC;gBAAM;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,sBAAkB,eAAA9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACjDzN,OAAA;kBAAO8O,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1C9N,OAAA;kBACEiF,EAAE,EAAC,UAAU;kBACb0E,IAAI,EAAC,UAAU;kBACfjC,KAAK,EAAEjF,UAAU,CAACS,QAAS;kBAC3BgL,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBAAAvB,QAAA,gBAERzN,OAAA;oBAAQ0H,KAAK,EAAC,gBAAgB;oBAAA+F,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtD9N,OAAA;oBAAQ0H,KAAK,EAAC,gBAAgB;oBAAA+F,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtD9N,OAAA;oBAAQ0H,KAAK,EAAC,aAAa;oBAAA+F,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD9N,OAAA;oBAAQ0H,KAAK,EAAC,WAAW;oBAAA+F,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C9N,OAAA;oBAAQ0H,KAAK,EAAC,WAAW;oBAAA+F,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,kBAAkB;kBAAArB,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1D9N,OAAA;kBACEiF,EAAE,EAAC,kBAAkB;kBACrB0E,IAAI,EAAC,kBAAkB;kBACvBjC,KAAK,EAAEjF,UAAU,CAACU,gBAAiB;kBACnC+K,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBAAAvB,QAAA,EAEPtN,kBAAkB,CAACwE,GAAG,CAAC8K,MAAM,iBAC5BzP,OAAA;oBAAqB0H,KAAK,EAAE+H,MAAO;oBAAAhC,QAAA,EAAEgC;kBAAM,GAA9BA,MAAM;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiC,CACrD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9C9N,OAAA;kBACE4J,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAEjF,UAAU,CAACI,UAAW;kBAC7BqL,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,UAAU;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1C9N,OAAA;kBACE4J,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,UAAU;kBACb0E,IAAI,EAAC,UAAU;kBACfjC,KAAK,EAAEjF,UAAU,CAACK,QAAS;kBAC3BoL,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,eAAe;kBAAArB,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD9N,OAAA;kBACE4J,IAAI,EAAC,MAAM;kBACX3E,EAAE,EAAC,eAAe;kBAClB0E,IAAI,EAAC,eAAe;kBACpBjC,KAAK,EAAEjF,UAAU,CAACM,aAAc;kBAChCmL,QAAQ,EAAExE,gBAAiB;kBAC3BuE,WAAW,EAAC;gBAA0D;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD9N,OAAA;kBACE4J,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAEjF,UAAU,CAACW,UAAW;kBAC7B8K,QAAQ,EAAExE,gBAAiB;kBAC3BuE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,YAAY;kBAAArB,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClD9N,OAAA;kBACE4J,IAAI,EAAC,QAAQ;kBACb3E,EAAE,EAAC,YAAY;kBACf0E,IAAI,EAAC,YAAY;kBACjBjC,KAAK,EAAEjF,UAAU,CAACY,UAAW;kBAC7B6K,QAAQ,EAAExE,gBAAiB;kBAC3BuE,WAAW,EAAC,gBAAgB;kBAC5BsB,GAAG,EAAC,GAAG;kBACPG,IAAI,EAAC;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBzN,OAAA;gBAAKwN,SAAS,EAAC,2BAA2B;gBAACW,KAAK,EAAE;kBAACwB,SAAS,EAAE;gBAAM,CAAE;gBAAAlC,QAAA,eACpEzN,OAAA;kBAAOwN,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACnCzN,OAAA;oBACE4J,IAAI,EAAC,UAAU;oBACfD,IAAI,EAAC,KAAK;oBACVE,OAAO,EAAEpH,UAAU,CAACmB,GAAI;oBACxBsK,QAAQ,EAAExE;kBAAiB;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACF9N,OAAA;oBAAMwN,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC7BzN,OAAA,CAAClB,eAAe;sBAAC4O,IAAI,EAAEpO,MAAO;sBAACkO,SAAS,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,sBAExD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAO8O,OAAO,EAAC,aAAa;kBAAArB,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxD9N,OAAA;kBACEiF,EAAE,EAAC,aAAa;kBAChB0E,IAAI,EAAC,aAAa;kBAClBjC,KAAK,EAAEjF,UAAU,CAACO,WAAY;kBAC9BkL,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBAAAvB,QAAA,gBAERzN,OAAA;oBAAQ0H,KAAK,EAAC,iBAAiB;oBAAA+F,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxD9N,OAAA;oBAAQ0H,KAAK,EAAC,cAAc;oBAAA+F,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD9N,OAAA;oBAAQ0H,KAAK,EAAC,cAAc;oBAAA+F,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD9N,OAAA;oBAAQ0H,KAAK,EAAC,aAAa;oBAAA+F,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,oBAAgB,eAAA9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,eAC7CzN,OAAA;gBAAKwN,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACxDzN,OAAA;kBAAAyN,QAAA,gBACEzN,OAAA;oBAAAyN,QAAA,EAAM;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvB9N,OAAA;oBAAMwN,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAEhL,UAAU,CAACQ,UAAU,CAAC4D,MAAM,EAAC,WAAS;kBAAA;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3E9N,OAAA;oBACE4J,IAAI,EAAC,QAAQ;oBACbuE,KAAK,EAAE;sBAAEyB,UAAU,EAAE,MAAM;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,QAAQ,EAAE;oBAAQ,CAAE;oBACrE/B,OAAO,EAAEA,CAAA,KAAMrL,aAAa,CAACqN,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE9M,UAAU,EAAE;oBAAG,CAAC,CAAC,CAAE;oBAAAwK,QAAA,EACrE;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACR9N,OAAA;kBAAKwN,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eACxCzN,OAAA;oBAAOmO,KAAK,EAAE;sBAAEY,KAAK,EAAE,SAAS;sBAAEiB,SAAS,EAAE;oBAAS,CAAE;oBAAAvC,QAAA,EAAC;kBAEzD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN9N,OAAA;kBAAKwN,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,EAC1CtJ,kBAAkB,CAACQ,GAAG,CAAC,CAAC6G,KAAK,EAAEyE,KAAK,kBACfjQ,OAAA;oBAEEwN,SAAS,EAAE,qBAAqB/K,UAAU,CAACQ,UAAU,CAACsF,QAAQ,CAACiD,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;oBAAAiC,QAAA,gBAEpHzN,OAAA;sBACE4J,IAAI,EAAC,UAAU;sBACf3E,EAAE,EAAE,YAAYgL,KAAK,EAAG;sBACxBpG,OAAO,EAAEpH,UAAU,CAACQ,UAAU,CAACsF,QAAQ,CAACiD,KAAK,CAAE;sBAC/C0C,QAAQ,EAAE1G,CAAC,IAAI;wBACb,MAAMqC,OAAO,GAAGrC,CAAC,CAACC,MAAM,CAACoC,OAAO;wBAChCnH,aAAa,CAACqN,IAAI,IAAI;0BACpB,MAAMG,GAAG,GAAG,IAAIC,GAAG,CAACJ,IAAI,CAAC9M,UAAU,CAAC;0BACpC,IAAI4G,OAAO,EAAE;4BACXqG,GAAG,CAACE,GAAG,CAAC5E,KAAK,CAAC;0BAChB,CAAC,MAAM;4BACL0E,GAAG,CAACxJ,MAAM,CAAC8E,KAAK,CAAC;0BACnB;0BACA,OAAO;4BACL,GAAGuE,IAAI;4BACP9M,UAAU,EAAE2F,KAAK,CAACyH,IAAI,CAACH,GAAG;0BAC5B,CAAC;wBACH,CAAC,CAAC;sBACJ;oBAAE;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACuB,CAAC,eACF9N,OAAA;sBAAO8O,OAAO,EAAE,YAAYmB,KAAK,EAAG;sBAAAxC,QAAA,EACjCjC;oBAAK;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA,GAzBHmC,KAAK;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA0BP,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBzN,OAAA;gBAAKwN,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpCzN,OAAA;kBAAO8O,OAAO,EAAC,iBAAiB;kBAAArB,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxD9N,OAAA;kBACEiF,EAAE,EAAC,iBAAiB;kBACpB0E,IAAI,EAAC,iBAAiB;kBACtB2G,IAAI,EAAC,GAAG;kBACR5I,KAAK,EAAEjF,UAAU,CAACa,eAAgB;kBAClC4K,QAAQ,EAAExE,gBAAiB;kBAC3BsF,QAAQ;kBACRf,WAAW,EAAC;gBAAgC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACZ9N,OAAA;kBAAOwN,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAElC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9N,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzN,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAAyN,QAAA,EAAO;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7B9N,OAAA;kBAAKwN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpCzN,OAAA;oBACE4J,IAAI,EAAC,MAAM;oBACX3E,EAAE,EAAC,gBAAgB;oBACnBiJ,QAAQ,EAAG1G,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,gBAAgB,CAAE;oBACxD4H,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACF9N,OAAA;oBAAO8O,OAAO,EAAC,gBAAgB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC3DzN,OAAA,CAAClB,eAAe;sBAAC4O,IAAI,EAAEjO;oBAAQ;sBAAAkO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClC9N,OAAA;sBAAAyN,QAAA,EAAM;oBAAY;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLjK,eAAe,iBACd7D,OAAA;kBAAKwN,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BzN,OAAA;oBAAKiP,GAAG,EAAEpL,eAAgB;oBAACqL,GAAG,EAAC;kBAAU;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN9N,OAAA;gBAAKwN,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzN,OAAA;kBAAAyN,QAAA,EAAO;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjC9N,OAAA;kBAAKwN,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBACpCzN,OAAA;oBACE4J,IAAI,EAAC,MAAM;oBACX3E,EAAE,EAAC,oBAAoB;oBACvBiJ,QAAQ,EAAG1G,CAAC,IAAKsC,iBAAiB,CAACtC,CAAC,EAAE,oBAAoB,CAAE;oBAC5D4H,MAAM,EAAC,SAAS;oBAChB5B,SAAS,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACF9N,OAAA;oBAAO8O,OAAO,EAAC,oBAAoB;oBAACtB,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC/DzN,OAAA,CAAClB,eAAe;sBAAC4O,IAAI,EAAEjO;oBAAQ;sBAAAkO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClC9N,OAAA;sBAAAyN,QAAA,EAAM;oBAAgB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACL/J,gBAAgB,iBACf/D,OAAA;kBAAKwN,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChCzN,OAAA;oBAAKiP,GAAG,EAAElL,gBAAiB;oBAACmL,GAAG,EAAC;kBAAW;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAIN9N,OAAA;cAAKwN,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BzN,OAAA;gBAAQ4J,IAAI,EAAC,QAAQ;gBAAC4D,SAAS,EAAC,eAAe;gBAACO,OAAO,EAAEzE,UAAW;gBAAAmE,QAAA,EAAC;cAErE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9N,OAAA;gBAAQ4J,IAAI,EAAC,QAAQ;gBAAC4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC5C3M,WAAW,GAAG,YAAY,GAAG;cAAY;gBAAA6M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzN,EAAA,CAl9CID,SAAS;AAAAmQ,EAAA,GAATnQ,SAAS;AAo9Cf,eAAeA,SAAS;AAAC,IAAAmQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}