[{"D:\\Thirmaa Office\\job_page\\client\\src\\index.js": "1", "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js": "2", "D:\\Thirmaa Office\\job_page\\client\\src\\App.js": "3", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx": "4", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx": "5", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx": "6", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx": "7", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx": "8", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx": "9", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx": "10", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx": "11", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx": "12", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx": "13", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx": "14", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx": "15", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx": "16", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx": "17", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx": "18", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx": "19", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx": "20", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx": "21", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx": "22", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx": "23", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx": "24", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx": "25", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx": "26", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx": "27", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx": "28", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx": "29", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx": "30", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx": "31", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx": "32", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js": "33", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx": "34", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js": "35", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js": "36", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx": "37", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx": "38", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx": "39", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx": "40", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx": "41", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx": "42", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NotFound.jsx": "43"}, {"size": 653, "mtime": 1749805078395, "results": "44", "hashOfConfig": "45"}, {"size": 375, "mtime": 1748403601275, "results": "46", "hashOfConfig": "45"}, {"size": 6489, "mtime": 1750046615292, "results": "47", "hashOfConfig": "45"}, {"size": 2193, "mtime": 1749466327689, "results": "48", "hashOfConfig": "45"}, {"size": 2844, "mtime": 1749466327693, "results": "49", "hashOfConfig": "45"}, {"size": 2491, "mtime": 1748403601256, "results": "50", "hashOfConfig": "45"}, {"size": 14414, "mtime": 1750045454135, "results": "51", "hashOfConfig": "45"}, {"size": 6247, "mtime": 1748403601256, "results": "52", "hashOfConfig": "45"}, {"size": 299, "mtime": 1748403601256, "results": "53", "hashOfConfig": "45"}, {"size": 9342, "mtime": 1749805078372, "results": "54", "hashOfConfig": "45"}, {"size": 21399, "mtime": 1750045454135, "results": "55", "hashOfConfig": "45"}, {"size": 3127, "mtime": 1750045454122, "results": "56", "hashOfConfig": "45"}, {"size": 38592, "mtime": 1750046347708, "results": "57", "hashOfConfig": "45"}, {"size": 6876, "mtime": 1750045454122, "results": "58", "hashOfConfig": "45"}, {"size": 8819, "mtime": 1749805078374, "results": "59", "hashOfConfig": "45"}, {"size": 30172, "mtime": 1750152545988, "results": "60", "hashOfConfig": "45"}, {"size": 3888, "mtime": 1750045454135, "results": "61", "hashOfConfig": "45"}, {"size": 11155, "mtime": 1750045454122, "results": "62", "hashOfConfig": "45"}, {"size": 19481, "mtime": 1750045454122, "results": "63", "hashOfConfig": "45"}, {"size": 13224, "mtime": 1750045454135, "results": "64", "hashOfConfig": "45"}, {"size": 12658, "mtime": 1750045454119, "results": "65", "hashOfConfig": "45"}, {"size": 3837, "mtime": 1750045454130, "results": "66", "hashOfConfig": "45"}, {"size": 9842, "mtime": 1750045454130, "results": "67", "hashOfConfig": "45"}, {"size": 2575, "mtime": 1749805078383, "results": "68", "hashOfConfig": "45"}, {"size": 23312, "mtime": 1750045454122, "results": "69", "hashOfConfig": "45"}, {"size": 13942, "mtime": 1750045454122, "results": "70", "hashOfConfig": "45"}, {"size": 17835, "mtime": 1750045454122, "results": "71", "hashOfConfig": "45"}, {"size": 59444, "mtime": 1750045454122, "results": "72", "hashOfConfig": "45"}, {"size": 17268, "mtime": 1750045454122, "results": "73", "hashOfConfig": "45"}, {"size": 7051, "mtime": 1750045454122, "results": "74", "hashOfConfig": "45"}, {"size": 23258, "mtime": 1750045454126, "results": "75", "hashOfConfig": "45"}, {"size": 13722, "mtime": 1750045454130, "results": "76", "hashOfConfig": "45"}, {"size": 1193, "mtime": 1749466288746, "results": "77", "hashOfConfig": "45"}, {"size": 2260, "mtime": 1749466327694, "results": "78", "hashOfConfig": "45"}, {"size": 2070, "mtime": 1750045454166, "results": "79", "hashOfConfig": "45"}, {"size": 3256, "mtime": 1750045454168, "results": "80", "hashOfConfig": "45"}, {"size": 22346, "mtime": 1750045454121, "results": "81", "hashOfConfig": "45"}, {"size": 1015, "mtime": 1750045454135, "results": "82", "hashOfConfig": "45"}, {"size": 3794, "mtime": 1749805078380, "results": "83", "hashOfConfig": "45"}, {"size": 3783, "mtime": 1749805078384, "results": "84", "hashOfConfig": "45"}, {"size": 4156, "mtime": 1749805078384, "results": "85", "hashOfConfig": "45"}, {"size": 581, "mtime": 1750044872255, "results": "86", "hashOfConfig": "45"}, {"size": 1808, "mtime": 1750152545988, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qmit6d", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Thirmaa Office\\job_page\\client\\src\\index.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\App.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx", ["217", "218", "219", "220"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx", ["221"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx", [], ["222", "223", "224", "225", "226", "227", "228", "229", "230", "231"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx", ["232", "233"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx", ["234", "235", "236"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx", ["237", "238"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx", ["239", "240", "241", "242", "243", "244", "245", "246", "247"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx", ["248"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx", [], ["249", "250", "251", "252"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx", ["253", "254", "255", "256", "257", "258", "259", "260"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx", ["261", "262", "263", "264", "265"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx", [], ["266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277", "278", "279", "280"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx", ["281", "282", "283", "284", "285", "286"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx", [], ["287", "288", "289"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx", [], ["290", "291", "292", "293", "294", "295"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx", [], ["296", "297", "298", "299", "300"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx", [], ["301"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx", ["302", "303"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx", ["304", "305", "306", "307"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx", [], ["308", "309", "310", "311"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NotFound.jsx", ["312", "313"], [], {"ruleId": "314", "severity": 1, "message": "315", "line": 4, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 20}, {"ruleId": "314", "severity": 1, "message": "318", "line": 4, "column": 22, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 30}, {"ruleId": "314", "severity": 1, "message": "319", "line": 4, "column": 32, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 43}, {"ruleId": "314", "severity": 1, "message": "320", "line": 4, "column": 45, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 55}, {"ruleId": "314", "severity": 1, "message": "321", "line": 71, "column": 12, "nodeType": "316", "messageId": "317", "endLine": 71, "endColumn": 22}, {"ruleId": "322", "severity": 1, "message": "323", "line": 19, "column": 11, "nodeType": "324", "endLine": 19, "endColumn": 23, "suppressions": "325"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 19, "column": 34, "nodeType": "324", "endLine": 19, "endColumn": 46, "suppressions": "326"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 30, "column": 15, "nodeType": "324", "endLine": 30, "endColumn": 27, "suppressions": "327"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 30, "column": 38, "nodeType": "324", "endLine": 30, "endColumn": 50, "suppressions": "328"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 126, "column": 15, "nodeType": "324", "endLine": 126, "endColumn": 60, "suppressions": "329"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 130, "column": 15, "nodeType": "324", "endLine": 130, "endColumn": 59, "suppressions": "330"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 134, "column": 15, "nodeType": "324", "endLine": 134, "endColumn": 59, "suppressions": "331"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 138, "column": 15, "nodeType": "324", "endLine": 138, "endColumn": 59, "suppressions": "332"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 141, "column": 15, "nodeType": "324", "endLine": 141, "endColumn": 60, "suppressions": "333"}, {"ruleId": "322", "severity": 1, "message": "323", "line": 144, "column": 15, "nodeType": "324", "endLine": 144, "endColumn": 61, "suppressions": "334"}, {"ruleId": "314", "severity": 1, "message": "335", "line": 13, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 13, "endColumn": 13}, {"ruleId": "314", "severity": 1, "message": "336", "line": 14, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 14, "endColumn": 14}, {"ruleId": "314", "severity": 1, "message": "337", "line": 14, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 14, "endColumn": 9}, {"ruleId": "314", "severity": 1, "message": "338", "line": 26, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 26, "endColumn": 17}, {"ruleId": "339", "severity": 1, "message": "340", "line": 159, "column": 6, "nodeType": "341", "endLine": 159, "endColumn": 41, "suggestions": "342"}, {"ruleId": "314", "severity": 1, "message": "343", "line": 4, "column": 45, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 51}, {"ruleId": "314", "severity": 1, "message": "344", "line": 20, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 20, "endColumn": 20}, {"ruleId": "314", "severity": 1, "message": "345", "line": 5, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 24}, {"ruleId": "314", "severity": 1, "message": "346", "line": 5, "column": 35, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 40}, {"ruleId": "314", "severity": 1, "message": "335", "line": 5, "column": 105, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 115}, {"ruleId": "314", "severity": 1, "message": "347", "line": 5, "column": 117, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 124}, {"ruleId": "314", "severity": 1, "message": "348", "line": 5, "column": 126, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 132}, {"ruleId": "314", "severity": 1, "message": "349", "line": 8, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 8, "endColumn": 18}, {"ruleId": "314", "severity": 1, "message": "350", "line": 13, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 13, "endColumn": 25}, {"ruleId": "314", "severity": 1, "message": "351", "line": 277, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 277, "endColumn": 19}, {"ruleId": "314", "severity": 1, "message": "352", "line": 339, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 339, "endColumn": 27}, {"ruleId": "314", "severity": 1, "message": "353", "line": 58, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 58, "endColumn": 20}, {"ruleId": "314", "severity": 1, "message": "354", "line": 7, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 7, "endColumn": 17, "suppressions": "355"}, {"ruleId": "314", "severity": 1, "message": "356", "line": 23, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 23, "endColumn": 19, "suppressions": "357"}, {"ruleId": "314", "severity": 1, "message": "358", "line": 33, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 33, "endColumn": 20, "suppressions": "359"}, {"ruleId": "339", "severity": 1, "message": "360", "line": 54, "column": 6, "nodeType": "341", "endLine": 54, "endColumn": 26, "suggestions": "361", "suppressions": "362"}, {"ruleId": "314", "severity": 1, "message": "349", "line": 4, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 18}, {"ruleId": "314", "severity": 1, "message": "363", "line": 5, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 5, "endColumn": 17}, {"ruleId": "314", "severity": 1, "message": "364", "line": 82, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 82, "endColumn": 25}, {"ruleId": "314", "severity": 1, "message": "365", "line": 82, "column": 27, "nodeType": "316", "messageId": "317", "endLine": 82, "endColumn": 45}, {"ruleId": "314", "severity": 1, "message": "366", "line": 84, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 84, "endColumn": 19}, {"ruleId": "314", "severity": 1, "message": "367", "line": 107, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 107, "endColumn": 18}, {"ruleId": "314", "severity": 1, "message": "368", "line": 107, "column": 20, "nodeType": "316", "messageId": "317", "endLine": 107, "endColumn": 31}, {"ruleId": "314", "severity": 1, "message": "369", "line": 109, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 109, "endColumn": 23}, {"ruleId": "314", "severity": 1, "message": "370", "line": 2, "column": 20, "nodeType": "316", "messageId": "317", "endLine": 2, "endColumn": 30}, {"ruleId": "314", "severity": 1, "message": "371", "line": 2, "column": 74, "nodeType": "316", "messageId": "317", "endLine": 2, "endColumn": 93}, {"ruleId": "314", "severity": 1, "message": "354", "line": 6, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 6, "endColumn": 17}, {"ruleId": "314", "severity": 1, "message": "372", "line": 133, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 133, "endColumn": 23}, {"ruleId": "314", "severity": 1, "message": "373", "line": 175, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 175, "endColumn": 22}, {"ruleId": "314", "severity": 1, "message": "374", "line": 7, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 7, "endColumn": 10, "suppressions": "375"}, {"ruleId": "314", "severity": 1, "message": "376", "line": 8, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 8, "endColumn": 13, "suppressions": "377"}, {"ruleId": "314", "severity": 1, "message": "378", "line": 9, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 9, "endColumn": 13, "suppressions": "379"}, {"ruleId": "314", "severity": 1, "message": "380", "line": 10, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 10, "endColumn": 8, "suppressions": "381"}, {"ruleId": "314", "severity": 1, "message": "337", "line": 12, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 12, "endColumn": 9, "suppressions": "382"}, {"ruleId": "314", "severity": 1, "message": "383", "line": 13, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 13, "endColumn": 11, "suppressions": "384"}, {"ruleId": "314", "severity": 1, "message": "385", "line": 111, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 111, "endColumn": 22, "suppressions": "386"}, {"ruleId": "314", "severity": 1, "message": "387", "line": 112, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 112, "endColumn": 21, "suppressions": "388"}, {"ruleId": "314", "severity": 1, "message": "389", "line": 113, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 113, "endColumn": 19, "suppressions": "390"}, {"ruleId": "314", "severity": 1, "message": "391", "line": 113, "column": 21, "nodeType": "316", "messageId": "317", "endLine": 113, "endColumn": 33, "suppressions": "392"}, {"ruleId": "314", "severity": 1, "message": "393", "line": 119, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 119, "endColumn": 26, "suppressions": "394"}, {"ruleId": "314", "severity": 1, "message": "395", "line": 127, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 127, "endColumn": 26, "suppressions": "396"}, {"ruleId": "314", "severity": 1, "message": "397", "line": 139, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 139, "endColumn": 25, "suppressions": "398"}, {"ruleId": "314", "severity": 1, "message": "399", "line": 151, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 151, "endColumn": 21, "suppressions": "400"}, {"ruleId": "314", "severity": 1, "message": "401", "line": 177, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 177, "endColumn": 24, "suppressions": "402"}, {"ruleId": "314", "severity": 1, "message": "403", "line": 8, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 8, "endColumn": 12}, {"ruleId": "314", "severity": 1, "message": "404", "line": 10, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 10, "endColumn": 12}, {"ruleId": "314", "severity": 1, "message": "405", "line": 13, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 13, "endColumn": 13}, {"ruleId": "314", "severity": 1, "message": "406", "line": 16, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 16, "endColumn": 16}, {"ruleId": "314", "severity": 1, "message": "407", "line": 17, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 17, "endColumn": 17}, {"ruleId": "314", "severity": 1, "message": "408", "line": 18, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 18, "endColumn": 15}, {"ruleId": "314", "severity": 1, "message": "409", "line": 10, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 10, "endColumn": 16, "suppressions": "410"}, {"ruleId": "314", "severity": 1, "message": "411", "line": 14, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 14, "endColumn": 16, "suppressions": "412"}, {"ruleId": "314", "severity": 1, "message": "413", "line": 21, "column": 8, "nodeType": "316", "messageId": "317", "endLine": 21, "endColumn": 13, "suppressions": "414"}, {"ruleId": "314", "severity": 1, "message": "415", "line": 9, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 9, "endColumn": 14, "suppressions": "416"}, {"ruleId": "314", "severity": 1, "message": "417", "line": 16, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 16, "endColumn": 13, "suppressions": "418"}, {"ruleId": "314", "severity": 1, "message": "419", "line": 21, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 21, "endColumn": 15, "suppressions": "420"}, {"ruleId": "314", "severity": 1, "message": "421", "line": 38, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 38, "endColumn": 25, "suppressions": "422"}, {"ruleId": "314", "severity": 1, "message": "423", "line": 168, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 168, "endColumn": 24, "suppressions": "424"}, {"ruleId": "314", "severity": 1, "message": "425", "line": 177, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 177, "endColumn": 21, "suppressions": "426"}, {"ruleId": "314", "severity": 1, "message": "427", "line": 4, "column": 38, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 44, "suppressions": "428"}, {"ruleId": "314", "severity": 1, "message": "409", "line": 12, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 12, "endColumn": 16, "suppressions": "429"}, {"ruleId": "339", "severity": 1, "message": "430", "line": 292, "column": 6, "nodeType": "341", "endLine": 292, "endColumn": 19, "suggestions": "431", "suppressions": "432"}, {"ruleId": "314", "severity": 1, "message": "433", "line": 481, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 481, "endColumn": 27, "suppressions": "434"}, {"ruleId": "435", "severity": 1, "message": "436", "line": 549, "column": 25, "nodeType": "437", "messageId": "438", "endLine": 549, "endColumn": 26, "suggestions": "439", "suppressions": "440"}, {"ruleId": "314", "severity": 1, "message": "441", "line": 4, "column": 46, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 54, "suppressions": "442"}, {"ruleId": "314", "severity": 1, "message": "443", "line": 1, "column": 27, "nodeType": "316", "messageId": "317", "endLine": 1, "endColumn": 36}, {"ruleId": "314", "severity": 1, "message": "444", "line": 2, "column": 10, "nodeType": "316", "messageId": "317", "endLine": 2, "endColumn": 14}, {"ruleId": "322", "severity": 1, "message": "323", "line": 330, "column": 17, "nodeType": "324", "endLine": 336, "endColumn": 18}, {"ruleId": "322", "severity": 1, "message": "323", "line": 339, "column": 17, "nodeType": "324", "endLine": 345, "endColumn": 18}, {"ruleId": "322", "severity": 1, "message": "323", "line": 348, "column": 17, "nodeType": "324", "endLine": 354, "endColumn": 18}, {"ruleId": "322", "severity": 1, "message": "323", "line": 357, "column": 17, "nodeType": "324", "endLine": 363, "endColumn": 18}, {"ruleId": "314", "severity": 1, "message": "409", "line": 10, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 10, "endColumn": 16, "suppressions": "445"}, {"ruleId": "314", "severity": 1, "message": "419", "line": 18, "column": 3, "nodeType": "316", "messageId": "317", "endLine": 18, "endColumn": 15, "suppressions": "446"}, {"ruleId": "314", "severity": 1, "message": "425", "line": 111, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 111, "endColumn": 21, "suppressions": "447"}, {"ruleId": "314", "severity": 1, "message": "448", "line": 309, "column": 9, "nodeType": "316", "messageId": "317", "endLine": 309, "endColumn": 18, "suppressions": "449"}, {"ruleId": "314", "severity": 1, "message": "383", "line": 4, "column": 18, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 26}, {"ruleId": "314", "severity": 1, "message": "450", "line": 4, "column": 28, "nodeType": "316", "messageId": "317", "endLine": 4, "endColumn": 39}, "no-unused-vars", "'FaFacebook' is defined but never used.", "Identifier", "unusedVar", "'FaTiktok' is defined but never used.", "'FaInstagram' is defined but never used.", "'FaLinkedin' is defined but never used.", "'hasMounted' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["451"], ["452"], ["453"], ["454"], ["455"], ["456"], ["457"], ["458"], ["459"], ["460"], "'faBookmark' is defined but never used.", "'faShieldAlt' is defined but never used.", "'faBell' is defined but never used.", "'navigate' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'maxSalary', 'minSalary', and 'selectedJobType'. Either include them or remove the dependency array.", "ArrayExpression", ["461"], "'faStar' is defined but never used.", "'refreshData' is assigned a value but never used.", "'faMapMarkerAlt' is defined but never used.", "'faEye' is defined but never used.", "'faShare' is defined but never used.", "'faBars' is defined but never used.", "'NewsLetter' is defined but never used.", "'FeaturedCompanies' is defined but never used.", "'urgentJobs' is assigned a value but never used.", "'JobLoadingSkeleton' is assigned a value but never used.", "'companyLogo' is assigned a value but never used.", "'banneradd' is defined but never used.", ["462"], "'categories' is assigned a value but never used.", ["463"], "'popularTags' is assigned a value but never used.", ["464"], "React Hook useEffect has a missing dependency: 'fetchBlogById'. Either include it or remove the dependency array.", ["465"], ["466"], "'ContactUs' is defined but never used.", "'testimonialPage' is assigned a value but never used.", "'setTestimonialPage' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'blogPage' is assigned a value but never used.", "'setBlogPage' is assigned a value but never used.", "'totalBlogPages' is assigned a value but never used.", "'FaBookmark' is defined but never used.", "'FaExclamationCircle' is defined but never used.", "'getLatestBlogs' is assigned a value but never used.", "'formatExcerpt' is assigned a value but never used.", "'faUsers' is defined but never used.", ["467"], "'faCalendar' is defined but never used.", ["468"], "'faComments' is defined but never used.", ["469"], "'faCog' is defined but never used.", ["470"], ["471"], "'faSearch' is defined but never used.", ["472"], "'imagePreview' is assigned a value but never used.", ["473"], "'logoPreview' is assigned a value but never used.", ["474"], "'activeTab' is assigned a value but never used.", ["475"], "'setActiveTab' is assigned a value but never used.", ["476"], "'handleInputChange' is assigned a value but never used.", ["477"], "'handleImageUpload' is assigned a value but never used.", ["478"], "'handleLogoUpload' is assigned a value but never used.", ["479"], "'handleSubmit' is assigned a value but never used.", ["480"], "'toggleAccordion' is assigned a value but never used.", ["481"], "'faTwitter' is defined but never used.", "'faYoutube' is defined but never used.", "'faFacebook' is defined but never used.", "'faChevronLeft' is defined but never used.", "'faChevronRight' is defined but never used.", "'faShareNodes' is defined but never used.", "'faCheckCircle' is defined but never used.", ["482"], "'faCalendarAlt' is defined but never used.", ["483"], "'axios' is defined but never used.", ["484"], "'faChartLine' is defined but never used.", ["485"], "'faIndustry' is defined but never used.", ["486"], "'faSignOutAlt' is defined but never used.", ["487"], "'recentCompanies' is assigned a value but never used.", ["488"], "'maxCompanyCount' is assigned a value but never used.", ["489"], "'handleLogout' is assigned a value but never used.", ["490"], "'useRef' is defined but never used.", ["491"], ["492"], "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["493"], ["494"], "'handleToggleStatus' is assigned a value but never used.", ["495"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["496", "497"], ["498"], "'faFilter' is defined but never used.", ["499"], "'useEffect' is defined but never used.", "'Link' is defined but never used.", ["500"], ["501"], ["502"], "'canCreate' is assigned a value but never used.", ["503"], "'faBriefcase' is defined but never used.", {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"desc": "506", "fix": "507"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"desc": "508", "fix": "509"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"desc": "510", "fix": "511"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"messageId": "512", "fix": "513", "desc": "514"}, {"messageId": "515", "fix": "516", "desc": "517"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, {"kind": "504", "justification": "505"}, "directive", "", "Update the dependencies array to be: [maxSalary, minSalary, searchParams, selectedJobType, setSelectedLocation]", {"range": "518", "text": "519"}, "Update the dependencies array to be: [location.state, id, fetchBlogById]", {"range": "520", "text": "521"}, "Update the dependencies array to be: [fetchCompanies, isModalOpen]", {"range": "522", "text": "523"}, "removeEscape", {"range": "524", "text": "505"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "525", "text": "526"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [6085, 6120], "[max<PERSON><PERSON><PERSON>, minSalary, searchParams, selectedJobType, setSelectedLocation]", [1801, 1821], "[location.state, id, fetchBlogById]", [10469, 10482], "[fetchCompanies, isModalOpen]", [20099, 20100], [20099, 20099], "\\"]