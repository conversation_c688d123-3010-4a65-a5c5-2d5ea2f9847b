[{"D:\\Thirmaa Office\\job_page\\client\\src\\index.js": "1", "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js": "2", "D:\\Thirmaa Office\\job_page\\client\\src\\App.js": "3", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx": "4", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx": "5", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx": "6", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx": "7", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx": "8", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx": "9", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx": "10", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx": "11", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx": "12", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx": "13", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx": "14", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx": "15", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx": "16", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx": "17", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx": "18", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx": "19", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx": "20", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx": "21", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx": "22", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx": "23", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx": "24", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx": "25", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx": "26", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx": "27", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx": "28", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx": "29", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx": "30", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx": "31", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx": "32", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js": "33", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx": "34", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js": "35", "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js": "36", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx": "37", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx": "38", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx": "39", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx": "40", "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx": "41", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx": "42", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NotFound.jsx": "43", "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\SubTopicsAdmin.jsx": "44"}, {"size": 653, "mtime": 1749805078395, "results": "45", "hashOfConfig": "46"}, {"size": 375, "mtime": 1748403601275, "results": "47", "hashOfConfig": "46"}, {"size": 6489, "mtime": 1750046615292, "results": "48", "hashOfConfig": "46"}, {"size": 2193, "mtime": 1749466327689, "results": "49", "hashOfConfig": "46"}, {"size": 2844, "mtime": 1749466327693, "results": "50", "hashOfConfig": "46"}, {"size": 2491, "mtime": 1748403601256, "results": "51", "hashOfConfig": "46"}, {"size": 14414, "mtime": 1750045454135, "results": "52", "hashOfConfig": "46"}, {"size": 6247, "mtime": 1748403601256, "results": "53", "hashOfConfig": "46"}, {"size": 299, "mtime": 1748403601256, "results": "54", "hashOfConfig": "46"}, {"size": 9342, "mtime": 1749805078372, "results": "55", "hashOfConfig": "46"}, {"size": 21399, "mtime": 1750045454135, "results": "56", "hashOfConfig": "46"}, {"size": 3127, "mtime": 1750045454122, "results": "57", "hashOfConfig": "46"}, {"size": 38592, "mtime": 1750046347708, "results": "58", "hashOfConfig": "46"}, {"size": 6876, "mtime": 1750045454122, "results": "59", "hashOfConfig": "46"}, {"size": 8819, "mtime": 1749805078374, "results": "60", "hashOfConfig": "46"}, {"size": 30172, "mtime": 1750152545988, "results": "61", "hashOfConfig": "46"}, {"size": 3888, "mtime": 1750045454135, "results": "62", "hashOfConfig": "46"}, {"size": 11155, "mtime": 1750045454122, "results": "63", "hashOfConfig": "46"}, {"size": 19481, "mtime": 1750045454122, "results": "64", "hashOfConfig": "46"}, {"size": 13224, "mtime": 1750045454135, "results": "65", "hashOfConfig": "46"}, {"size": 12898, "mtime": 1750152873760, "results": "66", "hashOfConfig": "46"}, {"size": 3837, "mtime": 1750045454130, "results": "67", "hashOfConfig": "46"}, {"size": 9842, "mtime": 1750045454130, "results": "68", "hashOfConfig": "46"}, {"size": 2575, "mtime": 1749805078383, "results": "69", "hashOfConfig": "46"}, {"size": 23312, "mtime": 1750045454122, "results": "70", "hashOfConfig": "46"}, {"size": 13942, "mtime": 1750045454122, "results": "71", "hashOfConfig": "46"}, {"size": 17835, "mtime": 1750045454122, "results": "72", "hashOfConfig": "46"}, {"size": 60008, "mtime": 1750153115229, "results": "73", "hashOfConfig": "46"}, {"size": 17268, "mtime": 1750045454122, "results": "74", "hashOfConfig": "46"}, {"size": 7051, "mtime": 1750045454122, "results": "75", "hashOfConfig": "46"}, {"size": 23258, "mtime": 1750045454126, "results": "76", "hashOfConfig": "46"}, {"size": 13722, "mtime": 1750045454130, "results": "77", "hashOfConfig": "46"}, {"size": 1193, "mtime": 1749466288746, "results": "78", "hashOfConfig": "46"}, {"size": 2260, "mtime": 1749466327694, "results": "79", "hashOfConfig": "46"}, {"size": 2070, "mtime": 1750045454166, "results": "80", "hashOfConfig": "46"}, {"size": 3256, "mtime": 1750045454168, "results": "81", "hashOfConfig": "46"}, {"size": 22346, "mtime": 1750045454121, "results": "82", "hashOfConfig": "46"}, {"size": 1015, "mtime": 1750045454135, "results": "83", "hashOfConfig": "46"}, {"size": 3794, "mtime": 1749805078380, "results": "84", "hashOfConfig": "46"}, {"size": 3783, "mtime": 1749805078384, "results": "85", "hashOfConfig": "46"}, {"size": 4156, "mtime": 1749805078384, "results": "86", "hashOfConfig": "46"}, {"size": 581, "mtime": 1750044872255, "results": "87", "hashOfConfig": "46"}, {"size": 1808, "mtime": 1750152545988, "results": "88", "hashOfConfig": "46"}, {"size": 14397, "mtime": 1750153250588, "results": "89", "hashOfConfig": "46"}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qmit6d", {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Thirmaa Office\\job_page\\client\\src\\index.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\reportWebVitals.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\App.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Footer.jsx", ["222", "223", "224", "225"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Navbar.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NewsLetter.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SearchArea.jsx", ["226"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetailsDis.jsx", [], ["227", "228", "229", "230", "231", "232", "233", "234", "235", "236"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Banner.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ContactUs.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobDetails.jsx", ["237", "238"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AdminLogin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBoard.jsx", ["239", "240", "241"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Companies.jsx", ["242", "243"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FAQ.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\HomePage.jsx", ["244", "245", "246", "247", "248", "249", "250", "251", "252"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobCard.jsx", ["253"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\BlogArticle.jsx", [], ["254", "255", "256", "257"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\AboutUs.jsx", ["258", "259", "260", "261", "262", "263", "264", "265"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\JobBlog.jsx", ["266", "267", "268", "269", "270"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminPanel.jsx", [], ["271", "272", "273", "274", "275", "276", "277", "278", "279", "280", "281", "282", "283", "284", "285"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedCompanies.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FeaturedSection.jsx", ["286", "287", "288", "289", "290", "291"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\SocialMediaFeeds.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\BlogAdmin.jsx", [], ["292", "293", "294"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AnalyticsDashboard.jsx", [], ["295", "296", "297", "298", "299", "300"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CompaniesAdmin.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\JobsAdmin.jsx", [], ["301", "302", "303", "304", "305", "306"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\CvAdmin.jsx", [], ["307"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\CompanyDetails.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Cv.jsx", ["308", "309"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\FullDetails.jsx", ["310", "311", "312", "313"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\LocationContext.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\UrgentJobCard.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\api.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\services\\apiService.js", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\AdminUsers.jsx", [], ["314", "315", "316", "317"], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PrivacyPolicy.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\TermsAndConditions.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\contexts\\JobFilterContext.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\PageHelmet.jsx", [], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\NotFound.jsx", ["318", "319"], [], "D:\\Thirmaa Office\\job_page\\client\\src\\components\\Admin\\SubTopicsAdmin.jsx", [], ["320"], {"ruleId": "321", "severity": 1, "message": "322", "line": 4, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 20}, {"ruleId": "321", "severity": 1, "message": "325", "line": 4, "column": 22, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 30}, {"ruleId": "321", "severity": 1, "message": "326", "line": 4, "column": 32, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 43}, {"ruleId": "321", "severity": 1, "message": "327", "line": 4, "column": 45, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 55}, {"ruleId": "321", "severity": 1, "message": "328", "line": 71, "column": 12, "nodeType": "323", "messageId": "324", "endLine": 71, "endColumn": 22}, {"ruleId": "329", "severity": 1, "message": "330", "line": 19, "column": 11, "nodeType": "331", "endLine": 19, "endColumn": 23, "suppressions": "332"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 19, "column": 34, "nodeType": "331", "endLine": 19, "endColumn": 46, "suppressions": "333"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 30, "column": 15, "nodeType": "331", "endLine": 30, "endColumn": 27, "suppressions": "334"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 30, "column": 38, "nodeType": "331", "endLine": 30, "endColumn": 50, "suppressions": "335"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 126, "column": 15, "nodeType": "331", "endLine": 126, "endColumn": 60, "suppressions": "336"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 130, "column": 15, "nodeType": "331", "endLine": 130, "endColumn": 59, "suppressions": "337"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 134, "column": 15, "nodeType": "331", "endLine": 134, "endColumn": 59, "suppressions": "338"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 138, "column": 15, "nodeType": "331", "endLine": 138, "endColumn": 59, "suppressions": "339"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 141, "column": 15, "nodeType": "331", "endLine": 141, "endColumn": 60, "suppressions": "340"}, {"ruleId": "329", "severity": 1, "message": "330", "line": 144, "column": 15, "nodeType": "331", "endLine": 144, "endColumn": 61, "suppressions": "341"}, {"ruleId": "321", "severity": 1, "message": "342", "line": 13, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 13, "endColumn": 13}, {"ruleId": "321", "severity": 1, "message": "343", "line": 14, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 14}, {"ruleId": "321", "severity": 1, "message": "344", "line": 14, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 9}, {"ruleId": "321", "severity": 1, "message": "345", "line": 26, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 26, "endColumn": 17}, {"ruleId": "346", "severity": 1, "message": "347", "line": 159, "column": 6, "nodeType": "348", "endLine": 159, "endColumn": 41, "suggestions": "349"}, {"ruleId": "321", "severity": 1, "message": "350", "line": 4, "column": 45, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 51}, {"ruleId": "321", "severity": 1, "message": "351", "line": 20, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 20, "endColumn": 20}, {"ruleId": "321", "severity": 1, "message": "352", "line": 5, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 24}, {"ruleId": "321", "severity": 1, "message": "353", "line": 5, "column": 35, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 40}, {"ruleId": "321", "severity": 1, "message": "342", "line": 5, "column": 105, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 115}, {"ruleId": "321", "severity": 1, "message": "354", "line": 5, "column": 117, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 124}, {"ruleId": "321", "severity": 1, "message": "355", "line": 5, "column": 126, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 132}, {"ruleId": "321", "severity": 1, "message": "356", "line": 8, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 18}, {"ruleId": "321", "severity": 1, "message": "357", "line": 13, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 13, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "358", "line": 277, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 277, "endColumn": 19}, {"ruleId": "321", "severity": 1, "message": "359", "line": 339, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 339, "endColumn": 27}, {"ruleId": "321", "severity": 1, "message": "360", "line": 58, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 58, "endColumn": 20}, {"ruleId": "321", "severity": 1, "message": "361", "line": 7, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 17, "suppressions": "362"}, {"ruleId": "321", "severity": 1, "message": "363", "line": 23, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 23, "endColumn": 19, "suppressions": "364"}, {"ruleId": "321", "severity": 1, "message": "365", "line": 33, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 33, "endColumn": 20, "suppressions": "366"}, {"ruleId": "346", "severity": 1, "message": "367", "line": 54, "column": 6, "nodeType": "348", "endLine": 54, "endColumn": 26, "suggestions": "368", "suppressions": "369"}, {"ruleId": "321", "severity": 1, "message": "356", "line": 4, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 18}, {"ruleId": "321", "severity": 1, "message": "370", "line": 5, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 5, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "371", "line": 82, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 82, "endColumn": 25}, {"ruleId": "321", "severity": 1, "message": "372", "line": 82, "column": 27, "nodeType": "323", "messageId": "324", "endLine": 82, "endColumn": 45}, {"ruleId": "321", "severity": 1, "message": "373", "line": 84, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 84, "endColumn": 19}, {"ruleId": "321", "severity": 1, "message": "374", "line": 107, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 107, "endColumn": 18}, {"ruleId": "321", "severity": 1, "message": "375", "line": 107, "column": 20, "nodeType": "323", "messageId": "324", "endLine": 107, "endColumn": 31}, {"ruleId": "321", "severity": 1, "message": "376", "line": 109, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 109, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "377", "line": 2, "column": 20, "nodeType": "323", "messageId": "324", "endLine": 2, "endColumn": 30}, {"ruleId": "321", "severity": 1, "message": "378", "line": 2, "column": 74, "nodeType": "323", "messageId": "324", "endLine": 2, "endColumn": 93}, {"ruleId": "321", "severity": 1, "message": "361", "line": 6, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 6, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "379", "line": 133, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 133, "endColumn": 23}, {"ruleId": "321", "severity": 1, "message": "380", "line": 175, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 175, "endColumn": 22}, {"ruleId": "321", "severity": 1, "message": "381", "line": 7, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 7, "endColumn": 10, "suppressions": "382"}, {"ruleId": "321", "severity": 1, "message": "383", "line": 8, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 13, "suppressions": "384"}, {"ruleId": "321", "severity": 1, "message": "385", "line": 9, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 9, "endColumn": 13, "suppressions": "386"}, {"ruleId": "321", "severity": 1, "message": "387", "line": 10, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 8, "suppressions": "388"}, {"ruleId": "321", "severity": 1, "message": "344", "line": 12, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 12, "endColumn": 9, "suppressions": "389"}, {"ruleId": "321", "severity": 1, "message": "390", "line": 13, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 13, "endColumn": 11, "suppressions": "391"}, {"ruleId": "321", "severity": 1, "message": "392", "line": 113, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 113, "endColumn": 22, "suppressions": "393"}, {"ruleId": "321", "severity": 1, "message": "394", "line": 114, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 114, "endColumn": 21, "suppressions": "395"}, {"ruleId": "321", "severity": 1, "message": "396", "line": 115, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 115, "endColumn": 19, "suppressions": "397"}, {"ruleId": "321", "severity": 1, "message": "398", "line": 115, "column": 21, "nodeType": "323", "messageId": "324", "endLine": 115, "endColumn": 33, "suppressions": "399"}, {"ruleId": "321", "severity": 1, "message": "400", "line": 121, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 121, "endColumn": 26, "suppressions": "401"}, {"ruleId": "321", "severity": 1, "message": "402", "line": 129, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 129, "endColumn": 26, "suppressions": "403"}, {"ruleId": "321", "severity": 1, "message": "404", "line": 141, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 141, "endColumn": 25, "suppressions": "405"}, {"ruleId": "321", "severity": 1, "message": "406", "line": 153, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 153, "endColumn": 21, "suppressions": "407"}, {"ruleId": "321", "severity": 1, "message": "408", "line": 179, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 179, "endColumn": 24, "suppressions": "409"}, {"ruleId": "321", "severity": 1, "message": "410", "line": 8, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 8, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "411", "line": 10, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 12}, {"ruleId": "321", "severity": 1, "message": "412", "line": 13, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 13, "endColumn": 13}, {"ruleId": "321", "severity": 1, "message": "413", "line": 16, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 16, "endColumn": 16}, {"ruleId": "321", "severity": 1, "message": "414", "line": 17, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 17, "endColumn": 17}, {"ruleId": "321", "severity": 1, "message": "415", "line": 18, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 18, "endColumn": 15}, {"ruleId": "321", "severity": 1, "message": "416", "line": 10, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 16, "suppressions": "417"}, {"ruleId": "321", "severity": 1, "message": "418", "line": 14, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 14, "endColumn": 16, "suppressions": "419"}, {"ruleId": "321", "severity": 1, "message": "420", "line": 21, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 21, "endColumn": 13, "suppressions": "421"}, {"ruleId": "321", "severity": 1, "message": "422", "line": 9, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 9, "endColumn": 14, "suppressions": "423"}, {"ruleId": "321", "severity": 1, "message": "424", "line": 16, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 16, "endColumn": 13, "suppressions": "425"}, {"ruleId": "321", "severity": 1, "message": "426", "line": 21, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 21, "endColumn": 15, "suppressions": "427"}, {"ruleId": "321", "severity": 1, "message": "428", "line": 38, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 38, "endColumn": 25, "suppressions": "429"}, {"ruleId": "321", "severity": 1, "message": "430", "line": 168, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 168, "endColumn": 24, "suppressions": "431"}, {"ruleId": "321", "severity": 1, "message": "432", "line": 177, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 177, "endColumn": 21, "suppressions": "433"}, {"ruleId": "321", "severity": 1, "message": "434", "line": 4, "column": 38, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 44, "suppressions": "435"}, {"ruleId": "321", "severity": 1, "message": "416", "line": 12, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 12, "endColumn": 16, "suppressions": "436"}, {"ruleId": "346", "severity": 1, "message": "437", "line": 128, "column": 6, "nodeType": "348", "endLine": 128, "endColumn": 8, "suggestions": "438", "suppressions": "439"}, {"ruleId": "346", "severity": 1, "message": "440", "line": 302, "column": 6, "nodeType": "348", "endLine": 302, "endColumn": 19, "suggestions": "441", "suppressions": "442"}, {"ruleId": "321", "severity": 1, "message": "443", "line": 491, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 491, "endColumn": 27, "suppressions": "444"}, {"ruleId": "445", "severity": 1, "message": "446", "line": 559, "column": 25, "nodeType": "447", "messageId": "448", "endLine": 559, "endColumn": 26, "suggestions": "449", "suppressions": "450"}, {"ruleId": "321", "severity": 1, "message": "451", "line": 4, "column": 46, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 54, "suppressions": "452"}, {"ruleId": "321", "severity": 1, "message": "453", "line": 1, "column": 27, "nodeType": "323", "messageId": "324", "endLine": 1, "endColumn": 36}, {"ruleId": "321", "severity": 1, "message": "454", "line": 2, "column": 10, "nodeType": "323", "messageId": "324", "endLine": 2, "endColumn": 14}, {"ruleId": "329", "severity": 1, "message": "330", "line": 330, "column": 17, "nodeType": "331", "endLine": 336, "endColumn": 18}, {"ruleId": "329", "severity": 1, "message": "330", "line": 339, "column": 17, "nodeType": "331", "endLine": 345, "endColumn": 18}, {"ruleId": "329", "severity": 1, "message": "330", "line": 348, "column": 17, "nodeType": "331", "endLine": 354, "endColumn": 18}, {"ruleId": "329", "severity": 1, "message": "330", "line": 357, "column": 17, "nodeType": "331", "endLine": 363, "endColumn": 18}, {"ruleId": "321", "severity": 1, "message": "416", "line": 10, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 10, "endColumn": 16, "suppressions": "455"}, {"ruleId": "321", "severity": 1, "message": "426", "line": 18, "column": 3, "nodeType": "323", "messageId": "324", "endLine": 18, "endColumn": 15, "suppressions": "456"}, {"ruleId": "321", "severity": 1, "message": "432", "line": 111, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 111, "endColumn": 21, "suppressions": "457"}, {"ruleId": "321", "severity": 1, "message": "458", "line": 309, "column": 9, "nodeType": "323", "messageId": "324", "endLine": 309, "endColumn": 18, "suppressions": "459"}, {"ruleId": "321", "severity": 1, "message": "390", "line": 4, "column": 18, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 26}, {"ruleId": "321", "severity": 1, "message": "460", "line": 4, "column": 28, "nodeType": "323", "messageId": "324", "endLine": 4, "endColumn": 39}, {"ruleId": "321", "severity": 1, "message": "461", "line": 20, "column": 8, "nodeType": "323", "messageId": "324", "endLine": 20, "endColumn": 18, "suppressions": "462"}, "no-unused-vars", "'FaFacebook' is defined but never used.", "Identifier", "unusedVar", "'FaTiktok' is defined but never used.", "'FaInstagram' is defined but never used.", "'FaLinkedin' is defined but never used.", "'hasMounted' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["463"], ["464"], ["465"], ["466"], ["467"], ["468"], ["469"], ["470"], ["471"], ["472"], "'faBookmark' is defined but never used.", "'faShieldAlt' is defined but never used.", "'faBell' is defined but never used.", "'navigate' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'maxSalary', 'minSalary', and 'selectedJobType'. Either include them or remove the dependency array.", "ArrayExpression", ["473"], "'faStar' is defined but never used.", "'refreshData' is assigned a value but never used.", "'faMapMarkerAlt' is defined but never used.", "'faEye' is defined but never used.", "'faShare' is defined but never used.", "'faBars' is defined but never used.", "'NewsLetter' is defined but never used.", "'FeaturedCompanies' is defined but never used.", "'urgentJobs' is assigned a value but never used.", "'JobLoadingSkeleton' is assigned a value but never used.", "'companyLogo' is assigned a value but never used.", "'banneradd' is defined but never used.", ["474"], "'categories' is assigned a value but never used.", ["475"], "'popularTags' is assigned a value but never used.", ["476"], "React Hook useEffect has a missing dependency: 'fetchBlogById'. Either include it or remove the dependency array.", ["477"], ["478"], "'ContactUs' is defined but never used.", "'testimonialPage' is assigned a value but never used.", "'setTestimonialPage' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'blogPage' is assigned a value but never used.", "'setBlogPage' is assigned a value but never used.", "'totalBlogPages' is assigned a value but never used.", "'FaBookmark' is defined but never used.", "'FaExclamationCircle' is defined but never used.", "'getLatestBlogs' is assigned a value but never used.", "'formatExcerpt' is assigned a value but never used.", "'faUsers' is defined but never used.", ["479"], "'faCalendar' is defined but never used.", ["480"], "'faComments' is defined but never used.", ["481"], "'faCog' is defined but never used.", ["482"], ["483"], "'faSearch' is defined but never used.", ["484"], "'imagePreview' is assigned a value but never used.", ["485"], "'logoPreview' is assigned a value but never used.", ["486"], "'activeTab' is assigned a value but never used.", ["487"], "'setActiveTab' is assigned a value but never used.", ["488"], "'handleInputChange' is assigned a value but never used.", ["489"], "'handleImageUpload' is assigned a value but never used.", ["490"], "'handleLogoUpload' is assigned a value but never used.", ["491"], "'handleSubmit' is assigned a value but never used.", ["492"], "'toggleAccordion' is assigned a value but never used.", ["493"], "'faTwitter' is defined but never used.", "'faYoutube' is defined but never used.", "'faFacebook' is defined but never used.", "'faChevronLeft' is defined but never used.", "'faChevronRight' is defined but never used.", "'faShareNodes' is defined but never used.", "'faCheckCircle' is defined but never used.", ["494"], "'faCalendarAlt' is defined but never used.", ["495"], "'axios' is defined but never used.", ["496"], "'faChartLine' is defined but never used.", ["497"], "'faIndustry' is defined but never used.", ["498"], "'faSignOutAlt' is defined but never used.", ["499"], "'recentCompanies' is assigned a value but never used.", ["500"], "'maxCompanyCount' is assigned a value but never used.", ["501"], "'handleLogout' is assigned a value but never used.", ["502"], "'useRef' is defined but never used.", ["503"], ["504"], "React Hook useEffect has a missing dependency: 'defaultSubTopics'. Either include it or remove the dependency array.", ["505"], ["506"], "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["507"], ["508"], "'handleToggleStatus' is assigned a value but never used.", ["509"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["510", "511"], ["512"], "'faFilter' is defined but never used.", ["513"], "'useEffect' is defined but never used.", "'Link' is defined but never used.", ["514"], ["515"], ["516"], "'canCreate' is assigned a value but never used.", ["517"], "'faBriefcase' is defined but never used.", "'ApiService' is defined but never used.", ["518"], {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"desc": "521", "fix": "522"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"desc": "523", "fix": "524"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"desc": "525", "fix": "526"}, {"kind": "519", "justification": "520"}, {"desc": "527", "fix": "528"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"messageId": "529", "fix": "530", "desc": "531"}, {"messageId": "532", "fix": "533", "desc": "534"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, {"kind": "519", "justification": "520"}, "directive", "", "Update the dependencies array to be: [maxSalary, minSalary, searchParams, selectedJobType, setSelectedLocation]", {"range": "535", "text": "536"}, "Update the dependencies array to be: [location.state, id, fetchBlogById]", {"range": "537", "text": "538"}, "Update the dependencies array to be: [defaultSubTopics]", {"range": "539", "text": "540"}, "Update the dependencies array to be: [fetchCompanies, isModalOpen]", {"range": "541", "text": "542"}, "removeEscape", {"range": "543", "text": "520"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "544", "text": "545"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [6085, 6120], "[max<PERSON><PERSON><PERSON>, minSalary, searchParams, selectedJobType, setSelectedLocation]", [1801, 1821], "[location.state, id, fetchBlogById]", [4188, 4190], "[defaultSubTopics]", [10812, 10825], "[fetchCompanies, isModalOpen]", [20442, 20443], [20442, 20442], "\\"]